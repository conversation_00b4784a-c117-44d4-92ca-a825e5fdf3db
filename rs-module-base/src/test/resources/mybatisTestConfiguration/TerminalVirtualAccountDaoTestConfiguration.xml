<?xml version="1.0" encoding="UTF-8" ?>
<!-- Mybatis config sample -->
<!DOCTYPE configuration
    PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>
        <typeAliases>
        <package name="com.bsp.dao"/>
        <package name="com.rs.dao"/>
        <package name="com.gosun.zhjg.dao"/>
        <package name="${rs.info.base-package}.dao"/>
    </typeAliases>


    <environments default = "default">
        <environment id="default">
            <transactionManager type="JDBC"/>
            <dataSource type="UNPOOLED">
                <property name = "driver" value = "org.postgresql.Driver"/>
                <property name="url" value="******************************************"/>
                <property name="username" value="postgres"/>
                <property name="password" value="Go@123456"/>
            </dataSource>
        </environment>
    </environments>



    <mappers>
        <mapper resource="mapper/terminal/TerminalVirtualAccountMapper.xml"/>
    </mappers>
</configuration>
