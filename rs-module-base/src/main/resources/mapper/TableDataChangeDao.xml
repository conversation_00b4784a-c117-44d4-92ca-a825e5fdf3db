<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.rs.module.base.dao.pm.TableDataChangeDao">

    <select id="queryDateChange" resultType="com.rs.module.base.entity.pm.TableDataChangeDO">
        SELECT id, is_del, add_time, table_name, update_type, pk_id, old_json_data FROM pam_pm_table_data_change
        where is_del = 0
        <if test="tableName != null and tableName != ''">
            and table_name = #{tableName}
        </if>
        <if test="addTimeGreatThan != null">
            and add_time &gt;= #{addTimeGreatThan}
        </if>
        order by add_time
        limit ${limit}
    </select>

</mapper>
