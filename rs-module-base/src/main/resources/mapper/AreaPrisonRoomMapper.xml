<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.pm.AreaPrisonRoomDao">


    <delete id="deleteAreaPrisonRoomByRoomCode">
        update acp_pm_area_prison_room set is_del = 1 where room_code = #{id}
    </delete>
    <select id="selectListByRoomCode" resultType="com.rs.module.base.entity.pm.AreaPrisonRoomDO">
        select * from acp_pm_area_prison_room where room_code = #{roomCode}
    </select>
    <select id="getNotCaseRoomInfo" resultType="java.lang.String">
        SELECT p.jsh, p.tabh,count(1) from vw_acp_pm_prisoner_in p, (
            SELECT jsh  from vw_acp_pm_prisoner_in t WHERE t.is_del = 0 GROUP BY jsh HAVING count(1) &gt; 1
        ) t where   p.is_del = 0 and p.jsh=t.jsh and p.tabh is not null
            <if test="orgCode != null and orgCode = '' ">
              and  p.org_code = #{orgCode}
            </if>
        GROUP BY p.jsh,p.tabh HAVING  count(1) &gt; 1
    </select>



    <select id="getHistoryRoomList" resultType="java.lang.String">
            SELECT distinct old_room_id room_id from acp_gj_prison_room_change t WHERE is_del = 0  and is_change =0
        and jgrybm in
        <foreach collection="jgrybmList" item="jgrybm" separator="," open="(" close=")">
            #{jgrybm}
        </foreach>
    </select>

</mapper>
