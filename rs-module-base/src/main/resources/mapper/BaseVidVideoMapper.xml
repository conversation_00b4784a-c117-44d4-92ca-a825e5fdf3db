<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.pm.device.BaseVidVideoDao">


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, device_code, tree_code
    </sql>

    <select id="getTreeCodeByDeviceCode" resultType="java.lang.String">
        select tree_code from acp_pm_vid_video where device_code = #{deviceCode}
    </select>

    <delete id="clearTable">
        DELETE from acp_pm_vid_video where prison_id = #{orgCode};
    </delete>

    <select id="getAllDevice" resultType="java.lang.String">
        select a.id from acp_pm_device a
    </select>

    <update id="updateDeviceStatus">
        update acp_pm_device set device_status = #{status} where id = #{id}
    </update>
</mapper>
