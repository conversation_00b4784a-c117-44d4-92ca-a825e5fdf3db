<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.pm.TerminalVirtualAccountDao">
    <select id="getAvailableAccount" resultType="com.rs.module.acp.controller.admin.pm.vo.TerminalVirtualAccountRespVO">
        SELECT id,
               name,
               sort_order
        FROM acp_pm_terminal_virtual_account atv
        WHERE is_del = 0
          AND not exists (SELECT *
                          FROM acp_pm_terminal_virtual_account_status vas
                          WHERE is_del = 0
                            AND vas.virtual_id = atv.id
                          ORDER BY sort_order desc)
        ORDER BY sort_order desc
        LIMIT 1
    </select>

    <select id="findJgrybmByTalkTaskId" resultType="java.lang.String">
        SELECT jgrybm
        FROM tem_talk_task
        WHERE id = #{talkTaskId}
          AND is_del = 0
    </select>
</mapper>
