<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.sys.BusTraceDao">

	<!-- 获取监管人员轨迹相关的场所 -->
    <select id="getJgryTraceOrgList">
		select t.org_code, t.org_name, p.ajbh, p.sxzm, p.jgrybm, p.id, p.gyqx from acp_sys_bus_trace t
			left join vw_acp_pm_prisoner p on t.jgrybm = p.jgrybm
			where t.bus_type in
			<foreach collection="busTypes" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			<if test="jgrybm != null and jgrybm != ''">
	            and t.jgrybm = #{jgrybm}
	        </if>
	        <if test="zjhm != null and zjhm != ''">
	            and t.zjhm = #{zjhm}
	        </if>
		group by t.org_code, t.org_name, p.ajbh, p.sxzm, p.jgrybm, p.id, p.gyqx
		order by t.org_code
	</select>
</mapper>
