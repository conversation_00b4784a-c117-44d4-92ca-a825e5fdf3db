<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.ihc.dao.ipm.appointment.IhcsInternalMedicalAppointmentDao">
    <sql id="Base_Column_List">
        id,
        appointment_num,
        supervised_user_code,
        sick_type,
        disease_time,
        disease_reason,
        primary_diagnosis,
        disease_level,
        process_method,
        process_userid,
        process_user_name,
        process_time,
        remote_diagnose_status,
        prison_id,
        has_allergy_history,
        CITY_NAME,
        CITY_CODE,
        REG_NAME,
        REG_CODE,
        ORG_NAME,
        ORG_CODE,
        IS_DEL,
        ADD_USER,
        ADD_USER_NAME,
        ADD_TIME,
        UPDATE_USER,
        UPDATE_USER_NAME,
        UPDATE_TIME
    </sql>
    <resultMap id="BaseResultMap" type="com.rs.module.ihc.entity.ipm.appointment.IhcsInternalMedicalAppointmentDO">
        <result column="id" property="id"/>
        <result column="appointment_num" property="appointmentNum"/>
        <result column="supervised_user_code" property="supervisedUserCode"/>
        <result column="sick_type" property="sickType"/>
        <result column="disease_time" property="diseaseTime"/>
        <result column="disease_reason" property="diseaseReason"/>
        <result column="primary_diagnosis" property="primaryDiagnosis"/>
        <result column="disease_level" property="diseaseLevel"/>
        <result column="process_method" property="processMethod"/>
        <result column="process_userid" property="processUserid"/>
        <result column="process_user_name" property="processUserName"/>
        <result column="process_time" property="processTime"/>
        <result column="remote_diagnose_status" property="remoteDiagnoseStatus"/>
        <result column="prison_id" property="prisonId"/>
        <result column="has_allergy_history" property="hasAllergyHistory"/>
        <result column="CITY_NAME" property="cityName"/>
        <result column="CITY_CODE" property="cityCode"/>
        <result column="REG_NAME" property="regName"/>
        <result column="REG_CODE" property="regCode"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="ORG_CODE" property="orgCode"/>
        <result column="IS_DEL" property="isDel"/>
        <result column="ADD_USER" property="addUser"/>
        <result column="ADD_USER_NAME" property="addUserName"/>
        <result column="ADD_TIME" property="addTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
        <result column="UPDATE_USER_NAME" property="updateUserName"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>
    <select id="getMaxAppointmentNumber" resultType="java.lang.String">
        select max(appointment_num) as appointment_num
        from ihc_ipm_internal_medical_appointment
        where disease_time between #{startTime} and #{endTime}
          and is_del = 0
        <if test="prefix != null and prefix != ''">
            and appointment_num like concat(#{prefix}, '%')
        </if>
    </select>

    <select id="getMedicalAppointmentById" resultType="com.rs.module.ihc.controller.admin.ipm.appointment.vo.GetMedicalAppointmentByIdVO">
        select t1.id,
        t1.appointment_num,
        t1.supervised_user_code,
        t1.sick_type,
        t1.disease_time,
        t1.disease_reason,
        t1.primary_diagnosis,
        t1.disease_level,
        t1.process_method,
        t1.process_userid,
        t1.process_user_name,
        t1.process_time,
        t2.xm   as supervised_user_name,
        t2.xb   as sex,
        t2.csrq as birthday,
        t2.jsh  as room_id,
        t2.rssj as entry_time,
        t2.front_photo,
        t3.room_name
        from ihc_ipm_internal_medical_appointment t1
        left join vw_acp_pm_prisoner t2 on t1.supervised_user_code = t2.jgrybm
        left join acp_pm_area_prison_room t3 on t2.jsh = t3.id
        where t1.id = #{id}
    </select>

    <select id="getMedicalAppointmentByJgrybm" resultMap="BaseResultMap">

    </select>
</mapper>
