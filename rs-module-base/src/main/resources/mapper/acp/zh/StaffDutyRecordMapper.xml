<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.zh.StaffDutyRecordDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , temp_id, duty_date, add_user, add_time, update_user, update_time, is_del
    </sql>

    <resultMap id="findDutyMap"
               type="com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageHeaderVO">
        <collection property="subPostList" column="{id=id}" select="findDutyList"/>
    </resultMap>

    <resultMap id="findDutyMap2"
               type="com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageHeaderVO">
        <collection property="subPostList" column="{id=id}" select="findDutyList2"/>
    </resultMap>

    <resultMap id="findDutyTimeMap"
               type="com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageSubPostVO">
        <collection property="subPostTimeVOS" column="{id=id}" select="findDutyTimeList"/>
    </resultMap>

    <resultMap id="findDutyTimeMap2"
               type="com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageSubPostVO">
        <collection property="subPostTimeVOS" column="{id=id}" select="findDutyTimeList2"/>
    </resultMap>

    <select id="findDutyTimeList" resultType="com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageSubPostTimeVO">
        SELECT
               t1.post_key,
               t1.duty_shift,
               t1.begintime || t1.duty_time_begin || '-' || t1.endtime || t1.duty_time_end AS "time"
        from (SELECT id,
                     duty_role_id,
                     duty_shift,
                     duty_time_begin as duty_time_begin,
                     duty_time_end as duty_time_end,
                     duty_time_type_begin,
                     duty_time_type_end,
                     CASE

                         WHEN duty_time_type_begin = 2 THEN
                             '次日'
                         ELSE ''
                         END AS begintime,
                     CASE

                         WHEN duty_time_type_end = 2 THEN
                             '次日'
                         ELSE ''
                         END AS endtime,
                     post_key
              FROM acp_zh_staff_duty_role_time) t1
        where t1.duty_role_id = #{id} order by t1.duty_time_begin asc
    </select>
<!--
to_char(a.duty_time_begin,'HH24:MI') as duty_time_begin,
                     to_char(a.duty_time_end,'HH24:MI') as duty_time_end,
                     -->
    <select id="findDutyTimeList2" resultType="com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageSubPostTimeVO">
        SELECT
            t1.post_key,
            t1.duty_shift,
            t1.begintime || t1.duty_time_begin || '-' || t1.endtime || t1.duty_time_end AS "time",
            t1.police_type,
            t1.police_name,
            t2.photo,
            t2.user_id
        from (SELECT a.id,
                     a.duty_role_id,
                     a.duty_shift,
                     a.duty_time_begin as duty_time_begin,
                     a.duty_time_end as duty_time_end,
                     a.duty_time_type_begin,
                     a.duty_time_type_end,
                     CASE

                         WHEN a.duty_time_type_begin = 2 THEN
                             '次日'
                         ELSE ''
                         END AS begintime,
                     CASE

                         WHEN a.duty_time_type_end = 2 THEN
                             '次日'
                         ELSE ''
                         END AS endtime,
                     a.post_key,
                     b.police_type,
                     b.police_name,
                     b.police_id
              FROM acp_zh_staff_duty_role_time a left join acp_zh_staff_duty_record_person b on a.post_key = b.post_key
              left join acp_zh_staff_duty_record c on b.record_id = c.id
              where c.is_del = '0' and c.duty_date = CURRENT_DATE
              ) t1 left join base_police_info t2 on t1.police_id = t2.id
        where t1.duty_role_id = #{id} order by t1.duty_time_begin asc
    </select>

    <select id="findDutyList" resultMap="findDutyTimeMap">
        SELECT       id,
                     post_id,
                     duty_role_name as subPost,
                     duty_post_id
              FROM acp_zh_staff_duty_role
        where post_id = #{id}
    </select>

    <select id="findDutyList2" resultMap="findDutyTimeMap2">
        SELECT       id,
                     post_id,
                     duty_role_name as subPost,
                     duty_post_id
        FROM acp_zh_staff_duty_role
        where post_id = #{id}
    </select>

    <select id="getHeaderList" resultMap="findDutyMap">
        SELECT id, post_name as post, case when has_sub_post = 0 then false else true end as hasSubPost
        from acp_zh_staff_duty_post
        where temp_id = #{id}
    </select>

    <select id="getIndexHeaderListByNowDay" resultMap="findDutyMap2">
        SELECT DISTINCT ON (A.ID) a.id, a.post_name as post, case when a.has_sub_post = 0 then false else true end as hasSubPost
        from acp_zh_staff_duty_post a left join acp_zh_staff_duty_role b on a.id = b.post_id
        where a.temp_id = #{id}
          <if test="type==1">
              and (b.duty_post_id = '5' or b.duty_post_id = '10' or b.duty_post_id = '7')
          </if>
        <if test="type==0">
            and b.duty_post_id = #{postId}
        </if>
    </select>


    <select id="getTemIdAndDateByDate" resultType="map">
        SELECT DISTINCT ON  ( t1."date" ) t1."date",  t1.temp_id
        FROM  (
            SELECT CURRENT_DATE AS DATE   ,   ID AS temp_id,   org_code   FROM
            acp_zh_staff_duty_template
            WHERE   is_del = '0'    AND status = '1' and type = #{type}  ) t1
    where
        to_char(t1.date, 'yyyy-mm-dd') = #{beginDate}
        and t1.org_code = #{orgCode}

    </select>


</mapper>
