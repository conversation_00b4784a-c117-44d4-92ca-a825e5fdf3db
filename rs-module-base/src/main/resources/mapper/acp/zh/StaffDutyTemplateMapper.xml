<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.zh.StaffDutyTemplateDao">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , name, add_user, ADD_USER_NAME, update_user, update_user_name, add_time, update_time, status,type, is_del, org_code
    </sql>

    <resultMap id="findDutyPostMap"
               type="com.rs.module.base.controller.admin.zh.vo.staffduty.StaffDutyTemplateRespVO">
        <collection property="dutyPostDTOS" column="{id=id}" select="findDutyList"/>
    </resultMap>

    <resultMap id="findDutyRoleMap"
               type="com.rs.module.base.controller.admin.zh.vo.staffduty.StaffDutyPostRespVO">
        <collection property="dutyRoleDTOS" column="{id=id}" select="findDutyRoleList"/>
    </resultMap>

    <resultMap id="findDutyRoleTimeMap"
               type="com.rs.module.base.controller.admin.zh.vo.staffduty.StaffDutyRoleRespVO">
        <collection property="roleTimeDTOS" column="{id=id}" select="findDutyRoleTimeList"/>
    </resultMap>

    <select id="findDutyList" resultMap="findDutyRoleMap">
        SELECT id,
               temp_id,
               post_name,
               has_sub_post
        FROM acp_zh_staff_duty_post
        where temp_id = #{id} and is_del = 0
    </select>

    <select id="findDutyRoleList" resultMap="findDutyRoleTimeMap">
        SELECT id,
               post_id,
               duty_role_name,
               post_name,
               duty_post_id,
               absent_notifier_sfzh,
               absent_notifier_name
        FROM acp_zh_staff_duty_role
               where post_id = #{id} and is_del = 0
    </select>

    <select id="findDutyRoleTimeList" resultType="com.rs.module.base.controller.admin.zh.vo.staffduty.StaffDutyRoleTimeRespVO">
        SELECT id,
               duty_role_id,
               duty_shift,
               duty_time_type_begin,
               duty_time_type_end,
               duty_time_begin,
               duty_time_end,
               post_key
        FROM acp_zh_staff_duty_role_time
        where duty_role_id = #{id} and is_del = 0
    </select>

    <select id="info" resultMap="findDutyPostMap">
        SELECT
        <include refid="Base_Column_List">
        </include>
        ,case when status='1' then '启用'
        else '停用' end as statusDisplayName
        FROM acp_zh_staff_duty_template
        <where>
            and id = #{id} and is_del = 0
        </where>
    </select>

    <select id="checkNameById" resultType="java.lang.Integer">
        select count(1) from acp_zh_staff_duty_template t where name = #{name} and is_del = 0 and id != #{id} and type = #{type}
    </select>

</mapper>
