<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.rs.module.acp.dao.pm.RsBaseDeviceInscreenDao">

    <select id="getInscreenByCondition" resultType="com.rs.module.acp.entity.pm.BaseDeviceInscreenDO">
        select c.* from acp_pm_device a
			inner join acp_pm_device_inscreen c on a.id = c.device_id
		<where>
			a.is_del = 0
			<if test="areaIds != null">
				and a.area_id in
				<foreach collection="areaIds" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="roomId != null and roomId != ''">
				and c.room_id = #{roomId}
			</if>
			<if test="deviceType != null and deviceType != ''">
				and c.device_type = #{deviceType}
			</if>
		</where>
    </select>

</mapper>