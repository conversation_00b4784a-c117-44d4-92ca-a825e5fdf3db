<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.ihc.dao.md.MedicineDeliveryApplyDao">

    <select id="getMedicineDeliveryApplyAppRespVO"
            resultType="com.rs.module.ihc.controller.admin.md.vo.MedicineDeliveryApplyAppRespVO">
        SELECT
            p.jgrybm,
            p.xm as jgryxm,
            p.xb as sex,
            date_part('year'::text, age(now(), p.csrq::timestamp with time zone)) AS age,
            t.*
        FROM ihc_md_medicine_delivery_apply t

                 LEFT JOIN vw_acp_pm_prisoner_list p ON t.jgrybm = p.jgrybm and  p.is_del = 0
        WHERE t.is_del = 0
           and t.org_code = #{pageReqVO.orgCode}
          <if test="pageReqVO.roomId != null and pageReqVO.roomId != '' ">
            and p.jsh = #{pageReqVO.roomId}
          </if>
          <if test="pageReqVO.jgrybm != null and pageReqVO.jgrybm != '' ">
            and p.jgrybm = #{pageReqVO.jgrybm}
          </if>

        <if test="pageReqVO.jgrybm != null and pageReqVO.jgrybm != '' ">
            and p.jgrybm = #{pageReqVO.jgrybm}
        </if>

        <if test="pageReqVO.startTime != null">
            and t.add_time &gt;= #{pageReqVO.startTime}
        </if>
        <if test="pageReqVO.endTime != null">
            and t.add_time &lt; #{pageReqVO.endTime}
        </if>

    </select>
</mapper>
