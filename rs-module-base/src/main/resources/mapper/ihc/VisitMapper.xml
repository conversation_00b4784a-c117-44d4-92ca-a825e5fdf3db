<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.ihc.dao.ipm.VisitDao">
    <sql id="Base_Column_List">
        id,
        "source",
        appointment_id,
        jgrybm,
        rybh,
        ryxm,
        sick_type,
        disease_time,
        disease_reason,
        main_complaint,
        visit_state,
        visit_process_method,
        visit_time,
        visit_userid,
        visit_user_name,
        visit_conclusion,
        prison_id,
        tool_use_advise,
        tool_use_cancel_reason,
        ext_id,
        CITY_NAME,
        CITY_CODE,
        REG_NAME,
        REG_CODE,
        ORG_NAME,
        ORG_CODE,
        IS_DEL,
        ADD_USER,
        ADD_USER_NAME,
        ADD_TIME,
        UPDATE_USER,
        UPDATE_USER_NAME,
        UPDATE_TIME
    </sql>
    <resultMap id="BaseResultMap" type="com.rs.module.ihc.entity.ipm.VisitDO">
        <result column="id" property="id"/>
        <result column="source" property="source"/>
        <result column="appointment_id" property="appointmentId"/>
        <result column="jgrybm" property="jgrybm"/>
        <result column="rybh" property="rybh"/>
        <result column="ryxm" property="ryxm"/>
        <result column="sick_type" property="sickType"/>
        <result column="disease_time" property="diseaseTime"/>
        <result column="disease_reason" property="diseaseReason"/>
        <result column="main_complaint" property="mainComplaint"/>
        <result column="visit_state" property="visitState"/>
        <result column="visit_process_method" property="visitProcessMethod"/>
        <result column="visit_time" property="visitTime"/>
        <result column="visit_userid" property="visitUserid"/>
        <result column="visit_user_name" property="visitUserName"/>
        <result column="visit_conclusion" property="visitConclusion"/>
        <result column="prison_id" property="prisonId"/>
        <result column="tool_use_advise" property="toolUseAdvise"/>
        <result column="tool_use_cancel_reason" property="toolUseCancelReason"/>
        <result column="ext_id" property="extId"/>
        <result column="CITY_NAME" property="cityName"/>
        <result column="CITY_CODE" property="cityCode"/>
        <result column="REG_NAME" property="regName"/>
        <result column="REG_CODE" property="regCode"/>
        <result column="ORG_NAME" property="orgName"/>
        <result column="ORG_CODE" property="orgCode"/>
        <result column="IS_DEL" property="isDel"/>
        <result column="ADD_USER" property="addUser"/>
        <result column="ADD_USER_NAME" property="addUserName"/>
        <result column="ADD_TIME" property="addTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
        <result column="UPDATE_USER_NAME" property="updateUserName"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>
    <select id="getVisitTodo" resultType="com.rs.module.ihc.controller.admin.ipm.vo.VisitRespVO">
        SELECT v.*,
               a.appointment_num,
               p.xm                 AS supervised_user_name,
               p.xb                 AS sex,
               p.csrq               AS birthday,
               CASE
                   WHEN p.csrq IS NOT NULL THEN
                       EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.csrq))
                   ELSE
                       NULL
                   END              AS age,
               p.dabh,
               p.ryzt,
               r.id                 AS room_id,
               r.room_name,
               a.primary_diagnosis,
               a.disease_level,
               mp.id                AS prescribe_id,
               mp.prescribe_status,
               mp.dispense_num,
               mp.dose_num,
               mp.primary_diagnosis AS prescribe_primary_diagnosis,
               mp.prescribe_num,
               mp.doctor_advice_type,
               mp.main_complaint    AS prescribe_main_complaint
        FROM ihc_ipm_visit v
                 LEFT JOIN ihc_ipm_internal_medical_appointment a ON a.id = v.appointment_id AND a.is_del = '0'
                 LEFT JOIN vw_acp_pm_prisoner p ON p.jgrybm = v.jgrybm AND p.is_del = '0'
                 LEFT JOIN acp_pm_area_prison_room r ON r.id = p.jsh AND r.is_del = '0'
                 LEFT JOIN ihc_ipm_prescribe mp ON mp.business_id = v.id AND mp.is_del = '0'
            AND mp.prescribe_type = 2
        <where>
            v.is_del = '0'
              and v.visit_process_method = '0'
            <if test="ry != null">
                <if test="ry.jgrybm != null">
                    AND v.jgrybm = #{ry.jgrybm}
                </if>
            </if>
        </where>

        ORDER BY v.update_time desc
    </select>
    <select id="listXzjgByRoomId" resultType="com.rs.module.ihc.controller.admin.ipm.vo.VisitXzjgRespVO">
        SELECT v.*,
               a.appointment_num,
               p.xm                 AS supervised_user_name,
               p.xb                 AS sex,
               p.csrq               AS birthday,
               CASE
                   WHEN p.csrq IS NOT NULL THEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.csrq))
                   ELSE NULL
                   END              AS age,
               p.dabh,
               p.ryzt,
               r.id                 AS room_id,
               r.room_name,
               a.primary_diagnosis,
               a.disease_level,
               mp.id                AS prescribe_id,
               mp.prescribe_status,
               mp.dispense_num,
               mp.dose_num,
               mp.primary_diagnosis AS prescribe_primary_diagnosis,
               mp.prescribe_num,
               mp.doctor_advice_type,
               mp.main_complaint    AS prescribe_main_complaint
        FROM ihc_ipm_visit v
                 LEFT JOIN ihc_ipm_internal_medical_appointment a ON a.id = v.appointment_id AND a.is_del = '0'
                 INNER JOIN vw_acp_pm_prisoner p ON p.jgrybm = v.jgrybm AND p.is_del = '0'
                 INNER JOIN acp_pm_area_prison_room r ON r.id = p.jsh AND r.is_del = '0'
                 LEFT JOIN ihc_ipm_prescribe mp ON mp.business_id = v.id AND mp.is_del = '0' AND mp.prescribe_type = 2
        WHERE v.is_del = '0'
        <if test="type != null">
            AND v.visit_process_method = #{type}
        </if>
        <if test="roomId != null">
            AND r.id = #{roomId}
        </if>
        ORDER BY v.update_time DESC
    </select>
    <select id="countEquipmentUseByJgrybm" resultType="java.lang.Integer">
        select count(*)
        from acp_gj_equipment_use
        where jgrybm = #{jgrybm}
          and status = '06'
          and is_del = 0
    </select>

    <select id="findDiseaseTimeDescInId" resultType="com.rs.module.ihc.entity.ipm.VisitDO">
        select *
        from ihc_ipm_visit
        where is_del = 0
          and id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by disease_time desc
    </select>
</mapper>
