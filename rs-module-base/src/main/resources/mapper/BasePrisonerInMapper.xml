<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rs.module.base.dao.pm.PrisonerInDao">

    <select id="getPoliceWarderInfo" resultType="com.rs.module.base.controller.admin.pm.vo.GetPoliceWarderInfoVO">
        select t1.jgrybm as "superviseUserCode",
               t2.id   as "roomId",
               t2.room_name,
               t3.police_id,
               t3.police_name,
               t3.user_type
        from vw_acp_pm_prisoner_in t1
                 inner join acp_pm_area_prison_room t2 on t1.jsh = t2.id
                 left join acp_pm_prison_room_warder t3 on t2.id = t3.room_id and t3.status = '1'
        where t1.rybh in (
        <foreach collection="supervisedUserCodeList" item="supervisedUserCode" separator=",">
            #{supervisedUserCode}
        </foreach>
        )
    </select>

    <select id="getHavePunishmentToolUse" resultType="java.lang.String">
        select prisoner_id
        from prison_punishment_tool_use
        where (is_del = 0 or is_del is null) and remove_time is null
        and prisoner_id in (
        <foreach collection="supervisedUserCodeList" item="supervisedUserCode" separator=",">
            #{supervisedUserCode}
        </foreach>
        )
    </select>

    <select id="getRoomPoliceWarderInfo" resultType="com.rs.module.base.controller.admin.pm.vo.GetRoomPoliceWarderInfoVO">
        select t1.id as "roomId",
               t1.room_name,
               t2.police_id,
               t2.police_name,
               t2.user_type
        from acp_pm_area_prison_room t1
                 left join acp_pm_prison_room_warder t2 on t1.id = t2.room_id and t2.status = '1'
        where t1.id in
        <foreach collection="roomIds" item="roomId" separator="," open="(" close=")">
            #{roomId}
        </foreach>
    </select>

    <select id="getNotPlanCwh" resultType="com.rs.module.base.entity.pm.PrisonerInDO">
        select * from vw_acp_pm_prisoner_in
        WHERE is_del = '0'
          AND org_code = #{orgCode}
          AND jsh = #{roomId}
          AND (cwh IS NULL OR cwh = '')
    </select>

    <select id="getSickByRoom" resultType="com.rs.module.base.entity.pm.PrisonerInDO">
        select t1.* from vw_acp_pm_prisoner_in t1, ihc_pm_severely_sick_manage t2
        WHERE t1.is_del = '0'
          AND t2.is_del = '0'
          AND t1.org_code = #{orgCode}
          AND t1.jsh = #{roomId}
          AND t1.jgrybm = t2.jgrybm
          AND t2.business_status = '1'
    </select>

    <select id="getAloneByRoom" resultType="com.rs.module.base.entity.pm.PrisonerInDO">
        select b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from acp_gj_alone_imprison a inner join vw_acp_pm_prisoner_in b on a.jgrybm = b.jgrybm and b.is_del = 0
        where a.is_del = 0
          and a.out_time is null
          AND b.org_code = #{orgCode}
          AND b.jsh = #{roomId}
    </select>

    <select id="getConfinementByRoom" resultType="com.rs.module.base.entity.pm.PrisonerInDO">
        select b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from acp_gj_confinement_reg a left join vw_acp_pm_prisoner_in b on a.jgrybm = b.jgrybm and b.is_del = 0
        where a.is_del = 0
          and a.actual_end_time &gt;= now()
          and a.confinement_start_date &lt;= now()
          AND b.org_code = #{orgCode}
          AND b.jsh = #{roomId}
    </select>

    <select id="getEquipmentByRoom" resultType="com.rs.module.base.entity.pm.PrisonerInDO">
        select b.id, b.jgrybm, b.xm, b.front_photo, b.jsh, b.room_name, b.sshj, b.sxzm, b.gyqx
        from acp_gj_equipment_use a left join vw_acp_pm_prisoner_in b on a.jgrybm = b.jgrybm and b.is_del = 0
        where a.is_del = 0
          and a.status = '06'
          AND b.org_code = #{orgCode}
          AND b.jsh = #{roomId}
    </select>

    <select id="getOutPrisonerByRoom" resultType="java.util.HashMap">
        SELECT A.jgrybm, 'lawyer' as "type"
        FROM acp_pm_lawyer_meeting li
                 LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm
        WHERE (li.back_to_cell_time is null or li.back_to_cell_time >= now())
          AND A.org_code = #{orgCode} AND A.jsh = #{roomId}

        UNION ALL

        SELECT A.jgrybm, 'family' as "type"
        FROM acp_wb_family_meeting li
                 LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm
        WHERE (li.return_time is null or li.return_time >= now())
          AND A.org_code = #{orgCode} AND A.jsh = #{roomId}

        UNION ALL

        SELECT A.jgrybm, 'arraignment' as "type"
        FROM acp_wb_arraignment li
                 LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm
        WHERE (li.return_time is null or li.return_time >= now())
          AND A.org_code = #{orgCode} AND A.jsh = #{roomId}

        UNION ALL

        SELECT A.jgrybm, 'interrogation' as "type"
        FROM acp_wb_bring_interrogation li
                 LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm
        WHERE (li.return_time is null or li.return_time >= now())
          AND A.org_code = #{orgCode} AND A.jsh = #{roomId}

        UNION ALL

        SELECT A.jgrybm, 'consular' as "type"
        FROM acp_wb_consular_meeting li
                 LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm
        WHERE (li.return_time is null or li.return_time >= now())
          AND A.org_code = #{orgCode} AND A.jsh = #{roomId}

        UNION ALL

        SELECT A.jgrybm, 'escort' as "type"
        FROM acp_wb_escort li
                 LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm
        WHERE (li.return_time is null or li.return_time >= now())
          AND A.org_code = #{orgCode} AND A.jsh = #{roomId}

        UNION ALL

        SELECT A.jgrybm, 'treatment' as "type"
        FROM acp_pm_out_prison_treatment li
                 LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm
        WHERE li.leave_time IS NOT NULL
          AND ( li.back_operate_time IS NULL OR li.back_operate_time >= now())
          AND A.org_code = #{orgCode} AND A.jsh = #{roomId}
    </select>

    <select id="getOutTreatmentByOrg" resultType="java.lang.Integer">
        SELECT count(1)
        FROM acp_pm_out_prison_treatment li
                 LEFT JOIN vw_acp_pm_prisoner_in A ON A.jgrybm = li.jgrybm
        WHERE li.leave_time IS NOT NULL
          AND ( li.back_operate_time IS NULL OR li.back_operate_time >= now())
          AND A.org_code = #{orgCode}
          <if test="roomId != null and roomId != ''">
            AND A.jsh = #{roomId}
          </if>
    </select>

    <select id="isSickByJgrybm" resultType="java.lang.Boolean">
        select case when count(1) > 0 then true else false end from ihc_pm_severely_sick_manage a
        where a.business_status = '1'
          and a.jgrybm = #{jgrybm}
    </select>

</mapper>
