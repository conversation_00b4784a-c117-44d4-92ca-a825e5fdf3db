<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.pm.CusAppDao">
    <select id="getAllApply" resultType="java.util.Map">
        select a.*, b.flmc,b.pre_url,b.mark
        from acp_pm_cus_app a,
        acp_pm_cus_app_cat b
        where ((a.id in (select t1.yyid
        from acp_pm_cus_app_uac t1
        where
        1 != 1
        <if test="roleIds != null and roleIds.size>0">
            or (t1.qxlx = '01' and t1.qxid in
            <foreach collection="roleIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>)
        </if>
        <if test="regCode != null and regCode != ''">
            or (t1.qxlx = '02' and t1.qxid = #{regCode})
        </if>
        <if test="orgCode != null and orgCode != ''">
            or (t1.qxlx = '03' and t1.qxid = #{orgCode})
        </if>
        ))
        or a.sfgg = '1')
        <if test="yylx != null and yylx != ''">
            and a.yylx = #{yylx}
        </if>
        <if test="mark != null and mark != ''">
            and b.mark = #{mark}
        </if>
        and a.is_del = '0' AND a.system_id = #{systemId}
        and a.fl_id = b.id order by b.order_id asc, a.order_id asc
    </select>


    <select id="getWdyyList" resultType="java.util.Map">
        select a.*, b.flmc
        from acp_pm_cus_app a,
        acp_pm_cus_app_cat b
        where
        a.id in
        <foreach collection="yyidList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="yylx != null and yylx != ''">
            and a.yylx = #{yylx}
        </if>
        and a.is_del = '0'
        and a.fl_id = b.id order by a.order_id asc
    </select>

    <delete id="deleteCusAppUserByUserIdCard" parameterType="string">
        delete from acp_pm_cus_app_user where user_id_card = #{idCard}
    </delete>


</mapper>
