<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.base.dao.pm.AreaPrisonRoomDao">

    <select id="getMaxIdByRoomType" resultType="string">
        select max(room_code) from acp_pm_area_prison_room where room_code like concat(#{str}, '%')
    </select>


    <select id="getRoomWithViolationPage" parameterType="com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageWithViolationReqVO"
            resultType="com.rs.module.base.entity.pm.AreaPrisonRoomDO">
            select c.* from (
                select a.*, coalesce(b.violation_num,0) as violation_num
                from acp_pm_area_prison_room a
                left join
                    (select room_id, count(1) as violation_num from acp_pi_violation_record
                    where is_del = 0 and add_time BETWEEN date_trunc('month', now()) and date_trunc('month', now()+ INTERVAL ' 1 month')
                     GROUP BY room_id) b
                on a.id = b.room_id
                where a.is_del = 0 and a.status = '1'
                <if test="req.regCode!=null and req.regCode!=''">
                    and a.reg_code = #{req.regCode}
                </if>
                <if test="req.orgCode!=null and req.orgCode!=''">
                    and a.org_code = #{req.orgCode}
                </if>
                <if test="req.roomName!=null and req.roomName!=''">
                    and a.room_name like concat('%',#{req.roomName},'%')
                </if>
                <if test="req.areaId!=null and req.areaId!=''">
                    and a.area_id = #{req.areaId}
                </if>
                <if test="req.areaName!=null and req.areaName!=''">
                    and a.area_name like concat('%',#{req.areaName},'%')
                </if>
                <if test="req.roomCodes!=null and req.roomCodes.size()!=0">
                    and a.id in (<foreach collection="req.roomCodes" item="item" separator=",">#{item}</foreach>)
                </if>
        ) c where 1=1  ORDER BY c.violation_num asc, c.add_time desc

    </select>

</mapper>
