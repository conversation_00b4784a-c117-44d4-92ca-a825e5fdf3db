<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.ihc.dao.ipm.PrescribeExecuteDao">
    <select id="selectFytz" resultType="com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecuteFytzRespVO">
        SELECT t0.id,
               t0.dose_time,
               t0.dose_video_url,
               t0.sign_pic_url,
               t0.prescribe_id,
               t0.sign_status,
               t0.sign_time,
               t0.dose_status,
               t0.doctor_advice,
               t0.prescribe_describe,
               t0.add_time,
               t0.add_user,
               t0.add_user_name,
               t0.execute_source,
               t0.medical_police_sfzh,
               t0.medical_police_name,
               t0.remark,
               t0.room_id,
               t0.room_name,
               t0.manage_police_sfzh,
               t0.manage_police_name,
               t0.assist_police_sfzhs,
               t0.assist_police_names,
               t1.jgrybm,
               t1.doctor_advice_type,
               t1.prescribe_num,
               t0.org_code,
               t1.doctor_advice_type,
               t1.dose_num
        FROM ihc_ipm_prescribe_execute t0
                 INNER JOIN ihc_ipm_prescribe t1 ON t0.prescribe_id = t1.id AND t1.is_del = '0'
        <where>
            t0.is_del = '0'
            <if test="reqVO.jgrybm != null and reqVO.jgrybm != ''">
                AND t1.jgrybm = #{reqVO.jgrybm}
            </if>
            <if test="reqVO.doseStatus != null and reqVO.doseStatus != ''">
                AND t0.dose_status = #{reqVO.doseStatus}
            </if>
            <if test="reqVO.prisonerId != null and reqVO.prisonerId != ''">
                AND t0.org_code = #{reqVO.prisonerId}
            </if>
            <if test="reqVO.doseStatus != null and reqVO.doseStatus != ''">
                AND t0.dose_status = #{reqVO.doseStatus}
            </if>

        </where>
        ORDER BY t0.update_time desc
    </select>

    <select id="selectFxgzsTodo" resultType="com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecuteFytzRespVO">
        SELECT t0.id,
               t0.dose_time,
               t0.dose_video_url,
               t0.sign_pic_url,
               t0.prescribe_id,
               t0.sign_status,
               t0.sign_time,
               t0.dose_status,
               t0.doctor_advice,
               t0.prescribe_describe,
               t0.add_time,
               t0.add_user,
               t0.add_user_name,
               t0.execute_source,
               t0.medical_police_sfzh,
               t0.medical_police_name,
               t0.remark,
               t0.room_id,
               t0.room_name,
               t0.manage_police_sfzh,
               t0.manage_police_name,
               t0.assist_police_sfzhs,
               t0.assist_police_names,
               t1.jgrybm,
               t1.doctor_advice_type,
               t1.prescribe_num,
               t0.org_code,
               t1.doctor_advice_type,
               t1.dose_num,
               t0.fxgzs_url
        FROM ihc_ipm_prescribe_execute t0
                 INNER JOIN ihc_ipm_prescribe t1 ON t0.prescribe_id = t1.id AND t1.is_del = '0'
        <where>
            t0.is_del = '0'
            AND t1.jgrybm = #{reqVO.jgrybm}
            and t0.fxgzs_url is null
            and t0.dose_status = '2'
            <!-- 可选的时间范围筛选 -->
            <if test="reqVO.rangType != null">
                <choose>
                    <when test="reqVO.rangType == 1"> <!-- 今天 -->
                        AND DATE(t0.dose_time) = CURRENT_DATE
                    </when>
                    <when test="reqVO.rangType == 2"> <!-- 昨天 -->
                        AND DATE(t0.dose_time) = CURRENT_DATE - INTERVAL '1 day'
                    </when>
                    <when test="reqVO.rangType == 3"> <!-- 近一周 -->
                        AND t0.dose_time >= CURRENT_DATE - INTERVAL '7 days'
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY t0.update_time desc
    </select>

    <select id="selectFxgzsSuccess" resultType="com.rs.module.ihc.controller.admin.ipm.vo.PrescribeExecuteFytzRespVO">
        SELECT t0.id,
        t0.dose_time,
        t0.dose_video_url,
        t0.sign_pic_url,
        t0.prescribe_id,
        t0.sign_status,
        t0.sign_time,
        t0.dose_status,
        t0.doctor_advice,
        t0.prescribe_describe,
        t0.add_time,
        t0.add_user,
        t0.add_user_name,
        t0.execute_source,
        t0.medical_police_sfzh,
        t0.medical_police_name,
        t0.remark,
        t0.room_id,
        t0.room_name,
        t0.manage_police_sfzh,
        t0.manage_police_name,
        t0.assist_police_sfzhs,
        t0.assist_police_names,
        t1.jgrybm,
        t1.doctor_advice_type,
        t1.prescribe_num,
        t0.org_code,
        t1.doctor_advice_type,
        t1.dose_num,
        t0.fxgzs_url
        FROM ihc_ipm_prescribe_execute t0
        INNER JOIN ihc_ipm_prescribe t1 ON t0.prescribe_id = t1.id AND t1.is_del = '0'
        <where>
            t0.is_del = '0'
            AND t1.jgrybm = #{reqVO.jgrybm}
            and t0.fxgzs_url is not null
            and t0.dose_status = '2'
            <!-- 可选的时间范围筛选 -->
            <if test="reqVO.rangType != null">
                <choose>
                    <when test="reqVO.rangType == 1"> <!-- 今天 -->
                        AND DATE(t0.dose_time) = CURRENT_DATE
                    </when>
                    <when test="reqVO.rangType == 2"> <!-- 昨天 -->
                        AND DATE(t0.dose_time) = CURRENT_DATE - INTERVAL '1 day'
                    </when>
                    <when test="reqVO.rangType == 3"> <!-- 近一周 -->
                        AND t0.dose_time >= CURRENT_DATE - INTERVAL '7 days'
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY t0.update_time desc
    </select>

</mapper>
