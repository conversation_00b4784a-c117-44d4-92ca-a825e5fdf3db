package com.rs.module.base.controller.admin.video.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* base_vid_video 实体
*
* <AUTHOR>
* @version 1.0
* @date: 2022-05-10
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value="BaseVidVideoVO对象", description="")
public class BaseVidVideoVO implements Serializable {




    @JsonProperty("deviceCode")
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;



    @JsonProperty("treeCode")
    @ApiModelProperty(value = "视频联网节点编码")
    private String treeCode;



}

