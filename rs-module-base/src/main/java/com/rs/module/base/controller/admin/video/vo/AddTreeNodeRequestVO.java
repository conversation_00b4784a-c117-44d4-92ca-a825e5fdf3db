package com.rs.module.base.controller.admin.video.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2022/5/10
*/


@Data
@ApiModel(value = "视频联网配置增加区域设备")
public class AddTreeNodeRequestVO {

    @ApiModelProperty(value = "父级树编码",required = true)
    private String parentTreeCode;

    @ApiModelProperty(value = "复制目标树编码")
    private String dstTreeCode;

    @ApiModelProperty(value = "子节点名字",required = true)
    private String name;

    @ApiModelProperty(value = "国标",required = true)
    private String gb28181Code;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "监所编号")
    private String prisonId;
}
