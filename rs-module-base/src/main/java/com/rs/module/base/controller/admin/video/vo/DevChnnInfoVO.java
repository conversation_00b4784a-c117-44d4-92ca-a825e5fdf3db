package com.rs.module.base.controller.admin.video.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "通道基本信息")
public class DevChnnInfoVO {
    private String chanId;

    @ApiModelProperty("所属区域")
    private String treeCodeName;

    @ApiModelProperty("所属设备")
    private String devName;

    @ApiModelProperty("通道名称")
    private String chanName;

    @ApiModelProperty("0在线 1:离线")
    private Integer status;

    @ApiModelProperty("默认码流（1主码流，2辅码流）")
    private Integer defaultStreamType;

    @ApiModelProperty("外观 0 枪机;1 球机;2 半球;3 台柱;4 重载;5 枪球一体机; 6 GPS视频设备")
    private Integer facade;

    @ApiModelProperty("所属平台")
    private String platformName;

    @ApiModelProperty("控制类型(0不可控、1完全可控)")
    private Integer controlType;

    @ApiModelProperty("设备编码信息集合")
    private List<DevChnnInfoAvVO> devAvList;

    @ApiModelProperty("0: 本机平台 1:级联平台")
    private Integer platformId;

}
