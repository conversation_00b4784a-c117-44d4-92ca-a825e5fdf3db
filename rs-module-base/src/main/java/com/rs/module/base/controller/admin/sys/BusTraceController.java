package com.rs.module.base.controller.admin.sys;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.sys.vo.BusTraceListReqVO;
import com.rs.module.base.controller.admin.sys.vo.BusTracePageReqVO;
import com.rs.module.base.controller.admin.sys.vo.BusTraceRespVO;
import com.rs.module.base.controller.admin.sys.vo.BusTraceSaveReqVO;
import com.rs.module.base.entity.sys.BusTraceDO;
import com.rs.module.base.enums.BusTraceGroupEnum;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.sys.BusTraceService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "实战平台 - 业务轨迹")
@RestController
@RequestMapping("/acp/sys/busTrace")
@Validated
public class BusTraceController {

    @Resource
    private BusTraceService busTraceService;

    @PostMapping("/create")
    @ApiOperation(value = "创建业务轨迹")
    public CommonResult<String> createBusTrace(@Valid @RequestBody BusTraceSaveReqVO createReqVO) {
        return success(busTraceService.createBusTrace(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新业务轨迹")
    public CommonResult<Boolean> updateBusTrace(@Valid @RequestBody BusTraceSaveReqVO updateReqVO) {
        busTraceService.updateBusTrace(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除业务轨迹")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBusTrace(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           busTraceService.deleteBusTrace(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获取业务轨迹")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BusTraceRespVO> getBusTrace(@RequestParam("id") String id) {
        BusTraceDO busTrace = busTraceService.getBusTrace(id);
        return success(BeanUtils.toBean(busTrace, BusTraceRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获取业务轨迹分页")
    public CommonResult<?> getBusTracePage(@Valid @RequestBody BusTracePageReqVO pageReqVO) {
        PageResult<BusTraceDO> pageResult = busTraceService.getBusTracePage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获取业务轨迹列表")
    public CommonResult<?> getBusTraceList(@Valid @RequestBody BusTraceListReqVO listReqVO) {
        List<BusTraceDO> list = busTraceService.getBusTraceList(listReqVO);
        return success(list);
    }
    
    @PostMapping("/getJgryTraceOrgList")
    @ApiOperation(value = "获取监管人员轨迹相关场所")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "jgrybm", value = "监管人员编码"),
    	@ApiImplicitParam(name = "group", value = "分组(01:业务轨迹,02:医疗轨迹,03:警员轨迹)")
    })
    public CommonResult<?> getJgryTraceOrgList(@RequestParam(value = "jgrybm", required = true) String jgrybm,
    		@RequestParam(value = "group", required = true) String group) {
    	List<String> busTypes = BusTraceGroupEnum.getGroupBusTypes(group);
    	List<Map<String, Object>> orgList = busTraceService.getJgryTraceOrgList(jgrybm, busTypes);
    	return success(orgList);
    }
    
    @PostMapping("/getBusType")
    @ApiOperation(value = "获取分组业务类型")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "group", value = "分组(01:业务轨迹,02:医疗轨迹,03:警员轨迹)")
    })
    public CommonResult<?> getBusType(@RequestParam(value = "group", required = true) String group) {
    	BusTraceGroupEnum busTraceGroup = BusTraceGroupEnum.getGroupByCode(group);
    	if(busTraceGroup != null) {
    		Map<String, String> busTypeMap = new LinkedHashMap<>();
    		List<BusTypeEnum> busTypes = busTraceGroup.getBusTypes();
    		for(BusTypeEnum busTypeEnum : busTypes) {
    			busTypeMap.put(busTypeEnum.getBusType(), busTypeEnum.getBusName());
    		}
    		return CommonResult.success(busTypeMap);
    	}
    	else {
    		return CommonResult.error("找不到业务类型分组");
    	}
    }
}
