package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;


@Data
@ApiModel(description = "")
public class StaffDutyRecordSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "模板temp主键")
    @NotEmpty(message = "模板id不能为空")
    private String tempId;

    @ApiModelProperty(value = "值班日期")
    @NotEmpty(message = "值班日期 不能为空")
    private Date dutyDate;
}

