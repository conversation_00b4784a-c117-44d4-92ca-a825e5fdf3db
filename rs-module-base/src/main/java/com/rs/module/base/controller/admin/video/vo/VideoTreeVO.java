package com.rs.module.base.controller.admin.video.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2021/12/13
*/
@ApiModel("设备树")
@Data
public class VideoTreeVO implements Serializable {
    private static final long serialVersionUID = 6243833545820041254L;
    @ApiModelProperty("编号")
    private Integer id;

    @ApiModelProperty("父类型编号")
    private Integer pid;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("类型 1：区域 2：设备")
    private Integer type;

    @ApiModelProperty("区域编码")
    private String treeCode;

    @ApiModelProperty("上级所有ID")
    private String parentPathId;

    @ApiModelProperty("所在目录级别")
    private Integer treeLevel;

    @ApiModelProperty("亲儿子个数")
    private Integer childNumber;

    @ApiModelProperty("设备总数")
    private Integer allNum;

    @ApiModelProperty("是否锁定")
    private Integer isLocked;

    @ApiModelProperty("在线数量")
    private Integer onlineNum;

    @ApiModelProperty("总数量")
    private Integer totalNum;

    @ApiModelProperty("设备id")
    private Long chanId;

    private Integer isFocus;

    @ApiModelProperty("0在线 1:离线")
    private Integer status;

    private Integer chanTypeId;

    private Integer treeId;

    @ApiModelProperty("AR类型0 非AR设备 1 AR设备")
    private String chnAbility;

    @ApiModelProperty("是否可控 0不可控、1完全可控")
    private Integer controlType;

    @ApiModelProperty("是否级联 0 直连 1：级联")
    private Integer platformId;

    @ApiModelProperty("设备编号")
    private Long devId;

    @ApiModelProperty("录像默认存储类型（0中心，1前端）")
    private Integer storageType;

    @ApiModelProperty("国标编码")
    private String gb28181Code;

    @ApiModelProperty("设备锁定状态 0:非加锁 1:加锁")
    private Integer devLockStatus;

    @ApiModelProperty("云台锁定状态 0:非加锁 1:加锁")
    private Integer ptzLockStatus;

    private Integer usageType;

    @ApiModelProperty("默认码流（1主码流，2辅码流）")
    private Integer defaultStreamType;

    @ApiModelProperty("外观 0 枪机;1 球机;2 半球;3 台柱;4 重载;5 枪球一体机; 6  GPS视频设备")
    private Integer facade;

    @ApiModelProperty("上级所有名字")
    private String pathName;

    @ApiModelProperty("监所编号")
    private String prisonId;

}
