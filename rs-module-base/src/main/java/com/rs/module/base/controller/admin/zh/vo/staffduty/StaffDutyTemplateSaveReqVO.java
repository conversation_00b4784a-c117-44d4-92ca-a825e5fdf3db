package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "值班模板对象", description = "排班模板")
public class StaffDutyTemplateSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "模板名称",required = true)
    @NotEmpty(message = "模板名字不能为空")
    private String name;

    @ApiModelProperty( "id 更新的时候传入")
    private String id;
    
    @NotEmpty(message = "启停状态不能为空")
    @ApiModelProperty(value = " 0-停用 1-启用",hidden = true)
    private String status;

    @ApiModelProperty(value = "值班岗位列表",required = true)
    @NotEmpty(message = "值班岗位列表不能为空")
    List<StaffDutyPostSaveReqVO> dutyPostDTOS;
}

