package com.rs.module.base.controller.admin.sys.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-业务轨迹 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BusTraceRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("ip地址")
    private String ip;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("操作用户")
    private String operateUser;
    @ApiModelProperty("业务类型")
    private String busType;
    @ApiModelProperty("业务名称")
    private String busName;
    @ApiModelProperty("业务内容")
    private String content;
    @ApiModelProperty("业务主键")
    private String businessId;
    @ApiModelProperty("监室Id")
    private String roomId;
}
