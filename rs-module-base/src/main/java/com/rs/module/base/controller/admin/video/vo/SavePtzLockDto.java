package com.rs.module.base.controller.admin.video.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("保存锁定信息DTO")
public class SavePtzLockDto {

    @ApiModelProperty(value = "用户ID")
    String userid;

    @ApiModelProperty(value = "通道ID")
    Integer channelId;

    @ApiModelProperty(value = "通道名称")
    String channelName;

    @ApiModelProperty(value = "锁定类型，0=云台锁定，1=设备锁定")
    String lockType;

    @ApiModelProperty(value = "锁定方式，0=常规锁定，1=计划锁定")
    String lockMethod;

    @ApiModelProperty(value = "自动解锁时间, yyyy-MM-dd HH:mm:ss,当lockMethod为1时，该字段必填")
    String autoUnlockTime;

    @ApiModelProperty(value = "解锁密码")
    String password;

    @ApiModelProperty(value = "锁定原因")
    String remark;
}
