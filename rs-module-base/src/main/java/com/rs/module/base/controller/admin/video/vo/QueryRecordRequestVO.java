package com.rs.module.base.controller.admin.video.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "查询录像文件返回")
public class QueryRecordRequestVO {
    @ApiModelProperty(value = "通道ID")
    private String chanId;

    @ApiModelProperty("录像原因(-1:所有录像;0:告警录像;1:计划录像;2:手动录像;3:下载录像;4:录像文件备份)，不传默认-1")
    private Integer relevantReason;

    @ApiModelProperty(value = "设备名字")
    private String name;

    @ApiModelProperty(value = "开始时间")
    private String rcdStartTime;

    @ApiModelProperty("结束时间")
    private String rcdEndTime;
}
