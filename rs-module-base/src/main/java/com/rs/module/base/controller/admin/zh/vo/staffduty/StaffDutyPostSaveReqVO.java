package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "PrisonWorkforceManageDutyPostDTO对象", description = "")
public class StaffDutyPostSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "模板表id",hidden = true)
    @NotEmpty(message = "模板表id 不能为空")
    private String tempId;

    @ApiModelProperty(value = "值班岗位",required = true)
    @NotEmpty(message = "值班岗位 不能为空")
    private String postName;

    @ApiModelProperty(value = "是否有值班岗位 0-无 1-有",required = true)
    @NotEmpty(message = "是否有值班岗位 不能为空")
    private Integer hasSubPost;

    @ApiModelProperty(value = "值班角色",required = true)
    @NotEmpty(message = "值班角色 不能为空")
    List<StaffDutyRoleSaveReqVO> dutyRoleDTOS;




}

