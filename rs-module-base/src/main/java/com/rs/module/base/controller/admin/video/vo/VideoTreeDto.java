package com.rs.module.base.controller.admin.video.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2021/12/13
*/
@Data
@ApiModel("分层获取区域设备树")
public class VideoTreeDto {
   private String accessToken;

   @ApiModelProperty("节点编码")
   private String treeCode;

   @ApiModelProperty("在线状态 0:在线 1:离线（单选。以前1是信号丢失，2是设备连不上，现在统一为离线） 传[0,1]")
   private String onlineStatus;

   @ApiModelProperty("设备型号 0枪机、1球机 7:全景（单选，以前5是枪球一体机，现在归为球机）传[0,1]")
   private String devFacade;

   @ApiModelProperty("AR类型（0 非AR设备 1 AR设备）传[0,1]")
   private String chnAbility;

   @ApiModelProperty("设备锁定状态 (0 没锁定 1 设备锁定 ）传[0,1]")
   private String devLockStatus;

   @ApiModelProperty("云台锁定状态 (0 没锁定 1 云台锁定 ）传[0,1]")
   private String ptzLockStatus;

   @ApiModelProperty("区域编号")
   private Integer id;

   @ApiModelProperty("监室号")
   private String roomId;

   @ApiModelProperty("监所编号号")
   private String prisonId;
}
