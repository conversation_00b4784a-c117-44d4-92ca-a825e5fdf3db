package com.rs.module.base.controller.admin.video.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/***
 *
 */
@Data
@ApiModel(value = "查询录像文件参数")
public class QueryRecordRequestDTO {
    @ApiModelProperty(value = "查询开始时间(例如：2018-01-01 12:00:00)")
    private String startTime;

    @ApiModelProperty(value = "查询结束时间(例如：2018-01-01 23:00:00)")
    private String endTime;

    @ApiModelProperty(hidden = true)
    private String channelIds;

    @ApiModelProperty(value = "通道ID")
    private String chanId;

    @ApiModelProperty(hidden = true)
    private Integer pageStart;

    @ApiModelProperty(hidden = true)
    private Integer pageNum;

    @ApiModelProperty(value = "0：中心录像；1：前端录像；2:级联中心")
    private String storageType;

    @ApiModelProperty(hidden = true)
    private Integer locked;

    @ApiModelProperty(value = "录像原因(-1:所有录像;0:告警录像;1:计划录像;2:手动录像;3:下载录像;4:录像文件备份)，不传默认-1",hidden = true)
    private Integer relevantReason;
}
