package com.rs.module.base.controller.admin.video;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.video.vo.*;
import com.rs.module.base.service.pm.device.BaseDeviceService;
import com.rs.module.base.service.video.VideoRouteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

/**
 *
 */
@Api(value = "视频联网平台升级版接口")
@RestController
@Slf4j
@RequestMapping("base/videoRoute")
public class VideoRouteController{
    @Autowired
    private VideoRouteService videoRouteService;


    @Autowired
    private BaseDeviceService baseDeviceService;

    @PostMapping(value = "/getRealTimeStreamForWeb")
    @ApiOperation(value = "获取实时视频流地址(web端)-对接视频联网新版本V1.0")
    public Object getRealTimeStreamForWeb(@RequestBody OtherRealTimeStreamDto dto) {
        try {
            return JSON.toJSON(videoRouteService.getRealTimeStreamForWeb(dto));
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }

    @PostMapping(value = "/getRealTimeStreamForApp")
    @ApiOperation(value = "app点流-对接视频联网新版本V1.0")
    public CommonResult<String> getRealTimeStreamForApp(@RequestBody OtherRealTimeStreamAppDto dto) {
        try {
            if(dto.getStreamAgentType()==null){
                dto.setStreamAgentType(5);
            }
            String realTimeStreamForApp = videoRouteService.getRealTimeStreamForApp(dto);
            CommonResult CommonResult = JSON.parseObject(realTimeStreamForApp, CommonResult.class);
            return CommonResult;
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.error(e.getMessage());
        }
    }

    @GetMapping(value = "/getVideoTree")
    @ApiOperation(value = "设备树-对接视频联网新版本V1.0", response = VideoTreeVO.class, responseContainer = "List")
    public CommonResult<List<VideoTreeVO>> getVideoTree(@ApiParam VideoTreeDto dto) {
        try {
           //-- dto.setPrisonId(getUser().getPrisonId());
            List<VideoTreeVO> videoTree = videoRouteService.getVideoTree(dto);
            return success(videoTree);
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.getMessage());
        }
    }

    @GetMapping(value = "/getVideoByPathId")
    @ApiOperation(value = "根据pathId查询父链所有节点-对接视频联网新版本V1.0", response = VideoTreeVO.class, responseContainer = "List")
    public CommonResult<List<VideoTreeVO>> getVideoByPathId(@ApiParam VideoPageDto dto) {
        try {
            List<VideoTreeVO> list = videoRouteService.getVideoByPathId(dto);
            return success(list);
        } catch (Exception e) {
            e.printStackTrace();
            return error( e.toString());
        }
    }

    @GetMapping(value = "/getVideoByPage")
    @ApiOperation(value = "综合查询设备-对接视频联网新版本V1.0", response = VideoTreeVO.class, responseContainer = "List")
    public CommonResult<PageResult<VideoTreeVO>> getVideoByPage(@ApiParam VideoPageDto dto) {
        try {
            PageResult<VideoTreeVO> videoByPage = videoRouteService.getVideoByPage(dto);
            return success(videoByPage);
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.toString());
        }
    }

    @GetMapping(value = "/getVideoListById")
    @ApiOperation(value = "视频联网-根据目录id查询下面指定摄像头", response = VideoTreeVO.class, responseContainer = "List")
    public CommonResult<List<VideoTreeVO>> getVideoListById(@RequestParam String id,@RequestParam Integer num) {
        try {
            List<VideoTreeVO> videoListById = videoRouteService.getVideoListById(id, num, SessionUserUtil.getSessionUser().getOrgCode());
            return success(videoListById);
        } catch (Exception e) {
            return error(e.toString());
        }
    }

    @PostMapping(value = "/getUapToken")
    @ApiOperation(value = "获取token")
    public CommonResult<JSONObject> getUapToken() {
        try {
            JSONObject uapTokenFe = videoRouteService.getUapTokenFe();
            String token = uapTokenFe.getString("token");
            // 去除字符串中的 "bearer"（不区分大小写）
            String resultString = token.replaceAll("(?i)bearer", "").trim();
            uapTokenFe.put("token",resultString);
            return success(uapTokenFe);
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.toString());
        }
    }

    @PostMapping(value = "/getParentTreeCode")
    @ApiOperation(value = "获取父节点")
    public CommonResult<String> getParentTreeCode() {
        try {
            return success(videoRouteService.getParentTreeCode());
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.toString());
        }
    }
    @PostMapping(value = "/getSplwAreaByTreeCode")
    @ApiOperation(value = "根据treeCode获取联网平台目录")
    public CommonResult<JSONObject> getSplwAreaByTreeCode(String rootCode) {
        try {
            return success(videoRouteService.getSplwAreaByTreeCode(StringUtil.isNotEmpty(rootCode) ? rootCode : "001"));
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.toString());
        }
    }
    @GetMapping(value = "/sysDeviceTree")
    @ApiOperation(value = "同步设备树数据-对接视频联网新版本V1.0")
    public CommonResult<?> sysDeviceTree() {
        try {
            //同步时间较长 异步调用 先返回接口 再执行
            baseDeviceService.sysDeviceTree(SessionUserUtil.getSessionUser().getOrgCode());
            return success("同步成功");
        } catch (Exception e) {
            return error(e.toString());
        }
    }

    @GetMapping(value = "/sysVideoTreeByAreaName")
    @ApiOperation(value = "根据联网平台区域名字同步设备树-对接视频联网新版本V1.0")
    public CommonResult sysVideoTreeByAreaName(@RequestParam String areaName) {
        try {
            //同步时间较长 异步调用 先返回接口 再执行
            baseDeviceService.sysVideoTreeByAreaName(areaName,SessionUserUtil.getSessionUser().getOrgCode());
            return success("同步成功");
        } catch (Exception e) {
            log.error("同步失败",e);
            return error("同步失败"+e.getMessage());
        }
    }

    @PostMapping(value = "/getGoVideoOrGoVlinkRecordRequest")
    @ApiOperation(value = "查询goVideo/goVlink的录像文件")
    public CommonResult getGoVideoOrGoVlinkRecordRequest(@RequestBody GoVideoOrGoVlinkRecordRequestDto dto) {
        try {
            CommonResult CommonResult = JSON.parseObject(videoRouteService.getGoVideoOrGoVlinkRecordRequest(dto), CommonResult.class);
            return CommonResult;
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.toString());
        }
    }

    @PostMapping(value = "/ptzCtrlRequest")
    @ApiOperation(value = "云台控制")
    public CommonResult ptzCtrl(@RequestBody PtzCtrlNewDto dto) {
        try {
            CommonResult CommonResult = JSON.parseObject(videoRouteService.ptzCtrlRequest(dto), CommonResult.class);
            return CommonResult;
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.toString());
        }
    }

    @PostMapping(value = "/recordMonthRetrievalRequest")
    @ApiOperation(value = "按月检索录像文件存在的日期")
    public CommonResult<?> recordMonthRetrievalRequest(@RequestBody RecordMonthRetrievalDTO dto) {
        try {
            videoRouteService.recordMonthRetrievalRequest(dto);
            return success(videoRouteService.recordMonthRetrievalRequest(dto));
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.toString());
        }
    }

    @PostMapping(value = "/recordFileOperRequestTransfer")
    @ApiOperation(value = "录像文件操作请求")
    public CommonResult<String> recordFileOperRequestTransfer(@RequestBody RecordFileOperRequestTransferDTO transferDTO) {
        JSONObject jsonObject = videoRouteService.recordFileOperRequestTransfer(transferDTO);
        JSONObject message = jsonObject.getJSONObject("message");
        try {
            Integer operResult = message.getInteger("operResult");
            if(operResult==0){
                String url = message.getString("url");
                return success(url);
            }
        } catch (Exception e) {
           e.printStackTrace();
        }
        return error(message.toJSONString());
    }

    @PostMapping(value = "/queryRecordRequest")
    @ApiOperation(value = "查询录像文件")
    public CommonResult<List<QueryRecordRequestVO>> queryRecordRequest(@RequestBody QueryRecordRequestDTO requestDTO) {
        try {
            List<QueryRecordRequestVO> queryRecordRequestVOS = videoRouteService.queryRecordRequest(requestDTO);
            return success(queryRecordRequestVOS);
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.toString());
        }
    }


    @ApiOperation(value = "获取通道基本信息")
    @GetMapping(value = "/govDevChanInfoAv/{chanId}")
    @ApiImplicitParam(name = "chanId", value = "chanId", dataTypeClass = String.class, paramType = "path", required = true)
    public CommonResult<String> govDevChnnInfoAv(@Validated @PathVariable(value = "chanId") String chanId) {
        CommonResult CommonResult = JSON.parseObject(videoRouteService.govDevChnnInfoAv(chanId), CommonResult.class);
        return CommonResult;
    }

    @ApiOperation(value = "获取通道详情")
    @GetMapping(value = "/govChanInfo/{chanId}")
    @ApiImplicitParam(name = "chanId", value = "chanId", dataTypeClass = String.class, paramType = "path", required = true)
    public CommonResult<DevChnnInfoVO> govChanInfo(@Validated @PathVariable(value = "chanId") String chanId) {
        DevChnnInfoVO devChnnInfoVO = videoRouteService.govChanInfo(chanId);
        return success(devChnnInfoVO);
    }

//    @ApiOperation(value = "编辑通道")
//    @GetMapping(value = "/getSingleDevChnnRequest/{chanId}")
//    @ApiImplicitParam(name = "chanId", value = "chanId", dataType = "string", paramType = "path", required = true)
//    public CommonResult<Object> getSingleDevChnnRequest(@Validated @PathVariable(value = "chanId") String chanId) {
//        JSONObject jsonObject = videoRouteService.setDevChnnRequest(chanId);
//        return success(jsonObject);
//    }

   /* @ApiOperation(value = "根据chanId获取监室信息")
    @GetMapping(value = "/getRoomInfoByChanId/{chanId}")
    @ApiImplicitParam(name = "chanId", value = "chanId", dataTypeClass = String.class, paramType = "path", required = true)
    public CommonResult<AreaPrisonRoomPageVO> getRoomInfoByChanId(@Validated @PathVariable(value = "chanId") String chanId) {
        AreaPrisonRoomPageVO areaPrisonRoomPageVO = videoRouteService.getRoomInfoByChanId(chanId);
        return success(areaPrisonRoomPageVO);
    }*/

    @ApiOperation(value = "视频联网添加区域")
    @GetMapping(value = "/addTreeNodeRequest/{parentTreeCode}")
    @ApiImplicitParam(name = "parentTreeCode", value = "parentTreeCode", dataTypeClass = String.class, paramType = "path", required = true)
    public CommonResult<String> addTreeNodeRequest(@Validated @RequestBody AddTreeNodeRequestVO addTreeNodeRequestVO) {
        String s = videoRouteService.addTreeNodeRequest(addTreeNodeRequestVO);
        return success(s);
    }

    @PostMapping(value = "/presetCtrl")
    @ApiOperation(value = "设置云台预置点")
    public CommonResult<String> presetCtrl(@RequestBody PresetCtrlDto dto) {
        try {
            List<PreseDTO> presetByChannelId = videoRouteService.getPresetByChannelId(dto.getChanId());
            presetByChannelId.sort(Comparator.comparing(PreseDTO::getPresetId).reversed());
            //拿到当前设备的最大index值并+1给当前数据 如果没有就默认1
            if(dto.getOperType()==0){
                if(!StringUtil.isNotEmpty(dto.getIndex())){
                    if(presetByChannelId.size()>0){
                        dto.setIndex((presetByChannelId.get(0).getPresetId()+1)+"");
                    }else {
                        dto.setIndex("0");
                    }
                }
                //判断名称是否被使用
                for(PreseDTO preseDTO:presetByChannelId){
                    if(StringUtil.isNotEmpty(dto.getName())&&dto.getName().equals(preseDTO.getPresetName())){
                        return error("该名称已被使用");
                    }
                }
            }
            JSONObject jsonObject = JSONObject.parseObject(videoRouteService.presetCtrl(dto));
            Integer returnCode = jsonObject.getInteger("returnCode");
            if(returnCode==0){
                return success();
            }else if(returnCode==1){
                return error(jsonObject.toJSONString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.getMessage());
        }
        return  error("");
    }

    @GetMapping(value = "/turnToDefaultPreset/{chanId}")
    @ApiOperation(value = "恢复到默认预置位")
    @ApiImplicitParam(name = "chanId", value = "chanId", dataTypeClass = String.class, paramType = "path", required = true)
    public CommonResult<String> turnToDefaultPreset(@Validated @PathVariable(value = "chanId") String chanId) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(videoRouteService.turnToDefaultPreset2(chanId));
            Integer returnCode = jsonObject.getInteger("returnCode");
            if(returnCode==0){
                return success();
            }else if(returnCode==1){
                return error(jsonObject.toJSONString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return  error(e.getMessage());
        }
        return  error("");
    }

    @PostMapping(value = "/deletePreset")
    @ApiOperation(value = "删除预置位")
    public CommonResult<String> deletePreset(@RequestBody DeletePresetVO vo) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(videoRouteService.deletePreset(vo));
            Integer returnCode = jsonObject.getInteger("returnCode");
            if(returnCode==0){
                return success();
            }else if(returnCode==1){
                return error(jsonObject.toJSONString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return  error(e.getMessage());
        }
        return  error("");
    }

    @GetMapping(value = "/getPresetByChannelId/{chanId}")
    @ApiOperation(value = "获取通道所有预置点信息")
    @ApiImplicitParam(name = "chanId", value = "chanId", dataTypeClass = String.class, paramType = "path", required = true)
    public CommonResult<List<PreseDTO>> getPresetByChannelId(@Validated @PathVariable(value = "chanId") String chanId) {
        try {
            return success(videoRouteService.getPresetByChannelId(chanId));
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.getMessage());
        }
    }

    @GetMapping(value = "/getAllDeviceRequest/{devId}")
    @ApiOperation(value = "根据设备编号获取详情")
    @ApiImplicitParam(name = "devId", value = "devId", dataTypeClass = String.class, paramType = "path", required = true)
    public CommonResult<?> getAllDeviceRequest(@Validated @PathVariable(value = "devId") String devId) {
        try {
            return success(videoRouteService.getAllDeviceRequest(devId));
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.getMessage());
        }
    }

}
