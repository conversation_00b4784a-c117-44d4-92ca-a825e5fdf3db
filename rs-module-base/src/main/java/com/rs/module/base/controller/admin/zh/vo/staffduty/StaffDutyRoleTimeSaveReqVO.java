package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@ApiModel(value = "PrisonWorkforceManageDutyRoleTimeDTO对象", description = "")
public class StaffDutyRoleTimeSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "关联值班角色表id",hidden = true)
    @NotEmpty(message = "关联值班角色表id 不能为空")
    private String dutyRoleId;
    
    @ApiModelProperty(value = "值班班次")
    private String dutyShift;
    
    @ApiModelProperty(value = "值班时间类型-开始 1-当日 2-次日",required = true)
    @NotEmpty(message = "值班时间类型-开始 不能为空")
    private Integer dutyTimeTypeBegin;

    @ApiModelProperty(value = "值班时间类型-结束  1-当日 2-次日",required = true)
    @NotEmpty(message = "值班时间类型-结束 不能为空")
    private Integer dutyTimeTypeEnd;


    @ApiModelProperty(value = "值班时间开始",required = true)
    @NotEmpty(message = "值班时间开始 不能为空")
    //@JsonFormat(pattern = "HH:mm")
    private String dutyTimeBegin;

    @ApiModelProperty(value = "值班时间结束",required = true)
    @NotEmpty(message = "值班时间结束 不能为空")
    //@JsonFormat(pattern = "HH:mm")
    private String dutyTimeEnd;

    @ApiModelProperty(value = "岗位key",hidden = true)
    @NotEmpty(message = "值班时间结束 不能为空")
    private String postKey;


}

