package com.rs.module.base.controller.admin.video.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "预置位信息")
@Data
public class PreseDTO {
    @ApiModelProperty("默认")
    private Integer isDefault;

    @ApiModelProperty("预置位名称")
    private String presetName;

    @ApiModelProperty("通道编号")
    private String chanId;

    @ApiModelProperty("预置位编号")
    private Integer presetId;
}
