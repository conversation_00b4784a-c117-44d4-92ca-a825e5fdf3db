package com.rs.module.base.controller.admin.base;

import com.rs.framework.common.entity.OpsDicCode;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.mybatis.util.DicUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "bsp - 字典处理")
@RestController
@RequestMapping("/bsp/dic")
@Validated
public class BspDicController {

    @Value("${system-mark:}")
    private String systemMark;

    @ApiOperation(value = "通过名称获取字典")
    @ApiImplicitParam(name = "dicName", value = "字典名称")
    @GetMapping("/getDicByName")
    public CommonResult<List<OpsDicCode>> getDicByName(@RequestParam("dicName") String dicName,
                                                       @RequestParam(value = "systemMark", required = false) String sysMark) {
        if (StringUtils.isEmpty(sysMark)) {
            sysMark = systemMark;
        }
        List<OpsDicCode> opsDicCodeList = DicUtil.getDic(dicName, sysMark);
        if (opsDicCodeList.isEmpty()) {
            opsDicCodeList = DicUtil.getDic(dicName, "bsp");
        }
        return CommonResult.success(opsDicCodeList);
    }

}
