package com.rs.module.base.controller.admin.video.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2022/3/4
*/

@Data
@ApiModel("编码信息")
public class DevChnnInfoAvVO {
    @ApiModelProperty("码率")
    private String videoBitRate;

    @ApiModelProperty("码流类型 (1 主码流 2 子码流)")
    private Integer streamType;

    @ApiModelProperty("码率类型")
    private String bitRateType;

    @ApiModelProperty("I帧间隔")
    private String intervalFrameI;

    @ApiModelProperty("视频编码类型")
    private String videoEncType;

    @ApiModelProperty("音频编码类型")
    private String audioEncType;

    @ApiModelProperty("帧率")
    private String videoFrameRate;

    @ApiModelProperty("分辨率")
    private String resolution;

    @ApiModelProperty("通道oneId")
    private String channelId;

}
