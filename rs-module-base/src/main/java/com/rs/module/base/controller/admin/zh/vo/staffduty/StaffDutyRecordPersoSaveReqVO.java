package com.rs.module.base.controller.admin.zh.vo.staffduty;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@ApiModel( description = "值班排版-值班人员表")
public class StaffDutyRecordPersoSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "record关联的id 不能为空")
    @ApiModelProperty(value = "record关联的id")
    private String recordId;

    @ApiModelProperty(value = "民警编号")
    private String policeId;

    @ApiModelProperty(value = "民警姓名")
    @NotEmpty(message = "民警姓名 不能为空")
    private String policeName;
    @ApiModelProperty(value = "民警类型 1-民警  2-非民警")
    @NotEmpty(message = "民警类型 不能为空")
    private Integer policeType;

    @ApiModelProperty(value = "岗位唯一关联key")
    @NotEmpty(message = "岗位唯一关联key 不能为空")
    private String postKey;

}

