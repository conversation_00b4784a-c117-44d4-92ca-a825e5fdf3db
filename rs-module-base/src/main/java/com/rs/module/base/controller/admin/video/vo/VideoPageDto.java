package com.rs.module.base.controller.admin.video.vo;

import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2021/12/15
*/
@Data
@ApiModel("设备查询分页")
public class VideoPageDto extends PageParam {

    @ApiModelProperty("搜索名字")
    private String channelName;

    @ApiModelProperty("父链")
    private String parentPathId;

    @ApiModelProperty("缺失部分父Id")
    private String pathId;

    @ApiModelProperty("1:综合查询 2:设备 3:区域")
    private int type;
}
