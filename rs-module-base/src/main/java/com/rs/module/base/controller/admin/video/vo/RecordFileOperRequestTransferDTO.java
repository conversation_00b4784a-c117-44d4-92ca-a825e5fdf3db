package com.rs.module.base.controller.admin.video.vo;


import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*<AUTHOR>
*@email <EMAIL>
*@date 2022/2/16
*/
@Data
@ApiModel(value = "录像文件操作请求")
public class RecordFileOperRequestTransferDTO {
    @ApiModelProperty(value = "通道ID")
    private String chanId;

    @ApiModelProperty(value = "通道ID",hidden = true)
    private String id;

    @ApiModelProperty(value = "通道ID",hidden = true)
    private String channelId;

    @ApiModelProperty(hidden = true,value = "操作类型(0:表示文件回放;1:表示文件下载;2:表示时间回放;3:表示时间下载)ps:级联不支持按文件查询")
    private Integer operType;

    @ApiModelProperty(value = "OperType为文件时：录像文件 ID;OperType为时间时：开始时间,播放时间和结束时间（如“2020-03-23-15-00-00,2020-03-23-15-00-00,2020-03-23-16-00-00”）")
    private String rcdFilename;

    @ApiModelProperty("存储类型(0:服务器;1:前端设备;2:级联中心)")
    private String storageType;

    @ApiModelProperty(hidden = true,value = "录像原因(-1:所有录像;0:告警录像;1:计划录像;2:手动录像;3:下载录像;4:录像文件备份)")
    private Integer relevantReason;

    @ApiModelProperty(hidden = true)
    private String streamAgentType;

    @ApiModelProperty(hidden = true)
    private JSONObject encodeInfo;
}
