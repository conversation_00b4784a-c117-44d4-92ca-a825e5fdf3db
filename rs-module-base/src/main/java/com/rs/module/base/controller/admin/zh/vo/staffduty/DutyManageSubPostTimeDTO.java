package com.rs.module.base.controller.admin.zh.vo.staffduty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "值班时间列表")
public class DutyManageSubPostTimeDTO {
    @NotBlank(message = "值班时间 不能为空")
    @ApiModelProperty(value = "值班时间")
    private String time;

    @NotBlank(message = "值班角色key 不能为空")
    @ApiModelProperty(value = "值班角色key 不能为空")
    private String postKey;
}
