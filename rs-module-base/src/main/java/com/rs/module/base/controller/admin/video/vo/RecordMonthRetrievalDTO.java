package com.rs.module.base.controller.admin.video.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("录像-按月检索")
public class RecordMonthRetrievalDTO {
    @ApiModelProperty(value = "通道ID")
    private String chanId;

    @ApiModelProperty(value = "通道ID",hidden = true)
    private String channelId;

    @ApiModelProperty("存储类型：0: 服务器;1: 前端设备;2: 级联中心")
    private String storageType;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("月")
    private Integer month;
}
