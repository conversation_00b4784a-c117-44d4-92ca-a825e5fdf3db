package com.rs.module.base.controller.admin.video.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("设置云台预置点DTO")
public class PresetCtrlDto {

    @ApiModelProperty(value = "通道ID", hidden = true)
    private String channelId;

    @ApiModelProperty(value = "通道ID")
    private String chanId;

    @ApiModelProperty(value = "索引号 (不为空则表示修改,添加预置位时需注意索引号不能相同，相同则会进行覆盖)，该参数唯一")
    private String index;

    @ApiModelProperty(value = "预置点名称")
    private String name;

    @ApiModelProperty(value = "0:添加预置点 3:设置默认预置位")
    private Integer operType;

    @ApiModelProperty(value = "1:默认预置位 0:非默认")
    private Integer isDefault;


}
