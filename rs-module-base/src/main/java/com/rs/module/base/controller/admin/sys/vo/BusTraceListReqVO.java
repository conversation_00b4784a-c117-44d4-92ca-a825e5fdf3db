package com.rs.module.base.controller.admin.sys.vo;

import java.util.Date;

import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-业务轨迹列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BusTraceListReqVO extends BaseVO {
    @ApiModelProperty("ip地址")
    private String ip;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    
    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("操作用户")
    private String operateUser;

    @ApiModelProperty("业务类型")
    private String busType;

    @ApiModelProperty("业务名称")
    private String busName;

    @ApiModelProperty("业务内容")
    private String content;
    
    @ApiModelProperty("业务主键")
    private String businessId;
    
    @ApiModelProperty("监室Id")
    private String roomId;

    @ApiModelProperty("分组(01:业务轨迹,02:医疗轨迹,03:警员轨迹)")
    private String group;
    
    @ApiModelProperty("操作开始时间")
    private Date startTime;
    
    @ApiModelProperty("操作结束时间")
    private Date endTime;
}
