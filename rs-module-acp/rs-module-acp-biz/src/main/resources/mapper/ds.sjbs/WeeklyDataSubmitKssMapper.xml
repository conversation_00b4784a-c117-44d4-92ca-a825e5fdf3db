<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.ds.sjbs.WeeklyDataSubmitKssDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 律师会见记录 -->
    <select id="statisticNumLawyerMeeting" resultType="com.rs.module.acp.entity.ds.sjbs.WeeklyDataSubmitKssDO">

        select
        COUNT(1) AS lshj,
        org_code AS orgcode,
        COUNT(1) FILTER (WHERE meeting_method = '0') AS lshjXchj,
        COUNT(1) FILTER (WHERE meeting_method = '1') AS lshjKshj,
        COUNT(1) FILTER (WHERE meeting_method = '2') AS lshjYchj
        FROM acp_wb_lawyer_meeting la
        WHERE is_del = 0 and EXISTS (SELECT 1 FROM vw_acp_pm_prisoner_kss kks WHERE kks.jgrybm = la.jgrybm)
        <if test="orgCode != null and orgCode != ''">
            AND org_code =#{orgCode}
        </if>
        <choose>
            <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND apply_meeting_start_time BETWEEN TO_TIMESTAMP(#{startDate}, 'YYYY-MM-DD')
                AND TO_TIMESTAMP(#{endDate}, 'YYYY-MM-DD') + INTERVAL '1 day'
            </when>
            <otherwise>
                AND apply_meeting_start_time BETWEEN
                (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '7 days')
                AND (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '1 day') + INTERVAL '1 day'
            </otherwise>
        </choose>
        group by org_code
    </select>

    <!-- 看守所 提解 -->
    <select id="statisticNumTj" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        CASE tjjglx WHEN '1' THEN '公安机关' WHEN '2' THEN '检察院' WHEN '3' THEN '法院' WHEN '4' THEN '安全机关' WHEN '9' THEN '其他' ELSE '未知' END AS tjjglx_name,
        tjjglx,org_code AS orgcode,
        COUNT(*) AS count
        FROM
        (SELECT
            e.id AS escort_id,
            p.tjjglx,e.org_code
        FROM acp_wb_escort e
        JOIN acp_wb_case_personnel p ON p.id IN (e.handler1_id, e.handler2_id, e.handler3_id)
        WHERE e.is_del = 0 AND p.tjjglx IS NOT NULL
        <if test="orgCode != null and orgCode != ''">
            AND e.org_code = #{orgCode}
        </if>
        <choose>
            <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND e.apply_escort_date BETWEEN TO_TIMESTAMP(#{startDate}, 'YYYY-MM-DD')
                AND TO_TIMESTAMP(#{endDate}, 'YYYY-MM-DD') + INTERVAL '1 day'
            </when>
            <otherwise>
                AND e.apply_escort_date BETWEEN
                (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '7 days')
                AND (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '1 day') + INTERVAL '1 day'
            </otherwise>
        </choose>
        and EXISTS (SELECT 1 FROM vw_acp_pm_prisoner_kss kss WHERE kss.jgrybm = e.jgrybm)
        AND (e.handler1_id IS NOT NULL OR e.handler2_id IS NOT NULL OR e.handler3_id IS NOT NULL)
        GROUP BY e.id, p.tjjglx,e.org_code
        ) a
        GROUP BY org_code,tjjglx

    </select>


    <!-- 看守所 提讯 -->
    <select id="statisticNumTx" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            CASE tjjglx WHEN '1' THEN '公安机关' WHEN '2' THEN '检察院' WHEN '3' THEN '法院' WHEN '4' THEN '安全机关' WHEN '9' THEN '其他' ELSE '未知' END AS tjjglx_name,
            tjjglx,org_code AS orgcode,
            COUNT(*) AS count
        FROM
            (SELECT
            e.id AS escort_id,
            p.tjjglx,e.org_code
            FROM acp_wb_arraignment e
            JOIN acp_wb_case_personnel p ON p.id IN (e.handler1_id, e.handler2_id, e.handler3_id)
            WHERE e.is_del = 0 AND p.tjjglx IS NOT NULL
            and EXISTS (SELECT 1 FROM vw_acp_pm_prisoner_kss kss  WHERE kss.jgrybm = e.jgrybm)
            <if test="orgCode != null and orgCode != ''">
                AND e.org_code = #{orgCode}
            </if>
            <choose>
                <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                    AND e.start_apply_arraignment_time BETWEEN TO_TIMESTAMP(#{startDate}, 'YYYY-MM-DD')
                    AND TO_TIMESTAMP(#{endDate}, 'YYYY-MM-DD') + INTERVAL '1 day'
                </when>
                <otherwise>
                    AND e.start_apply_arraignment_time BETWEEN
                    (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '7 days')
                    AND (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '1 day') + INTERVAL '1 day'
                </otherwise>
            </choose>
            AND (e.handler1_id IS NOT NULL OR e.handler2_id IS NOT NULL OR e.handler3_id IS NOT NULL)
            GROUP BY e.id, p.tjjglx,e.org_code
            ) a
        GROUP BY org_code,tjjglx

    </select>

    <delete id="deleteByCondition">
        DELETE FROM acp_ds_weekly_data_submit_kss
        WHERE start_date = #{startDate} AND end_date = #{endDate}
        <if test="orgCode != null and orgCode != ''">
            AND org_code =#{orgCode}
        </if>
    </delete>
</mapper>
