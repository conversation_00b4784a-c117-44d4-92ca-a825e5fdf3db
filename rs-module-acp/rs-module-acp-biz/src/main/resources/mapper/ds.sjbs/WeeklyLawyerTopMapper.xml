<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.ds.sjbs.WeeklyLawyerTopDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 律师及会见次数最多十人 排名，律师姓名，会见次数 -->
    <select id="selectTop10Lawyers" resultType="com.rs.module.acp.entity.ds.sjbs.WeeklyLawyerTopDO">
        SELECT
        p.xm AS xm,
        lc.lawyer_id AS lsId,
        lc.occurrence_count AS hjcs,
        lc.org_code AS orgCode,
        CURRENT_DATE AS solidificationDate,
        (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '7 days') AS startDate,
        (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '1 day') AS endDate
        FROM
        (
        SELECT  lawyer_id, COUNT(*) AS occurrence_count,org_code
        FROM
        (
        SELECT
        json_array_elements_text(json_build_array(NULLIF(lawyer1_id, ''),NULLIF(lawyer2_id, ''),NULLIF(lawyer3_id, ''),NULLIF(lawyer4_id, ''))) AS lawyer_id,
        org_code
        FROM  acp_wb_lawyer_meeting la
        WHERE  is_del = 0 and EXISTS (SELECT 1 FROM vw_acp_pm_prisoner jls WHERE jls.jgrybm = la.jgrybm)
        <if test="orgCode != null and orgCode != ''">
            AND org_code =#{orgCode}
        </if>
        <choose>
            <when test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND apply_meeting_start_time BETWEEN TO_TIMESTAMP(#{startDate}, 'YYYY-MM-DD')
                AND TO_TIMESTAMP(#{endDate}, 'YYYY-MM-DD') + INTERVAL '1 day'
            </when>
            <otherwise>
                AND apply_meeting_start_time BETWEEN
                (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '7 days')
                AND (DATE_TRUNC('week', CURRENT_DATE)::date - INTERVAL '1 day') + INTERVAL '1 day'
            </otherwise>
        </choose>
        and lawyer1_id IS NOT NULL OR lawyer2_id IS NOT NULL OR lawyer3_id IS NOT NULL OR lawyer4_id IS NOT NULL
        )a
        WHERE lawyer_id IS NOT NULL
        GROUP BY  lawyer_id, org_code
        ) lc
        JOIN acp_wb_lawyer p ON lc.lawyer_id = p.id
        ORDER BY lc.org_code,lc.occurrence_count DESC
        LIMIT 10

    </select>

    <delete id="deleteByCondition">
        DELETE FROM acp_ds_weekly_lawyer_top
        WHERE start_date = #{startDate} AND end_date = #{endDate}
        <if test="orgCode != null and orgCode != ''">
            AND org_code =#{orgCode}
        </if>
    </delete>
</mapper>
