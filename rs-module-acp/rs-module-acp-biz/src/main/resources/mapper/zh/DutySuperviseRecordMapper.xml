<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.zh.DutySuperviseRecordDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getCurrentDutyInfo" resultType="com.rs.module.acp.controller.admin.zh.vo.AcpZhStaffDutyTemplateCurrentInfo">
        <![CDATA[
        SELECT
            AT.id AS staff_duty_template_id,
            AT.org_code,
            asr.duty_date,
            asr.id AS record_id,
            ap.post_name,
            adr.post_id,
            adr.duty_role_name,
            art.duty_shift,
            art.duty_time_begin,
            art.duty_time_end,
            art.duty_time_type_begin,
            art.duty_time_type_end,
            art.duty_role_id,
            ap.id AS staff_duty_post_id,
            adr.id AS staff_duty_role_id
        FROM
            acp_zh_staff_duty_template AT
            LEFT JOIN acp_zh_staff_duty_post ap ON ap.temp_id = AT.id
            LEFT JOIN acp_zh_staff_duty_role adr ON adr.post_id = ap.id
            LEFT JOIN acp_zh_staff_duty_role_time art ON art.duty_role_id = adr.id
            LEFT JOIN acp_zh_staff_duty_record asr ON asr.temp_id = AT.id
        WHERE
            AT.is_del = '0'
            AND AT.status = '1'
            AND AT.type = '2'
            AND AT.org_code = #{orgCode}
            AND asr.duty_date = CURRENT_DATE
            AND EXISTS (select 1 FROM acp_zh_staff_duty_record_person arp WHERE arp.record_id = asr.id)
            AND (
                (art.duty_time_begin::TIME <= art.duty_time_end::TIME AND CURRENT_TIME::TIME BETWEEN art.duty_time_begin::TIME AND art.duty_time_end::TIME)
                OR (art.duty_time_begin::TIME > art.duty_time_end::TIME AND (CURRENT_TIME::TIME >= art.duty_time_begin::TIME OR CURRENT_TIME::TIME <= art.duty_time_end::TIME))
            )
        ]]>
    </select>

</mapper>
