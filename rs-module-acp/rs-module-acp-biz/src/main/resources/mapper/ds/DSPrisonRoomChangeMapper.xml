<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.ds.DSPrisonRoomChangeDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- 查询指定人员在指定监室和时间段内的同监室人员 -->
    <select id="getPrisonRoommates" resultType="com.rs.module.acp.controller.admin.ds.vo.DSPrisonRoomChangeRespVO">
        SELECT DISTINCT
        prc2.jgrybm AS jgrybm,
        prc2.jgryxm AS jgryxm,
        prc1.room_id AS roomId,
        prc1.room_name AS roomName,
        prc2.in_room_time AS inRoomTime,
        prc2.out_room_time AS outRoomTime,
        GREATEST(prc1.in_room_time, prc2.in_room_time) AS cohabitationStart,
        LEAST(
        CASE WHEN prc1.out_room_time IS NULL THEN
        <choose>
            <when test="query.endTime != null">#{query.endTime}</when>
            <otherwise>CURRENT_TIMESTAMP</otherwise>
        </choose>
        ELSE prc1.out_room_time END,
        CASE WHEN prc2.out_room_time IS NULL THEN
        <choose>
            <when test="query.endTime != null">#{query.endTime}</when>
            <otherwise>CURRENT_TIMESTAMP</otherwise>
        </choose>
        ELSE prc2.out_room_time END
        ) AS cohabitationEnd
        FROM acp_ds_prison_room_change prc1
        INNER JOIN acp_ds_prison_room_change prc2 ON
        prc1.room_id = prc2.room_id AND
        prc1.jgrybm != prc2.jgrybm AND
        prc1.in_room_time &lt; COALESCE(prc2.out_room_time,
        <choose>
            <when test="query.endTime != null">#{query.endTime}</when>
            <otherwise>CURRENT_TIMESTAMP</otherwise>
        </choose>
        ) AND
        prc2.in_room_time &lt; COALESCE(prc1.out_room_time,
        <choose>
            <when test="query.endTime != null">#{query.endTime}</when>
            <otherwise>CURRENT_TIMESTAMP</otherwise>
        </choose>
        )
        WHERE prc1.jgrybm = #{query.jgrybm}
        <if test="query.roomId != null and query.roomId != ''">
            AND prc1.room_id = #{query.roomId}
        </if>
        AND (
        <if test="query.startTime != null and query.endTime != null">
            (prc1.in_room_time &lt;= #{query.endTime} AND COALESCE(prc1.out_room_time, #{query.endTime}) >= #{query.startTime})
            AND
            (prc2.in_room_time &lt;= #{query.endTime} AND COALESCE(prc2.out_room_time, #{query.endTime}) >= #{query.startTime})
        </if>
        <if test="query.startTime == null and query.endTime != null">
            (prc1.in_room_time &lt;= #{query.endTime} AND COALESCE(prc1.out_room_time, #{query.endTime}) >= '1900-01-01')
            AND
            (prc2.in_room_time &lt;= #{query.endTime} AND COALESCE(prc2.out_room_time, #{query.endTime}) >= '1900-01-01')
        </if>
        <if test="query.startTime != null and query.endTime == null">
            (prc1.in_room_time &lt;= CURRENT_TIMESTAMP AND COALESCE(prc1.out_room_time, CURRENT_TIMESTAMP) >= #{query.startTime})
            AND
            (prc2.in_room_time &lt;= CURRENT_TIMESTAMP AND COALESCE(prc2.out_room_time, CURRENT_TIMESTAMP) >= #{query.startTime})
        </if>
        <if test="query.startTime == null and query.endTime == null">
            (prc1.in_room_time &lt;= CURRENT_TIMESTAMP AND COALESCE(prc1.out_room_time, CURRENT_TIMESTAMP) >= '1900-01-01')
            AND
            (prc2.in_room_time &lt;= CURRENT_TIMESTAMP AND COALESCE(prc2.out_room_time, CURRENT_TIMESTAMP) >= '1900-01-01')
        </if>
        )
        ORDER BY prc2.jgrybm
    </select>

    <!-- 查询指定人员在指定监室和时间段内的同监室人员，并按共同时间段分组 -->
    <select id="getPrisonRoommatesGrouped" resultType="com.rs.module.acp.controller.admin.ds.vo.DSPrisonRoomChangeRespVO">
        SELECT DISTINCT
        prc2.jgrybm AS jgrybm,
        prc2.jgryxm AS jgryxm,
        prc1.room_id AS roomId,
        prc1.room_name AS roomName,
        prc2.in_room_time AS inRoomTime,
        prc2.out_room_time AS outRoomTime,
        GREATEST(prc1.in_room_time, prc2.in_room_time) AS cohabitationStart,
        LEAST(
        CASE WHEN prc1.out_room_time IS NULL THEN
        <choose>
            <when test="query.endTime != null">#{query.endTime}</when>
            <otherwise>CURRENT_TIMESTAMP</otherwise>
        </choose>
        ELSE prc1.out_room_time END,
        CASE WHEN prc2.out_room_time IS NULL THEN
        <choose>
            <when test="query.endTime != null">#{query.endTime}</when>
            <otherwise>CURRENT_TIMESTAMP</otherwise>
        </choose>
        ELSE prc2.out_room_time END
        ) AS cohabitationEnd
        FROM acp_ds_prison_room_change prc1
        INNER JOIN acp_ds_prison_room_change prc2 ON
        prc1.room_id = prc2.room_id AND
        prc1.jgrybm != prc2.jgrybm AND
        prc1.in_room_time &lt; COALESCE(prc2.out_room_time,
        <choose>
            <when test="query.endTime != null">#{query.endTime}</when>
            <otherwise>CURRENT_TIMESTAMP</otherwise>
        </choose>
        ) AND
        prc2.in_room_time &lt; COALESCE(prc1.out_room_time,
        <choose>
            <when test="query.endTime != null">#{query.endTime}</when>
            <otherwise>CURRENT_TIMESTAMP</otherwise>
        </choose>
        )
        WHERE prc1.jgrybm = #{query.jgrybm}
        <if test="query.roomId != null and query.roomId != ''">
            AND prc1.room_id = #{query.roomId}
        </if>
        AND (
        <if test="query.startTime != null and query.endTime != null">
            (prc1.in_room_time &lt;= #{query.endTime} AND COALESCE(prc1.out_room_time, #{query.endTime}) >= #{query.startTime})
            AND
            (prc2.in_room_time &lt;= #{query.endTime} AND COALESCE(prc2.out_room_time, #{query.endTime}) >= #{query.startTime})
        </if>
        <if test="query.startTime == null and query.endTime != null">
            (prc1.in_room_time &lt;= #{query.endTime} AND COALESCE(prc1.out_room_time, #{query.endTime}) >= '1900-01-01')
            AND
            (prc2.in_room_time &lt;= #{query.endTime} AND COALESCE(prc2.out_room_time, #{query.endTime}) >= '1900-01-01')
        </if>
        <if test="query.startTime != null and query.endTime == null">
            (prc1.in_room_time &lt;= CURRENT_TIMESTAMP AND COALESCE(prc1.out_room_time, CURRENT_TIMESTAMP) >= #{query.startTime})
            AND
            (prc2.in_room_time &lt;= CURRENT_TIMESTAMP AND COALESCE(prc2.out_room_time, CURRENT_TIMESTAMP) >= #{query.startTime})
        </if>
        <if test="query.startTime == null and query.endTime == null">
            (prc1.in_room_time &lt;= CURRENT_TIMESTAMP AND COALESCE(prc1.out_room_time, CURRENT_TIMESTAMP) >= '1900-01-01')
            AND
            (prc2.in_room_time &lt;= CURRENT_TIMESTAMP AND COALESCE(prc2.out_room_time, CURRENT_TIMESTAMP) >= '1900-01-01')
        </if>
        )
        ORDER BY cohabitationStart, cohabitationEnd, prc2.jgrybm
    </select>
</mapper>
