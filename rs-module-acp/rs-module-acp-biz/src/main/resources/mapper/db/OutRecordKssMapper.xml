<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.db.OutRecordKssDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <insert id="inserToOut">
        INSERT INTO acp_pm_prisoner_kss_out (id, is_del, add_time, add_user, add_user_name, update_time,
        update_user, update_user_name, pro_code, pro_name, city_code, city_name, reg_code, reg_name,
        org_code, org_name, jgrybm, rybh, ajbh, ryzt, xm, xmpy, bm, xb, csrq, zjlx, zjhm, gj, mz, hyzk,
        jg, hjd, hjdxz, xzz, xzzxz, whcd, zzmm, zy, gzdw, zw, zwjb, jl, sf, tssf, jkzk, sg, tz, zc, tbtsbj,
        wffzjl, cylx, sxzm, jyaq, za, zxf, tabh, badwlx, badw, bar, barlxff, sshj, gyqx, jyrq, jlrq, dbrq,
        scqsrq, ysfyrq, zzczrq, zzczjg, zxtzssdrq, xq, xqjzrq, xqqsrq, fjcl, rssj, rsyy, sydw, syr, sypz,
        sypzwsh, fxdj, gllb, jsh, sbfh, cwh, snbh, dabh, lsyy, sfhs, fwbh, cssj, csyy, csqx, jbr, jbsj,
        dwdm, bz, bgr, bgsj, tc, front_photo, left_photo, right_photo, room_name, area_id)
        SELECT id, is_del, add_time, add_user, add_user_name, update_time, update_user, update_user_name,
        pro_code, pro_name, city_code, city_name, reg_code, reg_name, org_code, org_name, jgrybm, rybh,
        ajbh, '11' ryzt, xm, xmpy, bm, xb, csrq, zjlx, zjhm, gj, mz, hyzk, jg, hjd, hjdxz, xzz, xzzxz, whcd,
        zzmm, zy, gzdw, zw, zwjb, jl, sf, tssf, jkzk, sg, tz, zc, tbtsbj, wffzjl, cylx, sxzm, jyaq, za, zxf,
        tabh, badwlx, badw, bar, barlxff, sshj, gyqx, jyrq, jlrq, dbrq, scqsrq, ysfyrq, zzczrq, zzczjg,
        zxtzssdrq, xq, xqjzrq, xqqsrq, fjcl, rssj, rsyy, sydw, syr, sypz, sypzwsh, fxdj, gllb, jsh, sbfh,
        cwh, snbh, dabh, lsyy, sfhs, fwbh, cssj, csyy, csqx, jbr, jbsj, dwdm, bz, bgr, bgsj, tc,
        front_photo, left_photo, right_photo, room_name, area_id FROM acp_pm_prisoner_kss_in
        where jgrybm= #{jgrybm}

    </insert>
    <delete id="deleteFromIn">
        delete from acp_pm_prisoner_kss_in where jgrybm= #{jgrybm}
    </delete>
    <select id="getInRecordStatus" resultType="com.rs.module.acp.controller.admin.db.vo.InRecordStatusVO">
        SELECT
            ak.current_step,
            '99' AS rsdj,
            '99' AS jkjc,
            '99' AS wpgl,
            '99' AS xxcj
        FROM
            acp_db_out_record_kss ak
        WHERE
            ak.jgrybm = #{ jgrybm }
            LIMIT 1
    </select>
</mapper>
