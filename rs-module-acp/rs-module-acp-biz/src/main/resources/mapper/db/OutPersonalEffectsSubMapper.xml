<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.db.OutPersonalEffectsSubDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
<!-- PersonalEffectsSubMapper.xml -->
    <select id="getPersonalEffectsByPersonId" parameterType="string" resultType="com.rs.module.acp.entity.db.OutPersonalEffectsSubDO">
        SELECT
            ais.ID,
            ais.personal_effects_id,
            ais.rybh jgrybm,
            ais.ryxm jgryxm,
            ais.wpmc,
            ais.zl,
            ais.hbzl,
            ais.sl,
            ais.wptz,
            ais.wpzp,
            ais.wz,
            ais.wpczqk,
            ais.xjzrgrzkrq,
            (select aos.qcwpsl from acp_db_out_personal_effects_sub aos
                where aos.jgrybm = ais.rybh and aos.is_del = 0 and aos.wpmc = ais.wpmc and aos.id = ais.id limit 1) AS qcwpsl,
            ais.bz
        FROM
            acp_db_personal_effects_sub ais
        WHERE
            ais.rybh = #{rybh} and ais.is_del = 0
    </select>

</mapper>
