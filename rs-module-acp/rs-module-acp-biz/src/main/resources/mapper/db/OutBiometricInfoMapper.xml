<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.db.OutBiometricInfoDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getByJgrybm" resultType="com.rs.module.acp.entity.db.OutBiometricInfoDO">
        SELECT
            aoi.id,
            ai.rybh AS jgrybm,
            ai.cjxmlx,
            ai.swtz,
            ai.swtzfj,
            COALESCE(NULLIF(aoi.hyjg, ''), '0') AS hyjg,
            ai.bz
        FROM
            acp_db_biometric_info ai
                LEFT JOIN acp_db_out_biometric_info aoi ON ai.rybh = aoi.jgrybm
        WHERE
            ai.rybh = #{jgrybm}
    </select>
</mapper>
