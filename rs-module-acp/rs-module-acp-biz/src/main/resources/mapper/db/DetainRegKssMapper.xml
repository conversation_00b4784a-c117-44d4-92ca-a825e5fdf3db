<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.db.DetainRegKssDao">
    <update id="updateStepInfo">
        UPDATE
            acp_db_in_record_kss
        <set>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="spzt != null and spzt != ''">
                spzt = #{spzt},
            </if>
            <if test="actInstId != null and actInstId != ''">
                act_inst_id = #{actInstId},
            </if>
            <if test="currentStep != null and currentStep != ''">
                current_step = #{currentStep}
            </if>
        </set>
        WHERE
        rybh = #{rybh}
    </update>
    <delete id="deleteExitPrisoner">
        DELETE FROM
            acp_pm_prisoner_kss_in
        WHERE
            jgrybm = #{rybh}
    </delete>
    <select id="getCombineInfo" resultType="com.rs.module.acp.controller.admin.db.vo.CombineRespVO">
        SELECT
            ak.ID,
            ak.rybh,
            ak.xm,
            ak.bm,
            ak.xb,
            ak.csrq,
            ak.ajlbdm,
            ak.ajlb,
            ak.ajbh,
            ak.rssj,
            ak.jsh,
            ak.add_time,
            ak.status,
            ac.ysyj,
            ac.jcr,
            ac.jcsj,
            ac.bz
        FROM
            acp_db_detain_reg_kss ak LEFT JOIN acp_db_health_check ac ON ak.rybh = ac.rybh
        WHERE ak.id = #{id}
    </select>

    <insert id="syncDataToPrisonerKssIn">
        INSERT INTO acp_pm_prisoner_kss_in (
            id, is_del, add_time, add_user, add_user_name, update_time, update_user, update_user_name,
            pro_code, pro_name, city_code, city_name, reg_code, reg_name,
            org_code, org_name, jgrybm, rybh, ajbh, ryzt, xm, xmpy, bm, xb, csrq,
            zjlx, zjhm, gj, mz, hyzk, jg, hjd, hjdxz, xzz, xzzxz, whcd, zzmm,
            zy, gzdw, zw, zwjb, jl, sf, tssf, jkzk, sg, tz, zc, tbtsbj, wffzjl,
            cylx, sxzm, jyaq, za, zxf, tabh, badwlx, badw, bar, barlxff, sshj,
            gyqx, jyrq, jlrq, dbrq, scqsrq, ysfyrq, zzczrq, zzczjg, zxtzssdrq, xq,
            xqjzrq, xqqsrq, fjcl, rssj, rsyy, sydw, syr, sypz, sypzwsh, fxdj,
            gllb, jsh, sbfh, cwh, snbh, dabh, lsyy, sfhs, fwbh, cssj, csyy,
            csqx, jbr, jbsj, dwdm, bz, bgr, bgsj, tc, front_photo, left_photo,
            right_photo, room_name,area_id
        )
        SELECT
            id, is_del, add_time, add_user, add_user_name, update_time, update_user, update_user_name,
            pro_code, pro_name, city_code, city_name, reg_code, reg_name,
            org_code, org_name, jgrybm, rybh, ajbh, '10' ryzt, xm, xmpy, bm, xb, csrq,
            zjlx, zjhm, gj, mz, hyzk, jg, hjd, hjdxz, xzz, xzzxz, whcd, zzmm,
            zy, gzdw, zw, zwjb, NULL, sf, tssf, NULL, NULL, NULL, NULL, NULL, NULL,
            NULL, sxzm, jyaq, NULL, zxf, tabh, badwlx, badw, bar, barlxff, sshj,
            gyqx, jyrq, jlrq, dbrq, NULL, NULL, NULL, NULL, NULL, NULL,
            NULL, NULL, NULL, rssj, rsyy, sydw, syr1, sypz, sypzwsh, NULL,
            gllb, jsh, NULL, cwh, NULL, dabh, NULL, NULL, fwbh, NULL, NULL,
            NULL, jbr, jbsj, NULL, bz, NULL, NULL, tc, front_photo, NULL,
            NULL, room_name,area_id
        FROM
            acp_db_in_record_kss where id = #{id}

    </insert>

    <select id="getInRecordStatus" parameterType="string" resultType="com.rs.module.acp.controller.admin.db.vo.InRecordStatusVO">
        SELECT
            COALESCE (ak.current_step,'01') AS current_step,
            COALESCE (ak.status,'01') AS rsdj,
            COALESCE ( ( SELECT ac.status FROM acp_db_health_check ac WHERE ak.jgrybm = ac.jgrybm LIMIT 1 ), '01' ) AS jkjc,
            COALESCE ( ( SELECT ae.status FROM acp_db_personal_effects ae WHERE ak.jgrybm = ae.rybh LIMIT 1 ), '01' ) AS wpgl,
            COALESCE (
                ( SELECT ai.status FROM acp_db_biometric_info ai WHERE ak.jgrybm = ai.jgrybm ORDER BY ai.add_time DESC LIMIT 1 ),
                '01'
            ) AS xxcj
        FROM
            acp_db_in_record_kss ak
        WHERE
            ak.jgrybm = #{jgrybm}
        UNION ALL
        SELECT
            '01' AS current_step,
            '01' AS rsdj,
            '01' AS jkjc,
            '01' AS wpgl,
            '01' AS xxcj
        WHERE
            NOT EXISTS ( SELECT 1 FROM acp_db_in_record_kss ak WHERE ak.jgrybm = #{jgrybm} )
        LIMIT 1
    </select>
    <select id="ifExistsInPrisonerKssIn" resultType="java.lang.String">
        SELECT
            id
        FROM
            acp_pm_prisoner_kss_in
        WHERE
            jgrybm = #{rybh}
        LIMIT 1
    </select>

</mapper>
