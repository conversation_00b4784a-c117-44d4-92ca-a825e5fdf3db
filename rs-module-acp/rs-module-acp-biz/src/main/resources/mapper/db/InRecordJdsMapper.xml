<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.db.InRecordJdsDao">
    <insert id="syncDataToPrisonerJdsIn">
        INSERT INTO acp_pm_prisoner_jds_in (
            ID,
            is_del,
            add_time,
            add_user,
            add_user_name,
            update_time,
            update_user,
            update_user_name,
            pro_code,
            pro_name,
            city_code,
            city_name,
            reg_code,
            reg_name,
            org_code,
            org_name,
            jgrybm,
            rybh,
            ajbh,
            xm,
            xmpy,
            bm,
            xb,
            csrq,
            zjlx,
            zjhm,
            gj,
            mz,
            hyzk,
            jg,
            hjd,
            hjdxz,
            xzz,
            xzzxz,
            whcd,
            zzmm,
            zy,
            gzdw,
            sf,
            tssf,
            rsyy,
            jsh,
            dabh,
            jbr,
            jbsj,
            bz,
            room_name,
            ryzt
        ) SELECT ID
                ,
                 is_del,
                 add_time,
                 add_user,
                 add_user_name,
                 update_time,
                 update_user,
                 update_user_name,
                 pro_code,
                 pro_name,
                 city_code,
                 city_name,
                 reg_code,
                 reg_name,
                 org_code,
                 org_name,
                 jgrybm,
                 rybh,
                 ajbh,
                 xm,
                 xmpy,
                 bm,
                 xb,
                 csrq,
                 zjlx,
                 zjhm,
                 gj,
                 mz,
                 hyzk,
                 jg,
                 hjd,
                 hjdxz,
                 xzz,
                 xzzxz,
                 whcd,
                 zzmm,
                 zy,
                 gzdw,
                 sf,
                 tssf,
                 rsyy,
                 jsh,
                 dabh,
                 jbr,
                 jbsj,
                 bz,
                 room_name,
                 '10' ryzt
        FROM
            acp_db_in_record_jds  where id = #{id}
    </insert>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getInRecordStatus" resultType="com.rs.module.acp.controller.admin.db.vo.InRecordStatusVO">
        SELECT
            COALESCE (ak.current_step,'01') AS current_step,
            COALESCE (ak.status,'01') AS rsdj,
            COALESCE ( ( SELECT ac.status FROM acp_db_health_check ac WHERE ak.jgrybm = ac.jgrybm LIMIT 1 ), '01' ) AS jkjc,
            COALESCE ( ( SELECT ae.status FROM acp_db_personal_effects ae WHERE ak.jgrybm = ae.rybh LIMIT 1 ), '01' ) AS wpgl,
            COALESCE (
                    ( SELECT ai.status FROM acp_db_biometric_info ai WHERE ak.jgrybm = ai.jgrybm ORDER BY ai.add_time DESC LIMIT 1 ),
                '01'
            ) AS xxcj
        FROM
            acp_db_in_record_jds ak
        WHERE
            ak.jgrybm = #{jgrybm}
        UNION ALL
        SELECT
            '01' AS current_step,
            '01' AS rsdj,
            '01' AS jkjc,
            '01' AS wpgl,
            '01' AS xxcj
            WHERE
            NOT EXISTS ( SELECT 1 FROM acp_db_in_record_jds ak WHERE ak.jgrybm = #{jgrybm} )
                LIMIT 1
    </select>
</mapper>
