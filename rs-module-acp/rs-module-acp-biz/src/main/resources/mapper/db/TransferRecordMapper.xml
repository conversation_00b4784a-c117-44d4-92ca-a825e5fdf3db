<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.db.TransferRecordDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- TransferPrisonerMapper.xml -->
    <select id="getPrisonersByTransferRecordId" parameterType="string" resultType="com.rs.module.acp.entity.db.TransferPrisonerInfoDO">
        SELECT
            ap.ID,
            ap.jgrybm,
            ap.jgryxm,
            ai.jsh,
            ai.room_name,
            ai.zjhm,
            ai.rssj,
            ai.gyqx,
            ai.sxzm,
            ai.badw,
            ai.sshj
        FROM
            acp_db_transfer_prisoner ap
                LEFT JOIN acp_pm_prisoner_kss_in ai ON ap.jgrybm = ai.jgrybm
        WHERE
            ap.transfer_record_id = #{transferRecordId}
    </select>


</mapper>
