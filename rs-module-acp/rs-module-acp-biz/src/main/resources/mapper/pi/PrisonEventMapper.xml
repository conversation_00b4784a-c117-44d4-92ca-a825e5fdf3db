<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.pi.PrisonEventDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    
    <sql id="OutsiderPerson">
        SELECT
            id,
            xm as name,
            zjhm,
            zp_url,
            '0' person_type
        FROM
            acp_wb_case_personnel
        where org_code = #{req.orgCode}
        union all
        SELECT
            id,
            xm as ame,
            zjhm,
            zp_url,
            '1' person_type
        FROM
            acp_wb_lawyer
        where org_code = #{req.orgCode}
        union all
        SELECT
            id,
            name,
            id_number as zjhm,
            image_url as zp_url,
            '2' person_type
        FROM
            acp_wb_social_relations
        where org_code = #{req.orgCode}
        union all
        SELECT
            id,
            name,
            id_number as zjhm,
            image_url as zp_url,
            '2' person_type
        FROM
            acp_wb_consular
        where org_code = #{req.orgCode}
        union all
        SELECT
            id,
            name,
            id_number as zjhm,
            image_url as zp_url,
            '3' person_type
        FROM
            acp_wb_consular
        where org_code = #{req.orgCode}
    </sql>
    
    <select id="getOutsiderPersonPage"  resultType="com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPersonRespVO">
        select * from (
            <include refid="OutsiderPerson"/>
          ) person
          where 1=1
          <if test="req.name != null and req.name != ''">
              person.name like concat('%',#{req.name},'%')
          </if>
    </select>

    <select id="getOutsiderPersonByIds"  resultType="com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventPersonRespVO">
        select * from (
        <include refid="OutsiderPerson"/>
        ) person
        where 1=1
        <if test="idList != null">
            person.id in
            <foreach collection="idList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
