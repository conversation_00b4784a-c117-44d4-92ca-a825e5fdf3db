<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.WbApprovaDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateTask">
        update
        <choose>
            <when test="type==1">acp_wb_bring_interrogation</when>
            <when test="type==2">acp_wb_escort</when>
            <when test="type==4">acp_wb_family_meeting</when>
            <when test="type==6">acp_wb_family_meeting_video</when>
        </choose>
        set task_id = #{taskId} where id = #{id}
    </update>

    <update id="updateStatus">
        update
        <choose>
            <when test="type==1">acp_wb_bring_interrogation</when>
            <when test="type==2">acp_wb_escort</when>
            <when test="type==4">acp_wb_family_meeting</when>
            <when test="type==6">acp_wb_family_meeting_video</when>
        </choose>
        set status = #{status} where id = #{id}
    </update>

    <select id="getApprovaInfo" resultType="com.alibaba.fastjson.JSONObject">
        select
        t1.id,
        t1.act_inst_id,
        t1.task_id,
        t1.jgrybm
        <choose>
            <when test="type==1">
                ,t1.start_apply_arraignment_time apply_meeting_time
            </when>
            <when test="type==2">
                ,to_char(t1.apply_escort_date, 'YYYY-MM-DD' ) meeting_time,

                t1.add_user_name,
                t2.xm,
                t2.room_name,
                t1.apply_escort_date apply_meeting_time
            </when>
            <when test="type==4">
                ,t1.apply_meeting_start_time apply_meeting_time
            </when>
        </choose>
        from
        <choose>
            <when test="type==1">acp_wb_bring_interrogation t1</when>
            <when test="type==2">acp_wb_escort t1 left join vw_acp_pm_prisoner_list t2 on t1.jgrybm = t2.jgrybm </when>
            <when test="type==4">acp_wb_family_meeting t1</when>
            <when test="type==6">acp_wb_family_meeting_video t1</when>
        </choose>
        where t1.id = #{id}
    </select>
</mapper>
