<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.LawyerPrisonerDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getLawyerPrisonerByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            t1.id
        FROM
            acp_wb_lawyer_prisoner t1
                LEFT JOIN acp_wb_lawyer t2 ON t1.lawyer_id = t2.ID
        where t1.status = '1' and t2.lslx = '0' and t1.jgrybm = #{jgrybm} and t1.is_del = 0 and t2.is_del = 0
    </select>
    
    <select id="getPrisonerListByLawyerId" resultType="com.rs.module.acp.controller.admin.wb.vo.LawyerPrisonerRespVO">
        SELECT
            t1.jgrybm,
            t2.xm,
            t2.room_name,
            t2.front_photo,
            t1.id,
            t1.entrust_type,
            t1.add_time,
            t1.entrust_stage,
            t1.principal,
            t1.status,
            t1.principal_id,
            t1.power_of_attorney_type,
            t1.power_of_attorney_url,
            t1.letter_number,
            t1.meeting_approval_authority,
            t1.lawyer_id
        FROM
            acp_wb_lawyer_prisoner t1
                LEFT JOIN vw_acp_pm_prisoner_list t2 ON t1.jgrybm = t2.jgrybm
        WHERE t1.lawyer_id = #{lawyerId} and t1.is_del = 0 and t2.is_del = 0 ORDER BY ADD_TIME DESC
    </select>

    <select id="checkJgryAssociation" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            count(t1.id) num
        FROM
            acp_wb_lawyer_prisoner t1
                LEFT JOIN acp_wb_lawyer t2 ON t1.lawyer_id = t2.id
        WHERE t1.jgrybm = #{jgrybm} and t1.status = '1' and t2.lslx = '0' and t1.is_del = 0 and t2.is_del = 0
    </select>

    <select id="getLawyerJsonList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            t1.lawyer_id,
            t1.power_of_attorney_url,
            t2.zyzs_url
        FROM
            acp_wb_lawyer_prisoner t1
                LEFT JOIN acp_wb_lawyer t2 ON t1.lawyer_id = t2.ID
        WHERE
            t1.status = '1'
            and t1.jgrybm = #{jgrybm}
            <if test="laywerIdList != null">
                and t1.lawyer_id in
                <foreach collection="laywerIdList" item="lawyerId" open="(" close=")" separator=",">
                #{lawyerId}
                </foreach>
            </if>
            and t1.is_del = 0
    </select>
    
</mapper>
