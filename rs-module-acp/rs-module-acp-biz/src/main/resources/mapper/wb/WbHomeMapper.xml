<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.WbHomeDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getToDayMeetingUpdates" resultType="com.alibaba.fastjson.JSONObject">
        select * from (
        SELECT
            '提讯/询' businessname,
            '0' business,
            COALESCE(SUM ( todaymeetingtotal ),0) todaymeetingtotal,
            COALESCE(SUM ( signin ),0) signin,
            COALESCE(SUM ( completed ),0) completed,
            COALESCE(SUM ( unfinished ),0) unfinished
        from (
                 SELECT
                     COUNT( 1 ) todaymeetingtotal,
                     COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
                     COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
                     COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
                 FROM
                     acp_wb_arraignment
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0

                 UNION ALL

                 SELECT
                     COUNT( 1 ) todaymeetingtotal,
                     COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
                     COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
                     COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
                 FROM
                     acp_wb_bring_interrogation
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( start_apply_arraignment_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0
             ) txtj

        UNION ALL

        SELECT
            '提解' businessname,
            '1' business,
            COUNT( 1 ) todaymeetingtotal,
            COALESCE(COUNT( 1 ),0) signin,
            COALESCE(SUM ( CASE WHEN status = '2' then 1 else 0 end ),0) completed,
            COALESCE(SUM ( CASE WHEN status != '2' then 1 else 0 end ),0) unfinished
        FROM
            acp_wb_escort
        WHERE
            org_code = #{orgCode}
          AND apply_escort_date = CURRENT_DATE
          AND is_del = 0

        UNION ALL

        SELECT
            '律师会见' businessname,
            '2' business,
            COUNT( 1 ) todaymeetingtotal,
            COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
            COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
            COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
        FROM
            acp_wb_lawyer_meeting
        WHERE
            org_code = #{orgCode}
          AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
          AND is_del = 0

        UNION ALL

        SELECT
            '家属会见' businessname,
            '3' business,
            COALESCE(SUM ( todaymeetingtotal ),0) todaymeetingtotal,
            COALESCE(SUM ( signin ),0) signin,
            COALESCE(SUM ( completed ),0) completed,
            COALESCE(SUM ( unfinished ),0) unfinished
        from (
                 SELECT
                     COUNT( 1 ) todaymeetingtotal,
                     COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
                     COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
                     COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
                 FROM
                     acp_wb_family_meeting
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0

                 UNION ALL

                 SELECT
                     COUNT( 1 ) todaymeetingtotal,
                     COALESCE(SUM ( CASE WHEN notification_meeting_date IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
                     COALESCE(SUM ( CASE WHEN status = '2' then 1 else 0 end ),0) completed,
                     COALESCE(SUM ( CASE WHEN status != '2' then 1 else 0 end ),0) unfinished
                 FROM
                     acp_wb_family_meeting_video
                 WHERE
                     org_code = #{orgCode}
                   AND to_char( notification_meeting_date, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
                   AND is_del = 0
             ) jshj

        UNION ALL

        SELECT
            '使馆领事会见' businessname,
            '4' business,
            COUNT( 1 ) todaymeetingtotal,
            COALESCE(SUM ( CASE WHEN check_in_time IS NOT NULL THEN 1 ELSE 0 END ),0) signin,
            COALESCE(SUM ( CASE WHEN status = '4' then 1 else 0 end ),0) completed,
            COALESCE(SUM ( CASE WHEN status != '4' then 1 else 0 end ),0) unfinished
        FROM
            acp_wb_consular_meeting
        WHERE
            org_code = #{orgCode}
          AND to_char( apply_meeting_start_time, 'YYYY-MM-DD' ) = to_char( CURRENT_DATE, 'YYYY-MM-DD' )
          AND is_del = 0) res
    </select>

    <select id="getLawyerMeetingList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            room_id
        FROM
            acp_wb_lawyer_meeting
        WHERE
            apply_meeting_start_time &lt; CURRENT_TIMESTAMP
          AND apply_meeting_end_time > CURRENT_TIMESTAMP
          AND status IN ( '2', '3' )
          AND is_del = 0
    </select>

</mapper>
