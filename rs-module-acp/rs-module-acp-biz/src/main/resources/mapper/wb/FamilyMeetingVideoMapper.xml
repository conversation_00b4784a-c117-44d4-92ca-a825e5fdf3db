<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.FamilyMeetingVideoDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getremoteNumbering" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        t1.id,
        case
        when family_member1_name is not null then family_member1_name
        when family_member2_name is not null then family_member2_name
        when family_member3_name is not null then family_member3_name
        else '未知' end familymembername,
        '' roomname,
        concat_ws('~', to_char(meeting_start_time,'HH24:MI'),to_char(meeting_end_time,'HH24:MI'))  applymeetingtime,
        case
        when  meeting_start_time > CURRENT_TIMESTAMP or meeting_start_time is  null then '等待会见'
        when  (CURRENT_TIMESTAMP >  meeting_start_time and CURRENT_TIMESTAMP &lt; meeting_end_time) then '会见中'
        when CURRENT_TIMESTAMP > meeting_end_time then '已结束'
        else '未知' end statusName,
        case
        when  meeting_start_time > CURRENT_TIMESTAMP or meeting_start_time is null  then '1'
        when  (CURRENT_TIMESTAMP >  meeting_start_time and CURRENT_TIMESTAMP &lt; meeting_end_time) then '2'
        when CURRENT_TIMESTAMP > meeting_end_time then '4'
        else '未知' end status
        FROM
        acp_wb_family_meeting_video t1
        where
         t1.org_code = #{orgCode}
        and to_char(notification_meeting_date,'YYYY-MM-DD') = to_char(CURRENT_DATE,'YYYY-MM-DD')
        and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - meeting_end_time)) / 60 ) &lt; 30
    </select>
    
    <select id="limitNumber" resultType="com.alibaba.fastjson.JSONObject">
        select * from (
        select
            count(id) num,
            '0' businesstype
        from acp_wb_family_meeting_video
        where
        jgrybm = #{jgrybm}
        and status = '4'
        and to_char(notification_meeting_date,'YYYY-MM') = to_char(CURRENT_DATE,'YYYY-MM')
        union all
        select
            count(id) num,
            '1' businesstype
        from acp_wb_family_meeting_video
        where
            jgrybm = #{jgrybm}
          and status != '4'
        ) t limit 1
    </select>

</mapper>
