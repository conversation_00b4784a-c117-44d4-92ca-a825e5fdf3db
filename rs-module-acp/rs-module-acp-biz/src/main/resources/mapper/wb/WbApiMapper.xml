<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.WbApiDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getMeetingNoticePage" resultType="com.rs.module.acp.controller.admin.wb.vo.MeetingNoticeVO">
        select * from (
        SELECT
        id,
        '提讯' business_type_name,
        '0' business_type,
        '现场会见' meeting_method,
        arraignment_reason reason,
        concat_ws('~', to_char(start_apply_arraignment_time,'YYYY-MM-DD HH24:MI:SS'),to_char(end_apply_arraignment_time,'YYYY-MM-DD HH24:MI:SS')) meeting_time,
        add_time
        FROM
        acp_wb_arraignment
        WHERE
        jgrybm = #{pageReqVO.jgrybm}
        <if test="pageReqVO.queryStartTime != null">
            and add_time >= #{pageReqVO.queryStartTime}
        </if>
        <if test="pageReqVO.queryEndTime != null">
            and add_time &lt; #{pageReqVO.queryEndTime}
        </if>
        and org_code = #{ pageReqVO.orgCode }
        AND is_del = 0
        union all
        SELECT
        id,
        '提询' business_type_name,
        '1' business_type,
        '现场会见' meeting_method,
        arraignment_reason reason,
        concat_ws('~', to_char(start_apply_arraignment_time,'YYYY-MM-DD HH24:MI:SS'),to_char(end_apply_arraignment_time,'YYYY-MM-DD HH24:MI:SS')) meeting_time,
        add_time
        FROM
        acp_wb_bring_interrogation
        WHERE
        jgrybm = #{pageReqVO.jgrybm}
        <if test="pageReqVO.queryStartTime != null">
            and add_time >= #{pageReqVO.queryStartTime}
        </if>
        <if test="pageReqVO.queryEndTime != null">
            and add_time &lt; #{pageReqVO.queryEndTime}
        </if>
        and org_code = #{ pageReqVO.orgCode }
        AND is_del = 0
        union all
        SELECT
        id,
        '提解' business_type_name,
        '2' business_type,
        '/' meeting_method,
        escort_reason reason,
        to_char(apply_escort_date,'YYYY-MM-DD') meeting_time,
        add_time
        FROM
        acp_wb_escort
        WHERE
        jgrybm = #{pageReqVO.jgrybm}
        <if test="pageReqVO.queryStartTime != null">
            and add_time >= #{pageReqVO.queryStartTime}
        </if>
        <if test="pageReqVO.queryEndTime != null">
            and add_time &lt; #{pageReqVO.queryEndTime}
        </if>
        and org_code = #{ pageReqVO.orgCode }
        AND is_del = 0
        union all
        SELECT
        id,
        '律师会见' business_type_name,
        '3' business_type,
        case when meeting_method = '0' or meeting_method = '1' then '现场会见'
        else '远程会见' end meeting_method,
        '/' reason,
        concat_ws('~', to_char(apply_meeting_start_time,'YYYY-MM-DD HH24:MI:SS'),to_char(apply_meeting_end_time,'YYYY-MM-DD HH24:MI:SS')) meeting_time,
        add_time
        FROM
        acp_wb_lawyer_meeting
        WHERE
        jgrybm = #{pageReqVO.jgrybm}
        <if test="pageReqVO.queryStartTime != null">
            and add_time >= #{pageReqVO.queryStartTime}
        </if>
        <if test="pageReqVO.queryEndTime != null">
            and add_time &lt; #{pageReqVO.queryEndTime}
        </if>
        and org_code = #{ pageReqVO.orgCode }
        AND is_del = 0
        union all
        SELECT
        id,
        '家属当面会见' business_type_name,
        '4' business_type,
        '现场会见' meeting_method,
        '/' reason,
        concat_ws('~', to_char(apply_meeting_start_time,'YYYY-MM-DD HH24:MI:SS'),to_char(apply_meeting_end_time,'YYYY-MM-DD HH24:MI:SS')) meeting_time,
        add_time
        FROM
        acp_wb_family_meeting
        WHERE
        jgrybm = #{pageReqVO.jgrybm}
        <if test="pageReqVO.queryStartTime != null">
            and add_time >= #{pageReqVO.queryStartTime}
        </if>
        <if test="pageReqVO.queryEndTime != null">
            and add_time &lt; #{pageReqVO.queryEndTime}
        </if>
        and org_code = #{ pageReqVO.orgCode }
        AND is_del = 0
        union all
        SELECT
        id,
        '领事会见' business_type_name,
        '5' business_type,
        case when meeting_method = '0' then '现场会见'
        else '远程会见' end meeting_method,
        '/' reason,
        concat_ws('~', to_char(apply_meeting_start_time,'YYYY-MM-DD HH24:MI:SS'),to_char(apply_meeting_end_time,'YYYY-MM-DD HH24:MI:SS')) meeting_time,
        add_time
        FROM
        acp_wb_consular_meeting
        WHERE
        jgrybm = #{pageReqVO.jgrybm}
        <if test="pageReqVO.queryStartTime != null">
            and add_time >= #{pageReqVO.queryStartTime}
        </if>
        <if test="pageReqVO.queryEndTime != null">
            and add_time &lt; #{pageReqVO.queryEndTime}
        </if>
        and org_code = #{ pageReqVO.orgCode }
        AND is_del = 0
        union all
        SELECT
        id,
        '家属单向视频会见' business_type_name,
        '6' business_type,
        '远程会见' meeting_method,
        '/' reason,
        concat_ws('~', to_char(meeting_start_time,'YYYY-MM-DD HH24:MI:SS'),to_char(meeting_end_time,'YYYY-MM-DD HH24:MI:SS')) meeting_time,
        add_time
        FROM
        acp_wb_consular_meeting
        WHERE
        jgrybm = #{pageReqVO.jgrybm}
        <if test="pageReqVO.queryStartTime != null">
            and add_time >= #{pageReqVO.queryStartTime}
        </if>
        <if test="pageReqVO.queryEndTime != null">
            and add_time &lt; #{pageReqVO.queryEndTime}
        </if>
        and org_code = #{ pageReqVO.orgCode }
        AND is_del = 0 ) t order by t.add_time desc
    </select>

</mapper>
