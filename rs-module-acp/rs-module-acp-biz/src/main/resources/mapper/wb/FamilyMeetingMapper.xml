<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.FamilyMeetingDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getOrgTypeByOrgCode" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            org_type
        FROM
            acp_pm_org
        WHERE
            ID = #{orgCode}
    </select>

    <select id="getOnSiteNumbering" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        t1.id,
        case
        when family_member1_name is not null then family_member1_name
        when family_member2_name is not null then family_member2_name
        when family_member3_name is not null then family_member3_name
        else '未知' end familymembername,
        COALESCE(t2.area_name,'') roomname,
        concat_ws('~', to_char(apply_meeting_start_time,'HH24:MI'),to_char(apply_meeting_end_time,'HH24:MI')) applymeetingtime,
        case
        when t1.status = '0' and apply_meeting_start_time > CURRENT_TIMESTAMP then '待签到'
        when t1.status in('1','2','3') and apply_meeting_start_time > CURRENT_TIMESTAMP then '等待会见'
        when t1.status in('1','2','3') and (CURRENT_TIMESTAMP > apply_meeting_start_time and CURRENT_TIMESTAMP &lt;  apply_meeting_end_time) then '会见中'
        when CURRENT_TIMESTAMP > apply_meeting_end_time then '已结束'
        when t1.status = '0' and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_start_time)) / 60 ) >= 15 then
        '已逾期'
        when t1.status = '0' and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_start_time)) / 60 ) &lt; 15 then
        '待签到'
        else '未知' end statusName,
        case
        when t1.status = '0' and apply_meeting_start_time > CURRENT_TIMESTAMP then '0'
        when t1.status in('1','2','3') and apply_meeting_start_time > CURRENT_TIMESTAMP then '1'
        when t1.status in('1','2','3') and (CURRENT_TIMESTAMP > apply_meeting_start_time and CURRENT_TIMESTAMP &lt;
        apply_meeting_end_time) then '2'
        when CURRENT_TIMESTAMP > apply_meeting_end_time then '4'
        when t1.status = '0' and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_start_time)) / 60 ) >= 15 then
        '3'
        when t1.status = '0' and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_start_time)) / 60 ) &lt; 15 then
        '0'
        else '未知' end status
        FROM
        acp_wb_family_meeting t1
        LEFT JOIN acp_pm_area t2 ON t1.room_id = t2.area_code
        where
        t1.org_code = #{orgCode}
        and to_char(apply_meeting_start_time,'YYYY-MM-DD') = to_char(CURRENT_DATE,'YYYY-MM-DD')

        and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_end_time)) / 60 ) &lt; 30
    </select>
</mapper>
