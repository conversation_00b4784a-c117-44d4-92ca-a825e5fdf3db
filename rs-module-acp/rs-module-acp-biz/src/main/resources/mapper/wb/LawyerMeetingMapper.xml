<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.LawyerMeetingDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getPrisonerListByAjbh" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            jgrybm,
            xm,
            zjhm
        FROM
            vw_acp_pm_prisoner_list
        WHERE
            ajbh = #{ajbh} and org_code = #{orgCode}
    </select>

    <select id="timeOverlapJudgment" resultType="com.alibaba.fastjson.JSONObject">
        SELECT * FROM(
        SELECT
            '提讯/询' businesstype,
            start_apply_arraignment_time starttime,
            end_apply_arraignment_time endtime
        FROM
            acp_wb_arraignment
        WHERE
            end_apply_arraignment_time > TO_TIMESTAMP( #{startTime}, 'YYYY-MM-DD HH24:MI:SS')
          AND status IN ( '0', '1', '2', '3' ) and is_del = 0
        UNION ALL
        SELECT
            '提解' businesstype,
            TO_TIMESTAMP(concat(to_char(apply_escort_date,'YYYY-MM-DD'),to_char(add_time,'HH24:MI:SS')), 'YYYY-MM-DD HH24:MI:SS') starttime,
            TO_TIMESTAMP(concat(to_char(apply_escort_date,'YYYY-MM-DD'),COALESCE(to_char(return_time,'HH24:MI:SS'),'23:59:59')), 'YYYY-MM-DD HH24:MI:SS') endtime
        FROM
            acp_wb_escort
        WHERE
                TO_TIMESTAMP(concat(to_char(apply_escort_date,'YYYY-MM-DD'),COALESCE(to_char(return_time,'HH24:MI:SS'),'23:59:59')), 'YYYY-MM-DD HH24:MI:SS') > TO_TIMESTAMP( #{startTime}, 'YYYY-MM-DD HH24:MI:SS')
          AND status IN ( '0', '1' ) and is_del = 0
        UNION ALL
        SELECT
            '律师会见' businesstype,
            apply_meeting_start_time starttime,
            apply_meeting_end_time endtime
        FROM
            acp_wb_lawyer_meeting
        WHERE
            apply_meeting_end_time > TO_TIMESTAMP( #{startTime}, 'YYYY-MM-DD HH24:MI:SS')
          AND status IN ( '0', '1', '2', '3' ) and is_del = 0) t
    </select>

    <select id="getHistoryMeetingByLwayerId" resultType="com.rs.module.acp.controller.admin.wb.vo.LawyerMeetingRespVO">
        SELECT
            t2.xm,
            t2.room_name,
            t1.jgrybm,
            t2.xb,
            t2.sxzm,
            t2.sshj,
            t2.gyqx,
            concat_ws('、', t1.lawyer1_principal_name,t1.lawyer2_principal_name,t1.lawyer3_principal_name,t1.lawyer4_principal_name) lawyer_principal_name,
            t1.room_id,
            t1.meeting_start_time,
            t1.meeting_end_time
        FROM
            acp_wb_lawyer_meeting t1
                LEFT JOIN vw_acp_pm_prisoner_list t2 ON t1.jgrybm = t2.jgrybm
        WHERE
            t1.is_del = '0'
          AND ( t1.lawyer1_id = #{lawyerId} OR t1.lawyer2_id = #{lawyerId} OR t1.lawyer3_id = #{lawyerId} OR t1.lawyer4_id = #{lawyerId})
    </select>

    <select id="getOnSiteNumbering" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        t1.id,
        case
        when lawyer1_type = '0' then lawyer1_name
        when lawyer2_type = '0' then lawyer2_name
        when lawyer3_type = '0' then lawyer3_name
        when lawyer4_type = '0' then lawyer4_name
        else lawyer1_name end lawyername,
        COALESCE(t2.area_name,'') roomname,
        concat_ws('~', to_char(apply_meeting_start_time,'HH24:MI'),to_char(apply_meeting_end_time,'HH24:MI'))  applymeetingtime,
        case
        when t1.status = '0' and apply_meeting_start_time > CURRENT_TIMESTAMP then '待签到'
        when t1.status in('1','2','3') and apply_meeting_start_time > CURRENT_TIMESTAMP then '等待会见'
        when t1.status in('1','2','3') and (CURRENT_TIMESTAMP >  apply_meeting_start_time and CURRENT_TIMESTAMP &lt; apply_meeting_end_time) then '会见中'
        when (CURRENT_TIMESTAMP > apply_meeting_end_time or t1.status = '4') then '已结束'
        when t1.status = '0' and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_start_time)) / 60 ) >= 15 then '已逾期'
        when t1.status = '0' and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_start_time)) / 60 ) &lt; 15 then '待签到'
        else '未知' end statusName,
        case
        when t1.status = '0' and apply_meeting_start_time > CURRENT_TIMESTAMP then '0'
        when t1.status in('1','2','3') and apply_meeting_start_time > CURRENT_TIMESTAMP then '1'
        when t1.status in('1','2','3') and (CURRENT_TIMESTAMP >  apply_meeting_start_time and CURRENT_TIMESTAMP &lt; apply_meeting_end_time) then '2'
        when (CURRENT_TIMESTAMP > apply_meeting_end_time or t1.status = '4') then '4'
        when t1.status = '0' and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_start_time)) / 60 ) >= 15 then '3'
        when t1.status = '0' and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_start_time)) / 60 ) &lt; 15 then '0'
        else '未知' end status
        FROM
        acp_wb_lawyer_meeting t1
        LEFT JOIN acp_pm_area t2 ON t1.room_id = t2.area_code
        where
        t1.meeting_method in('0','1') and t1.org_code = #{orgCode}
        and
        <if test=" type == 'morning'">
        apply_meeting_start_time > TO_TIMESTAMP(concat(to_char(CURRENT_DATE,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS')
        and apply_meeting_start_time &lt; TO_TIMESTAMP(concat(to_char(CURRENT_DATE,'YYYY-MM-DD'),' 12:00:00'), 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test=" type == 'afternoon'">
        apply_meeting_start_time > TO_TIMESTAMP(concat(to_char(CURRENT_DATE,'YYYY-MM-DD'),' 12:00:00'), 'YYYY-MM-DD HH24:MI:SS')
        and apply_meeting_start_time &lt; TO_TIMESTAMP(concat(to_char(CURRENT_DATE,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')
        </if>
        and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_end_time)) / 60 ) &lt; 30
    </select>

    <select id="getremoteNumbering" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        t1.id,
        case
        when lawyer1_type = '0' then lawyer1_name
        when lawyer2_type = '0' then lawyer2_name
        when lawyer3_type = '0' then lawyer3_name
        when lawyer4_type = '0' then lawyer4_name
        else lawyer1_name end lawyername,
        COALESCE(t2.area_name,'') roomname,
        concat_ws('~', to_char(apply_meeting_start_time,'HH24:MI'),to_char(apply_meeting_end_time,'HH24:MI'))  applymeetingtime,
        case
        when  apply_meeting_start_time > CURRENT_TIMESTAMP then '等待会见'
        when  (CURRENT_TIMESTAMP >  apply_meeting_start_time and CURRENT_TIMESTAMP &lt; apply_meeting_end_time) then '会见中'
        when (CURRENT_TIMESTAMP > apply_meeting_end_time or t1.status = '4') then '已结束'
        else '未知' end statusName,
        case
        when  apply_meeting_start_time > CURRENT_TIMESTAMP then '1'
        when  (CURRENT_TIMESTAMP >  apply_meeting_start_time and CURRENT_TIMESTAMP &lt; apply_meeting_end_time) then '2'
        when (CURRENT_TIMESTAMP > apply_meeting_end_time or t1.status = '4') then '4'
        else '未知' end status
        FROM
        acp_wb_lawyer_meeting t1
        LEFT JOIN acp_pm_area t2 ON t1.room_id = t2.area_code
        where
        t1.meeting_method = '2'  and t1.org_code = #{orgCode}
        and
        <if test=" type == 'morning'">
            apply_meeting_start_time > TO_TIMESTAMP(concat(to_char(CURRENT_DATE,'YYYY-MM-DD'),' 00:00:00'), 'YYYY-MM-DD HH24:MI:SS')
            and apply_meeting_start_time &lt; TO_TIMESTAMP(concat(to_char(CURRENT_DATE,'YYYY-MM-DD'),' 12:00:00'), 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test=" type == 'afternoon'">
            apply_meeting_start_time > TO_TIMESTAMP(concat(to_char(CURRENT_DATE,'YYYY-MM-DD'),' 12:00:00'), 'YYYY-MM-DD HH24:MI:SS')
            and apply_meeting_start_time &lt; TO_TIMESTAMP(concat(to_char(CURRENT_DATE,'YYYY-MM-DD'),' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')
        </if>
        and (EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - apply_meeting_end_time)) / 60 ) &lt; 30
    </select>
</mapper>
