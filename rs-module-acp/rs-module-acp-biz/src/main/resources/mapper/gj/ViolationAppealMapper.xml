<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.ViolationAppealDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getOneById" parameterType="string" resultType="com.rs.module.acp.entity.gj.ViolationAppealDO">
        SELECT
            b.id,
            b.is_del,
            b.add_time,
            b.add_user,
            b.add_user_name,
            b.update_time,
            b.update_user,
            b.update_user_name,
            b.pro_code,
            b.pro_name,
            b.city_code,
            b.city_name,
            b.reg_code,
            b.reg_name,
            b.org_code,
            b.org_name,
            a.id as violation_record_id,
            b.appeal_reason,
            b.appeal_requirement,
            b.status,
            b.approver_sfzh,
            b.approver_xm,
            b.approver_time,
            b.approval_result,
            b.approval_comments,
            b.act_inst_id,
            b.task_id,
            a.violation_content,
            a.push_content,
            a.jgrybm,
            a.jgryxm,
            a.room_id,
            a.room_name
        FROM "acp_pi_violation_record" a inner join acp_gj_violation_appeal b on a.id = b.violation_record_id
        where a.is_del=0 and b.is_del=0 and b.id =#{id}
    </select>

    <select id="getListByIds" parameterType="list" resultType="com.rs.module.acp.entity.gj.ViolationAppealDO">
        SELECT
            b.id,
            b.is_del,
            b.add_time,
            b.add_user,
            b.add_user_name,
            b.update_time,
            b.update_user,
            b.update_user_name,
            b.pro_code,
            b.pro_name,
            b.city_code,
            b.city_name,
            b.reg_code,
            b.reg_name,
            b.org_code,
            b.org_name,
            a.id as violation_record_id,
            b.appeal_reason,
            b.appeal_requirement,
            b.status,
            b.approver_sfzh,
            b.approver_xm,
            b.approver_time,
            b.approval_result,
            b.approval_comments,
            b.act_inst_id,
            b.task_id,
            a.violation_content,
            a.push_content,
            a.jgrybm,
            a.jgryxm,
            a.room_id,
            a.room_name
        FROM "acp_pi_violation_record" a inner join acp_gj_violation_appeal b on a.id = b.violation_record_id
        where a.is_del=0 and b.is_del=0
        and b.id  in (<foreach collection="list" item="item" separator=",">#{item}</foreach>)
    </select>

    <select id="applyList" parameterType="string" resultType="com.rs.module.acp.entity.gj.ViolationAppealDO">
        SELECT
            b.id,
            b.is_del,
            b.add_time,
            b.add_user,
            b.add_user_name,
            b.update_time,
            b.update_user,
            b.update_user_name,
            b.pro_code,
            b.pro_name,
            b.city_code,
            b.city_name,
            b.reg_code,
            b.reg_name,
            b.org_code,
            b.org_name,
            a.id as violation_record_id,
            b.appeal_reason,
            b.appeal_requirement,
            b.status,
            b.approver_sfzh,
            b.approver_xm,
            b.approver_time,
            b.approval_result,
            b.approval_comments,
            b.act_inst_id,
            b.task_id,
            a.violation_content,
            a.push_content,
            a.jgrybm,
            a.jgryxm,
            a.room_id,
            a.room_name,
			(case when b.id is null then '02' else '03' end) as sszt,
			a.add_time as violation_time
        FROM "acp_pi_violation_record" a left join acp_gj_violation_appeal b on a.id = b.violation_record_id
        where a.is_del=0  and a.jgrybm = #{jgrybm}
        <if test="type=='02'">
            and b.id is null
        </if>
        <if test="type=='03'">
            and b.id is not null
        </if>
        order by a.add_time desc
    </select>

    <select id="applyRecord" resultType="com.rs.module.acp.entity.gj.ViolationAppealDO">
        SELECT
            b.id,
            b.is_del,
            b.add_time,
            b.add_user,
            b.add_user_name,
            b.update_time,
            b.update_user,
            b.update_user_name,
            b.pro_code,
            b.pro_name,
            b.city_code,
            b.city_name,
            b.reg_code,
            b.reg_name,
            b.org_code,
            b.org_name,
            a.id as violation_record_id,
            b.appeal_reason,
            b.appeal_requirement,
            b.status,
            b.approver_sfzh,
            b.approver_xm,
            b.approver_time,
            b.approval_result,
            b.approval_comments,
            b.act_inst_id,
            b.task_id,
            a.violation_content,
            a.push_content,
            a.jgrybm,
            a.jgryxm,
            a.room_id,
            a.room_name,
			a.add_time as violation_time
        FROM "acp_pi_violation_record" a inner join acp_gj_violation_appeal b on a.id = b.violation_record_id
        where a.is_del=0  and a.jgrybm = #{jgrybm}
        <if test="startTime!=null">
            and b.add_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null">
            and b.add_time &lt; #{endTime}
        </if>
        order by b.add_time desc
    </select>


</mapper>
