<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.PunishmentDao">



    <update id="updatePenaltyRemainsLifted">
        update  acp_gj_punishment set status = '06'  WHERE is_del =0 and end_date is not null and end_date &lt;= #{nowDate}  and status in( '05','02','03')
    </update>


</mapper>
