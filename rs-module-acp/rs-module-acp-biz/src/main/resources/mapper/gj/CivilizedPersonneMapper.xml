<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.CivilizedPersonneDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getNowPushInfo" resultType="com.rs.module.acp.entity.gj.CivilizedPersonneDO">
        select max(pro_code) pro_code, max(pro_name) pro_name, max(city_code) city_code,
        max(city_name) city_name, max(reg_code) reg_code, max(reg_name) reg_name, org_code,
        max(org_name) org_name, TO_CHAR(CURRENT_DATE, 'YYYY-MM') as evalMonth, '01' as status
        from acp_pm_area where is_del = 0 GROUP BY org_code
    </select>

</mapper>
