<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.PrisonRoomChangeDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <update id="updatePersonRoomInfoKss">
        update acp_pm_prisoner_kss_in set  jsh = #{jsh}, room_name = #{roomName}, area_Id = #{areaId} WHERE jgrybm = #{jgrybm}
    </update>

    <update id="updatePersonRoomInfoJds">
        update acp_pm_prisoner_jds_in set  jsh = #{jsh}, room_name = #{roomName}, area_Id = #{areaId} WHERE jgrybm = #{jgrybm}
    </update>

    <update id="updatePersonRoomInfoJls">
        update acp_pm_prisoner_jls_in set  jsh = #{jsh}, room_name = #{roomName}, area_Id = #{areaId} WHERE jgrybm = #{jgrybm}
    </update>


    <select id="getPrisonRoomChangeAppList"
            resultType="com.rs.module.acp.controller.app.gj.vo.prisonroom.PrisonRoomChangeAppListVO">

        SELECT
            t1.id,
            t1.add_time,
            t1.add_user_name,
            t1.old_room_name,
            t1.new_room_name,
            t1.change_reason,
            t1.room_change_time,
            t1.is_change,
            t1.jgrybm,
            t1.status,
            t1.approver_time,
            t2.xb as sex,
            t2.xm as jgryxm,
            t2.front_photo,t2.csrq
        FROM
            acp_gj_prison_room_change t1
                LEFT JOIN vw_acp_pm_prisoner_list t2 ON t1.jgrybm = t2.jgrybm
        where t1.is_del=0
        <if test="orgCode != null and orgCode != '' ">
            and t1.org_code = #{orgCode}
        </if>
        <if test="roomId != null and roomId != '' ">
            and t1.old_room_id = #{roomId}
        </if>

        <if test="startTime != null">
            and  t1.add_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and t1.add_time &lt;= #{endTime}
        </if>

    </select>

</mapper>
