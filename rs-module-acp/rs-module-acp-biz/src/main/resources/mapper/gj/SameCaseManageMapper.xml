<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.SameCaseManageDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getSystemTarAjbh" parameterType="string" resultType="string">
        select ajbh from vw_acp_pm_prisoner_in  where jgrybm = #{jgrybm}
    </select>

    <select id="manageListPage" parameterType="string"
            resultType="com.rs.module.acp.service.gj.samecasemanage.bo.SameCaseManageBO">
        select d.* from (
        <if test="ajbh !=null">
            select a.id, a.ajbh, a.jgrybm, a.xm, a.xb, a.sxzm, a.rssj, a.gyqx, a.sshj, a.badw, a.add_time, '0' as tar_type
            from vw_acp_pm_prisoner_in a where a.is_del = 0 and a.ajbh = #{ajbh}
            union all
        </if>
        select a.id, b.ajbh, b.jgrybm, b.xm, b.xb, b.sxzm, b.rssj, b.gyqx, b.sshj, b.badw, a.add_time, '1' as tar_type
        from (select same_case_jgrybm, add_time, id from acp_gj_same_case_manage where is_del = 0 and jgrybm=#{jgrybm}) a
        left join vw_acp_pm_prisoner_in b on a.same_case_jgrybm = b.jgrybm
        ) d ORDER BY d.add_time desc
    </select>

</mapper>
