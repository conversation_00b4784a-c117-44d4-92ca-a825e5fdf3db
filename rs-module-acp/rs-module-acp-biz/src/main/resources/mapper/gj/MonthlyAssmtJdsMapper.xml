<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.MonthlyAssmtJdsDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getMonthlyAssmtJdsListByInJds" resultType="com.rs.module.acp.entity.gj.MonthlyAssmtJdsDO">
        select
        pro_code,
        pro_name,
        city_code,
        city_name,
        reg_code,
        reg_name,
        org_code,
        org_name,
        a.jgrybm,
        xm as jgryxm,
        jsh as room_id,
        room_name,
        TO_CHAR(CURRENT_DATE, 'YYYY-MM') as month_period
        from acp_pm_prisoner_jds_in a left join
        (select jgrybm from acp_gj_monthly_assmt_jds where is_del = 0 and month_period = TO_CHAR(CURRENT_DATE, 'YYYY-MM'))	b on a.jgrybm = b.jgrybm
        where b.jgrybm is null and is_del = 0  and rssj + INTERVAL ' 10 day' &lt; CURRENT_DATE
    </select>

</mapper>
