<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.GroupInOutDao">

    <delete id="deleteRoom">
        DELETE FROM acp_gj_group_in_out_room WHERE group_in_out_id = #{groupInOutId}
    </delete>
    <delete id="deleteJgry">
        DELETE FROM acp_gj_group_in_out_prisoner WHERE group_in_out_id = #{groupInOutId}
    </delete>

    <delete id="delete">
        DELETE FROM acp_gj_group_in_out WHERE id = #{groupInOutId}
    </delete>
</mapper>
