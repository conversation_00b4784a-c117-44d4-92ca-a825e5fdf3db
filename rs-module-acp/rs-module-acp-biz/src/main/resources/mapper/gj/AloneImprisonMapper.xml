<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.AloneImprisonDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getAloneImprisonRoomList" resultType="com.rs.module.base.entity.pm.AreaPrisonRoomDO">
        select * from acp_pm_area_prison_room t
        where is_del=0 and t.room_type='19' and status='1' and org_code=#{orgCode}
        and not exists (select 1 from acp_gj_alone_imprison where new_room_id = t.id and is_del=0 and status in('1','3'))
        <if test="nowRoomId != null">
            and t.id != #{nowRoomId}
        </if>
    </select>
</mapper>
