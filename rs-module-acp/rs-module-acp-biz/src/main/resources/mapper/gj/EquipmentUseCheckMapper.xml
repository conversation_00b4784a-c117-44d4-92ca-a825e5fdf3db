<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.EquipmentUseCheckDao">

    <select id="getCheckList" resultType="com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO">
        select t1.id,t1.equipment_use_id,t1.check_time,t1.check_user_sfzh,t1.check_user,t1.check_result,
               t1.check_situation,t1.status,t3.jgrybm,t3.xm,t3.xb,t3.front_photo,date_part('year', age(t3.csrq)) age
        from acp_gj_equipment_use_check t1
        left join acp_gj_equipment_use t2 on t1.equipment_use_id = t2.id and t2.is_del = 0
        left join vw_acp_pm_prisoner_in t3 on t2.jgrybm = t3.jgrybm and t3.is_del = 0
        where t1.is_del = 0
        and t1.equipment_use_id = #{useId}
    </select>

    <select id="getCheckByRoomCode" resultType="com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO">
        select t1.id,t1.equipment_use_id,t1.check_time,t1.check_user_sfzh,t1.check_user,t1.check_result,
               t1.check_situation,t1.status,t3.jgrybm,t3.xm,t3.xb,t3.front_photo,date_part('year', age(t3.csrq)) age
        from acp_gj_equipment_use_check t1
                 left join acp_gj_equipment_use t2 on t1.equipment_use_id = t2.id and t2.is_del = 0
                 left join vw_acp_pm_prisoner_in t3 on t2.jgrybm = t3.jgrybm and t3.is_del = 0
        where t1.is_del = 0
          and t1.status = '0'
          and check_time::date = CURRENT_DATE
          and t3.jsh = #{roomCode}
    </select>

    <select id="getCheckRecordList" resultType="com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO">
        SELECT t2.id,t1.org_code,t1.org_name,t1.jgrybm,t3.xm,t3.xb,t3.jsh,t3.room_name,t1.punishment_tool_type,
        t1.use_days,t2.equipment_use_id,t2.check_user,t2.check_result,t2.check_situation,t2.status,t2.check_time
        FROM
            acp_gj_equipment_use t1
                LEFT JOIN acp_gj_equipment_use_check t2 ON t1.ID = t2.equipment_use_id AND t2.is_del = 0
                LEFT JOIN vw_acp_pm_prisoner_in t3 ON t1.jgrybm = t3.jgrybm AND t3.is_del = 0
        WHERE
            t2.status = '1'
        <if test="roomCode != null and roomCode != ''">
            AND t3.jsh = #{roomCode}
        </if>
        <if test="startTime != null and endTime != null">
            AND t2.check_time between #{startTime} and #{endTime}
        </if>
    </select>

    <select id="getCheckList" resultType="com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO">
        select t1.id,t1.equipment_use_id,t1.check_time,t1.check_user_sfzh,t1.check_user,t1.check_result,
               t1.check_situation,t1.status,t3.jgrybm,t3.xm,t3.front_photo,date_part('year', age(t3.csrq)) age
        from acp_gj_equipment_use_check t1
                 left join acp_gj_equipment_use t2 on t1.equipment_use_id = t2.id and t2.is_del = 0
                 left join vw_acp_pm_prisoner_in t3 on t2.jgrybm = t3.jgrybm and t3.is_del = 0
        where t1.is_del = 0
          and t1.equipment_use_id = #{useId}
    </select>

    <select id="getByUseIdAndDate" resultType="com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO">
        select * from acp_gj_equipment_use_check
        where is_del = 0
        and equipment_use_id = #{useId}
        and DATE(check_time) = DATE(#{date})
    </select>

</mapper>
