<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.EquipmentUseDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateRestraintsExaminationRemainsLifted">
        update  acp_gj_equipment_use set status = '07'  WHERE is_del =0 and end_time is not null and end_time &lt;= #{nowDate}  and status = '06'
    </update>


    <select id="getWearEquipmentUse"
            resultType="com.rs.module.acp.controller.admin.gj.vo.equipment.EquipmentUseRespWearVO">
        SELECT t.*,list.jsh as room_id, list.room_name,list.xm as jgryxm, (select area_id from acp_pm_area_ref_room where room_id = list.jsh and rel_type = '0038') as divisionControlAreaId from acp_gj_equipment_use t
         INNER JOIN vw_acp_pm_prisoner_list list on list.is_del = 0 and t.jgrybm =  list.jgrybm
        WHERE t.is_del = 0 and t.status in ('07','04','03','06')

          <if test="startTime != null">
              and  t.add_time &gt;= #{startTime}
          </if>

          <if test="endTime != null">
              and t.add_time &lt;= #{endTime}
          </if>

    </select>

</mapper>
