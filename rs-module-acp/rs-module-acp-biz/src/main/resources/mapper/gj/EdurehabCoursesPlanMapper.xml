<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.EdurehabCoursesPlanDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getPlanAreaByOrgCode" parameterType="string" resultType="com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.JqAreaVO">
        select a.id as area_id, a.area_name from acp_pm_area a
        inner join acp_pm_prisoner_jds_in b on a.id = b.area_id
        where a.is_del = 0 and b.is_del = 0 and a.org_code = #{orgCode}
        GROUP BY a.id ORDER BY a.area_name
    </select>

</mapper>
