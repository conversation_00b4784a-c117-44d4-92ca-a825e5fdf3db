<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.DiagnosisAssmtJdsDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="handleDiagnosisAssmtJds" resultType="com.rs.module.acp.entity.gj.DiagnosisAssmtJdsDO">
        select
        a.xm as jgryxm,
        a.jgrybm,
        a.room_name,
        a.jsh as room_id,
        #{assmtType} as assmt_type,
        #{periodType} as assmt_period,
        a.pro_code,
        a.pro_name,
        a.city_code,
        a.city_name,
        a.reg_code,
        a.reg_name,
        a.org_code,
        a.org_name,
        now() as add_time,
        now() as update_time,
        '01' as status
        from  acp_pm_prisoner_jds_in a left join
        (select jgrybm from acp_gj_diagnosis_assmt_jds where is_del = 0
            and assmt_type = #{assmtType} and assmt_period = #{periodType}
        ) b on a.jgrybm = b.jgrybm
        where  rssj + ${sqlTime} &lt;= now()  and  b.jgrybm is null
    </select>


    <select id="getJgryxxByJgrybm" parameterType="string" resultType="com.alibaba.fastjson.JSONObject">
        select a.*,
        (to_char(a.jlqsrq,'YYYY年MM月DD日') || '至' ||to_char(a.jljzrq,'YYYY年MM月DD日')) as jlqzrq,
        to_char(a.csrq, 'YYYY年MM月') as csny
         from vw_acp_pm_prisoner_jds a where a.jgrybm = #{jgrybm}
    </select>

</mapper>
