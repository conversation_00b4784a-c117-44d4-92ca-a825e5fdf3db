<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.UndercoverDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getUndercoverRespVOByJgrybm"
            resultType="com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverRecordVO">
        SELECT t.id,t.jgrybm,t.status,t.add_time,
               case when t.status = '05' then t1.add_user_name else t.add_user_name end add_user_name,
               case when t.status = '05' then t1.approver_time else t.approver_time end approver_time, t.arrange_reason
        from acp_gj_undercover  t LEFT JOIN acp_gj_undercover_cancel  t1 on t1.id = t.id and t1.is_del =0
        WHERE t.is_del = 0 and t.jgrybm = #{jgrybm} and t.status in ('04','05') order by t.update_time desc
    </select>

</mapper>
