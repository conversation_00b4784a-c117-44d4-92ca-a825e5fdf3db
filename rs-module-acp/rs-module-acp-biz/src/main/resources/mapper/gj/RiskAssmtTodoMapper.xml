<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.RiskAssmtTodoDao">

    <select id="getNewEntrantJob" resultType="com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoRespVO">
        SELECT
            ry.org_code,ry.reg_code,ry.reg_name,ry.pro_code,ry.pro_name,
            ry.area_id,area.area_name,ry.jgrybm, ry.xm as jgryxm,
            ry.room_name,ry.jsh as room_id,ry.fxdj,'02' as risk_type,ry.id as source_business_id
        from vw_acp_pm_prisoner_in ry left join acp_gj_risk_assmt_todo t on  ry.id =  t.source_business_id and t.is_del =0
                                      INNER JOIN acp_pm_area area on area.id = ry.area_id and area.is_del = 0
        where  ry.is_del = 0 and ry.rssj  &gt;= CURRENT_TIMESTAMP - INTERVAL '3 DAY' and t.id is null
    </select>

    <select id="getAttentionPersonnel"
            resultType="com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoRespVO">
        SELECT
            ry.org_code,ry.reg_code,ry.reg_name,ry.pro_code,ry.pro_name,
            ry.area_id,area.area_name,ry.jgrybm, ry.xm as jgryxm,
            ry.room_name,ry.jsh as room_id,ry.fxdj,'06' as risk_type,pam.id as source_business_id
        from pam_attention_prisoner pam
                 left join acp_gj_risk_assmt_todo t on  pam.id =  t.source_business_id and t.is_del =0
                 inner join vw_acp_pm_prisoner_in ry  on pam.jgrybm = ry.jgrybm
                 INNER JOIN acp_pm_area area on area.id = ry.area_id and area.is_del = 0
        where  ry.is_del = 0 and pam.is_del = 0  and pam.reg_status  ='3'
          and pam.update_time &gt;= CURRENT_TIMESTAMP - INTERVAL '3 DAY' and t.id is null
    </select>

</mapper>
