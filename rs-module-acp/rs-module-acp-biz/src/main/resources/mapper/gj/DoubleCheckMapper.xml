<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.DoubleCheckDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getAppDoubleCheckPage"  resultType="com.rs.module.acp.entity.gj.DoubleCheckDO">
        select a.*, b.xb, extract(YEAR FROM AGE(now(), b.csrq)) as nl
        from acp_gj_double_check a left join vw_acp_pm_prisoner_list b on a.jgrybm = b.jgrybm
         where a.is_del = 0 and a.operate_police_sfzh = #{operatePoliceSfzh}
         <if test="startTime!=null">
             and a.add_time &gt;= #{startTime}
         </if>
         <if test="endTime!=null">
             and a.add_time &lt; #{endTime}
         </if>
         order by a.add_time desc
    </select>

</mapper>
