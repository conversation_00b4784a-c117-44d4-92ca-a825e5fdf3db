<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.FaceToFaceDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getNeedRemindRoomId" resultType="com.rs.module.acp.job.facetoface.bo.FaceToFaceBO">
        select m.room_id, x.room_name, m.org_code, m.org_name, m.police_id, m.police_name, m.police_sfzh, m.user_type from acp_pm_prison_room_warder m inner join (
        select c.* from (
        select a.org_code, a.id as room_id, COALESCE(b.djcs,0) as djcs from acp_pm_area_prison_room a left join (
        select org_code, room_id, count(1) as djcs from acp_gj_face_to_face where add_time &gt;= date_trunc('day',now()) GROUP BY org_code, room_id
        ) b on a.org_code = b.org_code and a.id = b.room_id where a.is_del = 0 and a.status = '1'
        ) c where c.djcs &lt; 2
        ) n on m.org_code = n.org_code and m.room_id = n.room_id
        left join acp_pm_area_prison_room x on m.room_id = x.id
        where m.is_del = 0 and m.status = '1'
    </select>


</mapper>
