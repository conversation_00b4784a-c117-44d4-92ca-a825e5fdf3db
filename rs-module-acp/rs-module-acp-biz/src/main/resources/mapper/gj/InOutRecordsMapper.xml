<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.gj.InOutRecordsDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <!-- user.orgType != null and user.orgType == '01' -->
    <!-- user.orgType != null and user.orgType == '02' -->
    <select id="getJgrmBusiness" resultType="com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsBusinessRespVO">

        SELECT id  AS businessId,'01' AS businessType,'提讯' AS businessTypeName FROM acp_wb_arraignment
        WHERE jgrybm = #{jgrybm} and is_del = 0
        union all
        SELECT id  AS businessId,'02' AS businessType,'提解' AS businessTypeName FROM acp_wb_escort
        WHERE jgrybm = #{jgrybm} and is_del = 0
        union all
        SELECT id  AS businessId,'01' AS businessType,'提询' AS businessTypeName FROM acp_wb_bring_interrogation
        WHERE jgrybm = #{jgrybm} and is_del = 0
    </select>
        <!--
        SELECT id  AS businessId,'01' AS businessType,'提讯' AS businessTypeName FROM acp_wb_arraignment
            WHERE jgrybm = #{jgrybm} and is_del = 0
                and start_apply_arraignment_time &gt;= #{startDate}
                and start_apply_arraignment_time &lt;= #{endDate}
            union all
            SELECT id  AS businessId,'02' AS businessType,'提解' AS businessTypeName FROM acp_wb_escort
            WHERE jgrybm = #{jgrybm} and is_del = 0
                and apply_escort_date &gt;= #{startDate}
                and apply_escort_date &lt;= #{endDate}
            union all

            SELECT id  AS businessId,'01' AS businessType,'提询' AS businessTypeName FROM acp_wb_bring_interrogation
            WHERE jgrybm = #{jgrybm} and is_del = 0
                and start_apply_arraignment_time &gt;= #{startDate}
                and start_apply_arraignment_time &lt;= #{endDate}
            union all

        SELECT id  AS businessId,'04' AS businessType,'律师登记' AS businessTypeName FROM acp_wb_lawyer_meeting
        WHERE jgrybm = #{jgrybm} and is_del = 0
            and apply_meeting_start_time &gt;= #{startDate}
            and apply_meeting_start_time &lt;= #{endDate}
        union all
        SELECT id  AS businessId,'03' AS businessType,'家属登记' AS businessTypeName FROM acp_wb_family_meeting
        WHERE jgrybm = #{jgrybm} and is_del = 0
            and apply_meeting_start_time &gt;= #{startDate}
            and apply_meeting_start_time &lt;= #{endDate}
        union all
        SELECT id  AS businessId,'07' AS businessType,'领事会见' AS businessTypeName FROM acp_wb_consular_meeting
        WHERE jgrybm = #{jgrybm} and is_del = 0
            and apply_meeting_start_time &gt;= #{startDate}
            and apply_meeting_start_time &lt;= #{endDate}
        union all
        SELECT id  AS businessId,'08' AS businessType,'监室调整' AS businessTypeName from  acp_gj_prison_room_change t
        WHERE t.IS_DEL = 0 and t.is_change = 1 and  t.jgrybm = #{jgrybm}
            and room_change_time &gt;= #{startDate}
            and room_change_time &lt;= #{endDate}
        -->
        <!--


     - 家属当向视频会见
        SELECT id  AS businessId,'' AS businessType FROM acp_wb_family_meeting_video
        WHERE jgrybm = #{jgrybm} and is_del = 0
          and notification_meeting_date &gt; #{startDate}
        union all

     -->

    <select id="getPaddingRoomInOutRecords" resultType="com.rs.module.acp.controller.app.gj.vo.inoutrecords.PrisonerInOutBusinessTypeRespVO">

        select id as id,
               jgrybm as jgrybm,
               xm as name,
               rssj,
               front_photo as frontPhoto,
               fxdj as riskLevel,
               ryzt,
               jsh,
               (
                   select string_agg(business_type, ',') AS business_types from acp_gj_in_out_records where status='0' and inout_type='01' and is_del = 0
                                                                                                        and jgrybm = bpi.jgrybm
                   group by jgrybm
               ) as business_types
        from vw_acp_pm_prisoner bpi
        WHERE jsh = #{room_id} and is_del = 0
    </select>
    <select id="selectBusinessTypeCount" resultType="com.rs.module.acp.controller.app.gj.vo.inoutrecords.InOutStatisticDetailVO">
        SELECT business_type, COUNT(1) AS nums,jgrybm
        FROM acp_gj_in_out_records
        WHERE room_id = #{roomId}
          AND status = '0'
          AND is_del = 0
          AND inout_type='02'
          <if test="startOfDay != null and endOfDay != null">
              AND inout_time BETWEEN #{startOfDay} AND #{endOfDay}
          </if>

        GROUP BY business_type,jgrybm
    </select>
</mapper>
