package com.rs.module.acp.controller.admin.gj.vo.prisonroom;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务--带入带出登记 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonRoomChangeInOutSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;


    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date escortingTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("带回监室时间")
    private Date returnTime;

    @ApiModelProperty("带回民警身份证号")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

}
