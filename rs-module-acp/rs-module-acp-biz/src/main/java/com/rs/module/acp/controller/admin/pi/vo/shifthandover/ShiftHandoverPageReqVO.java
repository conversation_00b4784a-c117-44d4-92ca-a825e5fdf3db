package com.rs.module.acp.controller.admin.pi.vo.shifthandover;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-巡控交接班登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ShiftHandoverPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("人员情况")
    private String ryqk;

    @ApiModelProperty("重点关注人员")
    private String zdgzry;

    @ApiModelProperty("巡控登记")
    private String xkdj;

    @ApiModelProperty("交班总人数")
    private Integer totalPersons;

    @ApiModelProperty("交班点名总人数")
    private Integer rollCallTotal;

    @ApiModelProperty("待处理问题")
    private String pendingIssues;

    @ApiModelProperty("交班人身份证号")
    private String handoverPersonSfzh;

    @ApiModelProperty("交班人")
    private String handoverPerson;

    @ApiModelProperty("交班时间")
    private Date[] handoverTime;

    @ApiModelProperty("接班总人数")
    private Integer takeoverTotalPersons;

    @ApiModelProperty("接班人身份证号")
    private String takeoverPersonSfzh;

    @ApiModelProperty("接班人")
    private String takeoverPerson;

    @ApiModelProperty("接班时间")
    private Date[] takeoverTime;

    @ApiModelProperty("状态")
    private String status;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
