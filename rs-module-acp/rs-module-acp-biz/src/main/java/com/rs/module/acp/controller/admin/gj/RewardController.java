package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.reward.RewardRegInfoReqVO;
import com.rs.module.acp.controller.admin.gj.vo.reward.RewardRespVO;
import com.rs.module.acp.controller.admin.gj.vo.reward.RewardSaveReqVO;
import com.rs.module.acp.service.gj.reward.RewardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-奖励管理")
@RestController
@RequestMapping("/acp/gj/reward")
@Validated
public class RewardController {

    @Resource
    private RewardService rewardService;

    @PostMapping("/create")
    @ApiOperation(value = "奖励呈批申请")
    @LogRecordAnnotation(bizModule = "acp:reward:create", operateType = LogOperateType.CREATE, title = "管教业务-奖励呈批-登记",
            success = "管教业务-奖励呈批-创建登记成功", fail = "管教业务-奖励呈批-创建请登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createReward(@Valid @RequestBody RewardSaveReqVO createReqVO) {
        return success(rewardService.createReward(createReqVO));
    }


    @PostMapping("/approve")
    @ApiOperation(value = "奖励申请-领导审批")
    @LogRecordAnnotation(bizModule = "acp:reward:approve", operateType = LogOperateType.UPDATE, title = "管教业务-奖励申请-领导审批",
            success = "管教业务-处罚呈批-领导审批成功", fail = "管教业务-处罚呈批-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        rewardService.leaderApprove(approveReqVO);
        return success(true);
    }


    @PostMapping("/regInfo")
    @ApiOperation(value = "奖励登记")
    @LogRecordAnnotation(bizModule = "acp:reward:regInfo", operateType = LogOperateType.UPDATE, title = "管教业务-奖励申请-领导审批",
            success = "管教业务-处罚呈批-领导审批成功", fail = "管教业务-处罚呈批-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#regInfoReqVO}}")
    public CommonResult<Boolean> regInfo(@Valid @RequestBody RewardRegInfoReqVO regInfoReqVO) {
        rewardService.regInfo(regInfoReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @ApiOperation(value = "奖励登记获取详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:reward:get", operateType = LogOperateType.QUERY, title = "管教业务-奖励登记-详情获取",
            bizNo = "{{#id}}", success = "管教业务-奖励登记-详情获取-成功", fail = "管教业务-奖励登记-详情获取-失败", extraInfo = "{{#id}}")
    public CommonResult<RewardRespVO> getReward(@RequestParam("id") String id) {
        RewardRespVO reward = rewardService.getReward(id);
        return success(reward);
    }

}
