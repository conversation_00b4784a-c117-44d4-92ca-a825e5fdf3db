package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程计划列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesPlanListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("课程计划名称")
    private String planName;

    @ApiModelProperty("plan_code")
    private String planCode;

    @ApiModelProperty("课程时段-开始")
    private Date[] startDate;

    @ApiModelProperty("课程时段-结束")
    private Date[] endDate;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
