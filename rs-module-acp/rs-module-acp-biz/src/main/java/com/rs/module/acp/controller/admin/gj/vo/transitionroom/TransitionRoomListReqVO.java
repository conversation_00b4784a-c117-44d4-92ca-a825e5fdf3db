package com.rs.module.acp.controller.admin.gj.vo.transitionroom;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡监室管理列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransitionRoomListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("过渡开始日期")
    private Date[] startDate;

    @ApiModelProperty("过渡结束日期")
    private Date[] endDate;

    @ApiModelProperty("实际开始日期")
    private Date[] actualStartDate;

    @ApiModelProperty("实际结束日期")
    private Date[] actualEndDate;

    @ApiModelProperty("过渡监室id")
    private String roomId;

    @ApiModelProperty("过渡监室名称")
    private String roomName;

    @ApiModelProperty("是否延长")
    private Short isExtend;

    @ApiModelProperty("状态")
    private String status;

}
