package com.rs.module.acp.controller.admin.wb;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.VisitorRegDO;
import com.rs.module.acp.service.wb.VisitorRegService;

@Api(tags = "实战平台-窗口业务-对外开放登记")
@RestController
@RequestMapping("/acp/wb/visitorReg")
@Validated
public class VisitorRegController {

    @Resource
    private VisitorRegService visitorRegService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-对外开放登记")
    @LogRecordAnnotation(bizModule = "acp:visitorReg:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建对外开放登记",
            success = "实战平台-窗口业务-创建对外开放登记成功", fail = "实战平台-窗口业务-创建对外开放登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createVisitorReg(@Valid @RequestBody VisitorRegSaveReqVO createReqVO) {
        return success(visitorRegService.createVisitorReg(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新对外开放登记")
    @LogRecordAnnotation(bizModule = "acp:visitorReg:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新对外开放登记",
            success = "实战平台-窗口业务-更新对外开放登记成功", fail = "实战平台-窗口业务-更新对外开放登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> updateVisitorReg(@Valid @RequestBody VisitorRegSaveReqVO updateReqVO) {
        visitorRegService.updateVisitorReg(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-窗口业务-删除对外开放登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:visitorReg:delete", operateType = LogOperateType.DELETE, title = "实战平台-窗口业务-删除对外开放登记",
            success = "实战平台-窗口业务-删除对外开放登记成功", fail = "实战平台-窗口业务-删除对外开放登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteVisitorReg(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           visitorRegService.deleteVisitorReg(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得对外开放登记详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:visitorReg:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得对外开放登记详情",
            success = "实战平台-窗口业务-获得对外开放登记详情成功", fail = "实战平台-窗口业务-获得对外开放登记详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<VisitorRegRespVO> getVisitorReg(@RequestParam("id") String id) {
        return success(visitorRegService.getVisitorRegById(id));
    }
}
