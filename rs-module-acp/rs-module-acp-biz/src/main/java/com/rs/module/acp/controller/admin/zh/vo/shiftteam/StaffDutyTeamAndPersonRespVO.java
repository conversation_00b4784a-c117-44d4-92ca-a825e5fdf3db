package com.rs.module.acp.controller.admin.zh.vo.shiftteam;

import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageHeaderVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@ApiModel(description = "管理后台 - 值班模板班组及人员信息 Response VO")
@Data
public class StaffDutyTeamAndPersonRespVO {

    @ApiModelProperty("值班模板班组信息")
    private StaffDutyTeamRespVO teamInfo;

    @ApiModelProperty("值班模板班组人员信息列表")
    private List<StaffDutyTeamPersonRespVO> personInfos;

    @ApiModelProperty(value = "值班表头列表")
    private List<DutyManageHeaderVO> headerList;
}
