package com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-定屏监控与被监管人员关联 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FixedScreenMonitorPrisonerRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("定屏监控ID")
    private String fixedScreenMonitorId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("上墙人员信息")
    private String onScreenPersonInfo;
    @ApiModelProperty("状态")
    private String status;
}
