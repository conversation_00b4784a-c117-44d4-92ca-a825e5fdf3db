package com.rs.module.acp.controller.admin.gj;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.*;
import com.rs.module.acp.job.edurehabcourses.EdurehabCoursesPlanJob;
import io.swagger.annotations.ApiImplicitParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.EdurehabCoursesPlanDO;
import com.rs.module.acp.service.gj.edurehabcourses.EdurehabCoursesPlanService;

@Api(tags = "实战平台-管教业务-教育康复课程计划")
@RestController
@RequestMapping("/acp/gj/edurehabCoursesPlan")
@Validated
public class EdurehabCoursesPlanController {

    @Resource
    private EdurehabCoursesPlanService edurehabCoursesPlanService;

    @Resource
    private EdurehabCoursesPlanJob edurehabCoursesPlanJob;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-教育康复课程计划")
    public CommonResult<String> createEdurehabCoursesPlan(@Valid @RequestBody EdurehabCoursesPlanSaveReqVO createReqVO) {
        return success(edurehabCoursesPlanService.createEdurehabCoursesPlan(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-教育康复课程计划")
    public CommonResult<Boolean> updateEdurehabCoursesPlan(@Valid @RequestBody EdurehabCoursesPlanSaveReqVO updateReqVO) {
        edurehabCoursesPlanService.updateEdurehabCoursesPlan(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-教育康复课程计划")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEdurehabCoursesPlan(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           edurehabCoursesPlanService.deleteEdurehabCoursesPlan(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程计划")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EdurehabCoursesPlanRespVO> getEdurehabCoursesPlan(@RequestParam("id") String id) {
        return success(edurehabCoursesPlanService.getEdurehabCoursesPlan(id));
    }

    @GetMapping("/getByDate")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "startDate", value = "开始时间", required = true),
            @ApiImplicitParam(name = "endDate", value = "结束时间", required = true)
    })

    public CommonResult<EdurehabCoursesPlanRespVO> getByDate(@RequestParam(value = "orgCode", required = false) String orgCode,
                                                             @RequestParam("startDate") Date startDate,
                                                             @RequestParam("endDate") Date endDate) {
        if(StringUtils.isEmpty(orgCode)){
            orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        }
        return success(edurehabCoursesPlanService.getByDate(orgCode, startDate, endDate));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程计划分页")
    public CommonResult<PageResult<EdurehabCoursesPlanRespVO>> getEdurehabCoursesPlanPage(@Valid @RequestBody EdurehabCoursesPlanPageReqVO pageReqVO) {
        PageResult<EdurehabCoursesPlanDO> pageResult = edurehabCoursesPlanService.getEdurehabCoursesPlanPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EdurehabCoursesPlanRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程计划列表")
    public CommonResult<List<EdurehabCoursesPlanRespVO>> getEdurehabCoursesPlanList(@Valid @RequestBody EdurehabCoursesPlanListReqVO listReqVO) {
        List<EdurehabCoursesPlanDO> list = edurehabCoursesPlanService.getEdurehabCoursesPlanList(listReqVO);
        return success(BeanUtils.toBean(list, EdurehabCoursesPlanRespVO.class));
    }

    @GetMapping("/getPlanTime")
    @ApiOperation(value = "获取创建课程时间")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<EdurehabCoursesPlanRespVO> getPlanTime(@RequestParam(value = "orgCode", required = false) String orgCode) {
        if(StringUtils.isEmpty(orgCode)){
            orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        }
        return success(edurehabCoursesPlanService.getPlanTime(orgCode));
    }

    @GetMapping("/getPlanArea")
    @ApiOperation(value = "获取创建课程戒区信息")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<List<JqAreaVO>> getPlanArea(@RequestParam(value = "orgCode", required = false) String orgCode) {
        if(StringUtils.isEmpty(orgCode)){
            orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        }
        return success(edurehabCoursesPlanService.getPlanArea(orgCode));
    }


    @PostMapping("/approval")
    @ApiOperation(value = "审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody EdurehabCoursesPlanApprovalReqVO approvalReqVO) {
        edurehabCoursesPlanService.approval(approvalReqVO);
        return success(true);
    }

    @GetMapping("/test-job")
    @ApiOperation(value = "定时任务测试")
    public CommonResult<Boolean> testJob() {
        edurehabCoursesPlanJob.changeEdurehabCoursesStatusJob();
        return success(true);
    }

}
