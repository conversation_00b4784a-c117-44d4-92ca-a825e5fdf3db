package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel(description = "每天课程信息")
@lombok.Data
public class EdurehabDayRespVO {

    @ApiModelProperty("课程日期")
    private Date coursesDate;

    @ApiModelProperty("课程日期 String类型")
    private String coursesDateStr;

    @ApiModelProperty("戒区信息")
    private List<JqAreaVO> jqAreaVOList;

}
