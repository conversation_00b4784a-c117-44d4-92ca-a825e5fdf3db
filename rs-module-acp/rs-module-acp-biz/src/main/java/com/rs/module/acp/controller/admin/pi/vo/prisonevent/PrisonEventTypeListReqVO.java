package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件类型列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventTypeListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("事件类型名称")
    private String typeName;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("机构编码-无需传值")
    private String orgCode;

    @ApiModelProperty("是否列表使用,为true则只返回基本信息")
    private boolean isList = true;
}
