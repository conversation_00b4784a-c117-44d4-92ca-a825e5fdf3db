package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-出入监区登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonAreaInoutSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String xm;

    @ApiModelProperty("证件类型")
    @NotEmpty(message = "证件类型不能为空")
    private String zjlx;

    @ApiModelProperty("性别")
    @NotEmpty(message = "性别不能为空")
    private String xb;

    @ApiModelProperty("证件号码")
    @NotEmpty(message = "证件号码不能为空")
    private String zjhm;

    @ApiModelProperty("联系方式")
    @NotEmpty(message = "联系方式不能为空")
    private String lxfs;

    @ApiModelProperty("进入监区原因")
    @NotEmpty(message = "进入监区原因不能为空")
    private String jrjqry;

    @ApiModelProperty("身份背景调查")
    @NotEmpty(message = "身份背景调查不能为空")
    private String sfbjdc;

    @ApiModelProperty("预计来访时间")
    @NotNull(message = "预计来访时间不能为空")
    private Date yjlfsj;

    @ApiModelProperty("预计离所时间")
    @NotNull(message = "预计离所时间不能为空")
    private Date yjlssj;

    @ApiModelProperty("车辆号码")
    private String clhm;

    @ApiModelProperty("照片存储url")
    private String zpUrl;

    @ApiModelProperty("进入监区时间")
    private Date jrjqsj;

    @ApiModelProperty("进入监区安全检查信息")
    private String jrjqaqjcxx;

    @ApiModelProperty("进入监区安全检查人身份证号")
    private String jrjqaqjcrsfzh;

    @ApiModelProperty("进入监区安全检查人姓名")
    private String jrjqaqjcrxm;

    @ApiModelProperty("离开监区时间")
    private Date lkjqsj;

    @ApiModelProperty("离开监区安全检查信息")
    private String lkjqaqjcxx;

    @ApiModelProperty("离开监区安全检查人身份证号")
    private String lkjqaqjcrsfzh;

    @ApiModelProperty("离开监区安全检查人姓名")
    private String lkjqaqjcrxm;

    @ApiModelProperty("办理状态")
    private String status;

}
