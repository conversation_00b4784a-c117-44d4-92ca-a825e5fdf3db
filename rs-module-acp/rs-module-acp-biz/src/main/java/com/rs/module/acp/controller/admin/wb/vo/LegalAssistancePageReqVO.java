package com.rs.module.acp.controller.admin.wb.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-法律帮助申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LegalAssistancePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("律师姓名")
    private String lawyerName;

    @ApiModelProperty("律师证件号码")
    private String lawyerIdNumber;

    @ApiModelProperty("律师执业证号码")
    private String lawyerPracticeLicenseNumber;

    @ApiModelProperty("律师所属单位")
    private String lawyerFirm;

    @ApiModelProperty("申请时间")
    private Date[] applyTime;

    @ApiModelProperty("申请帮助事项")
    private String assistanceMatter;

    @ApiModelProperty("是否需要办案机关许可")
    private Short isNeedCaseUnitAllowed;

    @ApiModelProperty("办案机关类型")
    private String caseUnitType;

    @ApiModelProperty("办案机关名称")
    private String caseUnitName;

    @ApiModelProperty("办案机关是否许可")
    private Short isCaseUnitAllowed;

    @ApiModelProperty("办案机关反馈")
    private String caseUnitFeedback;

    @ApiModelProperty("决定时间")
    private Date[] decisionTime;

    @ApiModelProperty("会见方式")
    private String meetingMethod;

    @ApiModelProperty("会见室ID")
    private String meetingRoomId;

    @ApiModelProperty("会见室名称")
    private String meetingRoomName;

    @ApiModelProperty("是否特殊会见")
    private Short isSpecialMeeting;

    @ApiModelProperty("带出监室时间")
    private Date[] escortingTime;

    @ApiModelProperty("带出检查结果")
    private String inspectionResult;

    @ApiModelProperty("会见开始时间")
    private Date[] meetingStartTime;

    @ApiModelProperty("会见结束时间")
    private Date[] meetingEndTime;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带回民警身份证号")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

    @ApiModelProperty("带回监室时间")
    private Date[] returnTime;

    @ApiModelProperty("会毕检查结果")
    private String returnInspectionResult;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("监督民警身份证号")
    private String supervisingPoliceSfzh;

    @ApiModelProperty("监督民警姓名")
    private String supervisingPoliceName;

    @ApiModelProperty("律师是否有违规")
    private Short isLawyerViolation;

    @ApiModelProperty("律师是否告知在押人员异常行为")
    private Short isLawyerInformAbnormalBehav;

    @ApiModelProperty("律师违规情况")
    private String lawyerViolationDetails;

    @ApiModelProperty("在押人员异常行为情况")
    private String abnormalBehaviorDetails;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
