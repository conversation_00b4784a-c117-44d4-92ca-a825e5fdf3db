package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-加减分考核审核 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmttApprovalRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("考核记录ID")
    private String assmtRecordId;
    @ApiModelProperty("被考核人身份证号")
    private String assessedSfzh;
    @ApiModelProperty("被考核人姓名")
    private String assessedName;
    @ApiModelProperty("考核月份")
    private String assmtMonth;
    @ApiModelProperty("指标类型，01：自观指标，02：加减分指标")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_ZBLX")
    private String indicatorType;
    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;
    @ApiModelProperty("考核人姓名")
    private String assessorName;
    @ApiModelProperty("考核时间")
    private Date assessorTime;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_KHSPZT")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

//    @ApiModelProperty("主指标信息")
//    private List<AssmtApprovalMainIndicatorRespVO> mainIndicatorList;

    @ApiModelProperty("子指标名称")
    private List<AssmttApprovalRecordRespVO> approvalRecordList;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;

}
