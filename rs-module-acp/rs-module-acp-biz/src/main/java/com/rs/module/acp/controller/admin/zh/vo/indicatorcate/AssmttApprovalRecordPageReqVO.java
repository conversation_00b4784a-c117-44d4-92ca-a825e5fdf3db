package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-考核审核记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssmttApprovalRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("加减分审核ID")
    private String assmttApprovalId;

    @ApiModelProperty("指标类型ID")
    private String indicatorCateId;

    @ApiModelProperty("指标类型名称")
    private String indicatorCateName;

    @ApiModelProperty("所属主指标ID")
    private String mainIndicatorId;

    @ApiModelProperty("所属子指标ID")
    private String subIndicatorId;

    @ApiModelProperty("指标名称")
    private String indicatorName;

    @ApiModelProperty("指标描述")
    private String indicatorDescription;

    @ApiModelProperty("分值类型，字典：FIXED：固定分，RANGE：范围分")
    private String scoreType;

    @ApiModelProperty("基础分值")
    private BigDecimal baseScore;

    @ApiModelProperty("最小分值")
    private BigDecimal minScore;

    @ApiModelProperty("最大分值（仅范围分有效）")
    private BigDecimal maxScore;

    @ApiModelProperty("排序序号")
    private Integer sortOrder;

    @ApiModelProperty("绩效原因")
    private String assmtReason;

    @ApiModelProperty("绩效分数")
    private BigDecimal assmtScore;

    @ApiModelProperty("综合审核原因")
    private String zhApprovalReason;

    @ApiModelProperty("综合审核分数")
    private BigDecimal zhApprovalScore;

    @ApiModelProperty("最终审核分数")
    private BigDecimal zzApprovalScore;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
