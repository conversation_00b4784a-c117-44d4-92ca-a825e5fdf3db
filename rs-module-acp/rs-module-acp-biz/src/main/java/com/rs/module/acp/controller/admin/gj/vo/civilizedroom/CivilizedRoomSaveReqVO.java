package com.rs.module.acp.controller.admin.gj.vo.civilizedroom;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明监室登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedRoomSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("评比月份")
    @NotEmpty(message = "评比月份不能为空")
    private String evalMonth;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("申请人身份证号")
    private String applyUserSfzh;

    @ApiModelProperty("申请人")
    private String applyUser;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
