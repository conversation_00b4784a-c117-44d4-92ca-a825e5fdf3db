package com.rs.module.acp.controller.admin.gj.vo.groupinout;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-集体出入分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GroupInOutPageReqVO extends PageParam {

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("是否按监室(0:否，1:是)")
    private String isRoom;

    @ApiModelProperty("对象ID")
    private String objectId;

    @ApiModelProperty("对象名称")
    private String objectName;

    @ApiModelProperty("对象数量")
    private Integer objectCount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
