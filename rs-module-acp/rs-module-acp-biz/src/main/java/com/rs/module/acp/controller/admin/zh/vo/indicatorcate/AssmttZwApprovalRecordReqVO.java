package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-考核审核记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmttZwApprovalRecordReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("最终审核分数")
    private BigDecimal zzApprovalScore;
    
}
