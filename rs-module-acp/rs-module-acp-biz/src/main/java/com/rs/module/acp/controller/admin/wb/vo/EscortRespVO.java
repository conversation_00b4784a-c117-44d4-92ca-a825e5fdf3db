package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-提解登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EscortRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("第一位办案人ID")
    private String handler1Id;
    @ApiModelProperty("第一位办案人姓名")
    private String handler1Xm;
    @ApiModelProperty("第二位办案人ID")
    private String handler2Id;
    @ApiModelProperty("第二位办案人姓名")
    private String handler2Xm;
    @ApiModelProperty("第三位办案人ID")
    private String handler3Id;
    @ApiModelProperty("第三位办案人姓名")
    private String handler3Xm;
    @ApiModelProperty("审讯室ID")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PM_JQ")
    private String roomId;
    @ApiModelProperty("提解原因")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_TJSY")
    private String escortReason;
    @ApiModelProperty("申请提解日期")
    private Date applyEscortDate;
    @ApiModelProperty("详细事由")
    private String detailReason;
    @ApiModelProperty("提解目的地")
    private String destination;
    @ApiModelProperty("提解文书号提解文书号")
    private String wsh;
    @ApiModelProperty("上传提讯凭证URL")
    private String evidenceUrl;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("提解机关类型")
    private String tjjglx;
    @ApiModelProperty("提解机关名称")
    private String tjjgmc;
    @ApiModelProperty("办理状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_TJZT")
    private String status;
    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;
    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;
    @ApiModelProperty("带出监室时间")
    private Date escortingTime;
    @ApiModelProperty("检查结果")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_JCJG")
    private String inspectionResult;
    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;
    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;
    @ApiModelProperty("体表检查登记")
    private String physicalExam;
    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;
    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;
    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;
    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;
    @ApiModelProperty("检查时间")
    private Date inspectionTime;
    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;
    @ApiModelProperty("检查民警身份证号")
    private String inspector;
    @ApiModelProperty("提讯开始时间")
    private Date arraignmentStartTime;
    @ApiModelProperty("提讯结束时间")
    private Date arraignmentEndTime;
    @ApiModelProperty("带回检查人")
    private String returnInspectorSfzh;
    @ApiModelProperty("带回检查人")
    private String returnInspector;
    @ApiModelProperty("带回检查时间")
    private Date returnInspectionTime;
    @ApiModelProperty("带回检查结果")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_JCJG")
    private String returnInspectionResult;
    @ApiModelProperty("带回违禁物品登记")
    private String returnProhibitedItems;
    @ApiModelProperty("带回体表检查登记")
    private String returnPhysicalExam;
    @ApiModelProperty("带回异常情况登记")
    private String returnAbnormalSituations;
    @ApiModelProperty("带回监室时间")
    private Date returnTime;
    @ApiModelProperty("带回民警姓名")
    private String returnPolice;
    @ApiModelProperty("带回民警身份证号")
    private String returnPoliceSfzh;
    @ApiModelProperty("办案人员列表")
    private List<CasePersonnelRespVO> casePersonnelList;
    @ApiModelProperty("办案人员")
    private String handlerXm;
    @ApiModelProperty("办案单位")
    private String badwmc;
    @ApiModelProperty("带出操作人身份证号")
    private String escortingOperatorSfzh;
    @ApiModelProperty("带出操作人")
    private String escortingOperator;
    @ApiModelProperty("带出操作时间")
    private Date escortingOperatorTime;
    @ApiModelProperty("带回操作人身份证号")
    private String returnOperatorSfzh;
    @ApiModelProperty("带回操作人")
    private String returnOperator;
    @ApiModelProperty("带回操作时间")
    private Date returnOperatorTime;

    @ApiModelProperty("创建时间")
    private Date addTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;
}
