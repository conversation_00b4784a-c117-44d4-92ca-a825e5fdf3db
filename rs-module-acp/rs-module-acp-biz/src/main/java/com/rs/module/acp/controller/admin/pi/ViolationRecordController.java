package com.rs.module.acp.controller.admin.pi;

import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordRespVO;
import com.rs.module.acp.controller.admin.pi.vo.violationrecord.ViolationRecordSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.pi.ViolationRecordDO;
import com.rs.module.acp.service.pi.ViolationRecordService;

@Api(tags = "实战平台-巡视管控-违规登记")
@RestController
@RequestMapping("/acp/pi/violationRecord")
@Validated
public class ViolationRecordController {

    @Resource
    private ViolationRecordService violationRecordService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-巡视管控-违规登记")
    public CommonResult<String> createViolationRecord(@Valid @RequestBody ViolationRecordSaveReqVO createReqVO) {
        return success(violationRecordService.createViolationRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-巡视管控-违规登记")
    public CommonResult<Boolean> updateViolationRecord(@Valid @RequestBody ViolationRecordSaveReqVO updateReqVO) {
        violationRecordService.updateViolationRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-巡视管控-违规登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteViolationRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           violationRecordService.deleteViolationRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-巡视管控-违规登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ViolationRecordRespVO> getViolationRecord(@RequestParam("id") String id) {
        ViolationRecordDO violationRecord = violationRecordService.getViolationRecord(id);
        return success(BeanUtils.toBean(violationRecord, ViolationRecordRespVO.class));
    }

}
