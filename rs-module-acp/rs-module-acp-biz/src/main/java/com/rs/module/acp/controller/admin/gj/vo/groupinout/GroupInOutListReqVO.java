package com.rs.module.acp.controller.admin.gj.vo.groupinout;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-集体出入列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GroupInOutListReqVO extends BaseVO {
    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("是否按监室")
    private String isRoom;

    @ApiModelProperty("对象ID")
    private String objectId;

    @ApiModelProperty("对象名称")
    private String objectName;

    @ApiModelProperty("对象数量")
    private Integer objectCount;

    @ApiModelProperty("备注")
    private String remark;

}
