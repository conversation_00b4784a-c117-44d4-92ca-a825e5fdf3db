package com.rs.module.acp.controller.admin.wb.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-所领导接待登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeadershipReceptionPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("接待领导姓名")
    private String leaderName;

    @ApiModelProperty("同行人姓名")
    private String companionName;

    @ApiModelProperty("接待开始时间")
    private Date[] receptionStartTime;

    @ApiModelProperty("接待结束时间")
    private Date[] receptionEndTime;

    @ApiModelProperty("接待类型")
    private String type;

    @ApiModelProperty("接待事由")
    private String receptionReason;

    @ApiModelProperty("接待地点")
    private String receptionAddress;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
