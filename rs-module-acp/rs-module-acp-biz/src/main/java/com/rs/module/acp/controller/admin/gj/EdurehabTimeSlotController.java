package com.rs.module.acp.controller.admin.gj;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotRespVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabTimeSlotSaveReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.EdurehabTimeSlotDO;
import com.rs.module.acp.service.gj.edurehabcourses.EdurehabTimeSlotService;

@Api(tags = "实战平台-管教业务-教育康复课程时段")
@RestController
@RequestMapping("/acp/gj/edurehabTimeSlot")
@Validated
public class EdurehabTimeSlotController {

    @Resource
    private EdurehabTimeSlotService edurehabTimeSlotService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-教育康复课程时段")
    public CommonResult<String> createEdurehabTimeSlot(@Valid @RequestBody EdurehabTimeSlotSaveReqVO createReqVO) {
        return success(edurehabTimeSlotService.createEdurehabTimeSlot(createReqVO));
    }

    @PostMapping("/batchCreate")
    @ApiOperation(value = "批量创建")
    public CommonResult<Boolean> batchCreate(@Valid @RequestBody List<EdurehabTimeSlotSaveReqVO> list) {
        edurehabTimeSlotService.batchCreate(list);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-教育康复课程时段")
    public CommonResult<Boolean> updateEdurehabTimeSlot(@Valid @RequestBody EdurehabTimeSlotSaveReqVO updateReqVO) {
        edurehabTimeSlotService.updateEdurehabTimeSlot(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-教育康复课程时段")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEdurehabTimeSlot(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            edurehabTimeSlotService.deleteEdurehabTimeSlot(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程时段")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EdurehabTimeSlotRespVO> getEdurehabTimeSlot(@RequestParam("id") String id) {
        EdurehabTimeSlotDO edurehabTimeSlot = edurehabTimeSlotService.getEdurehabTimeSlot(id);
        return success(BeanUtils.toBean(edurehabTimeSlot, EdurehabTimeSlotRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程时段分页")
    public CommonResult<PageResult<EdurehabTimeSlotRespVO>> getEdurehabTimeSlotPage(@Valid @RequestBody EdurehabTimeSlotPageReqVO pageReqVO) {
        PageResult<EdurehabTimeSlotDO> pageResult = edurehabTimeSlotService.getEdurehabTimeSlotPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EdurehabTimeSlotRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得课程时段列表")
    public CommonResult<List<EdurehabTimeSlotRespVO>> getEdurehabTimeSlotList(@Valid @RequestBody EdurehabTimeSlotListReqVO listReqVO) {
        if (StringUtils.isEmpty(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<EdurehabTimeSlotDO> list = edurehabTimeSlotService.getEdurehabTimeSlotList(listReqVO);
        return success(BeanUtils.toBean(list, EdurehabTimeSlotRespVO.class));
    }
}
