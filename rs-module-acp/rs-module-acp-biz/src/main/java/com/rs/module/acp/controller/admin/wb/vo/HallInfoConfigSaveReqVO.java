package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-服务大厅信息发布配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HallInfoConfigSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("是否轮播(0:否，1：是)")
    @NotNull(message = "是否轮播不能为空")
    private Short isCarousel;

}
