package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情处置模板列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventDisposeTemplateListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("模板内容")
    private String templateContent;

    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("所情事件类型ID")
    private String typeId;

}
