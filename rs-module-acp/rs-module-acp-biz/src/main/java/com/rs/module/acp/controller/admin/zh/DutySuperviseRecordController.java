package com.rs.module.acp.controller.admin.zh;

import com.rs.module.acp.job.zh.DutySupervisionJob;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRecordDO;
import com.rs.module.acp.service.zh.DutySuperviseRecordService;

@Api(tags = "综合管理-值班管理-值班督导记录")
@RestController
@RequestMapping("/acp/zh/dutySuperviseRecord")
@Validated
public class DutySuperviseRecordController {

    @Resource
    private DutySuperviseRecordService dutySuperviseRecordService;

    @Resource
    private DutySupervisionJob dutySupervisionJob;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-值班管理-值班督导记录")
    public CommonResult<String> createDutySuperviseRecord(@Valid @RequestBody DutySuperviseRecordSaveReqVO createReqVO) {
        return success(dutySuperviseRecordService.createDutySuperviseRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-值班管理-值班督导记录")
    public CommonResult<Boolean> updateDutySuperviseRecord(@Valid @RequestBody DutySuperviseRecordSaveReqVO updateReqVO) {
        dutySuperviseRecordService.updateDutySuperviseRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-值班管理-值班督导记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDutySuperviseRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           dutySuperviseRecordService.deleteDutySuperviseRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-值班管理-值班督导记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DutySuperviseRecordRespVO> getDutySuperviseRecord(@RequestParam("id") String id) {
        DutySuperviseRecordDO dutySuperviseRecord = dutySuperviseRecordService.getDutySuperviseRecord(id);
        return success(BeanUtils.toBean(dutySuperviseRecord, DutySuperviseRecordRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-值班管理-值班督导记录分页")
    public CommonResult<PageResult<DutySuperviseRecordRespVO>> getDutySuperviseRecordPage(@Valid @RequestBody DutySuperviseRecordPageReqVO pageReqVO) {
        PageResult<DutySuperviseRecordDO> pageResult = dutySuperviseRecordService.getDutySuperviseRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DutySuperviseRecordRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-值班管理-值班督导记录列表")
    public CommonResult<List<DutySuperviseRecordRespVO>> getDutySuperviseRecordList(@Valid @RequestBody DutySuperviseRecordListReqVO listReqVO) {
        List<DutySuperviseRecordDO> list = dutySuperviseRecordService.getDutySuperviseRecordList(listReqVO);
        return success(BeanUtils.toBean(list, DutySuperviseRecordRespVO.class));
    }

    @GetMapping("/getCurrentDutyInfo")
    public CommonResult<String> getCurrentDutyInfo(@RequestParam("orgCode") String orgCode) {
        dutySupervisionJob.dutySupervision();
        return success("true");
    }
}
