package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师会见同行登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerMeetingCompanionSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("律师会见ID")
    private String lawyerMeetingId;

    @ApiModelProperty("第一位律师姓名")
    private String name;

    @ApiModelProperty("第一位律师性别")
    private String gender;

    @ApiModelProperty("第一位律师证件号码")
    private String idCard;

    @ApiModelProperty("同行人类别")
    private String companionType;

    @ApiModelProperty("附件URL")
    private String attachmentUrl;

}
