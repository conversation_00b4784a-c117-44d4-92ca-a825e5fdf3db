package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件类型明细项列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventTypeItemListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("事件项名称")
    private String itemName;

    @ApiModelProperty("上级ID")
    private String parentId;

    @ApiModelProperty("级别代码（1：一级，2：二级，3：三级）")
    private Integer levelCode;

    @ApiModelProperty("扣分值")
    private Integer deductPoint;

    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("所情事件类型ID")
    private String typeId;

}
