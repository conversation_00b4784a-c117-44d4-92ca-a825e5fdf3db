package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasureListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasurePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasureRespVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.PunishmentMeasureSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.PunishmentMeasureDO;
import com.rs.module.acp.service.gj.punishment.PunishmentMeasureService;

@Api(tags = "实战平台-管教业务-处罚呈批关联措施")
@RestController
@RequestMapping("/acp/gj/punishmentMeasure")
@Validated
public class PunishmentMeasureController {

    @Resource
    private PunishmentMeasureService punishmentMeasureService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-处罚呈批关联措施")
    public CommonResult<String> createPunishmentMeasure(@Valid @RequestBody PunishmentMeasureSaveReqVO createReqVO) {
        return success(punishmentMeasureService.createPunishmentMeasure(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-处罚呈批关联措施")
    public CommonResult<Boolean> updatePunishmentMeasure(@Valid @RequestBody PunishmentMeasureSaveReqVO updateReqVO) {
        punishmentMeasureService.updatePunishmentMeasure(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-处罚呈批关联措施")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePunishmentMeasure(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           punishmentMeasureService.deletePunishmentMeasure(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-处罚呈批关联措施")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PunishmentMeasureRespVO> getPunishmentMeasure(@RequestParam("id") String id) {
        PunishmentMeasureDO punishmentMeasure = punishmentMeasureService.getPunishmentMeasure(id);
        return success(BeanUtils.toBean(punishmentMeasure, PunishmentMeasureRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-处罚呈批关联措施分页")
    public CommonResult<PageResult<PunishmentMeasureRespVO>> getPunishmentMeasurePage(@Valid @RequestBody PunishmentMeasurePageReqVO pageReqVO) {
        PageResult<PunishmentMeasureDO> pageResult = punishmentMeasureService.getPunishmentMeasurePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PunishmentMeasureRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-处罚呈批关联措施列表")
    public CommonResult<List<PunishmentMeasureRespVO>> getPunishmentMeasureList(@Valid @RequestBody PunishmentMeasureListReqVO listReqVO) {
        List<PunishmentMeasureDO> list = punishmentMeasureService.getPunishmentMeasureList(listReqVO);
        return success(BeanUtils.toBean(list, PunishmentMeasureRespVO.class));
    }
}
