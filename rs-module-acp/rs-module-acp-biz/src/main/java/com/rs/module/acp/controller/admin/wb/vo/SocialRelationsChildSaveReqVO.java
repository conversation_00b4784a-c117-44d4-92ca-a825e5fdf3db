package com.rs.module.acp.controller.admin.wb.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-社会关系新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SocialRelationsChildSaveReqVO extends BaseVO {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("家属姓名")
    @NotEmpty(message = "家属姓名不能为空")
    private String name;

    @ApiModelProperty("家属性别")
    @NotEmpty(message = "家属性别不能为空")
    private String gender;

    @ApiModelProperty("家属证件类型")
    @NotEmpty(message = "家属证件类型不能为空")
    private String idType;

    @ApiModelProperty("家属证件号码")
    @NotEmpty(message = "家属证件号码不能为空")
    private String idNumber;

    @ApiModelProperty("家属与被会见人社会关系")
    @NotEmpty(message = "家属与被会见人社会关系不能为空")
    private String relationship;

    @ApiModelProperty("家属联系方式")
    private String contact;

    @ApiModelProperty("家属工作单位")
    private String workUnit;

    @ApiModelProperty("家属居住地址")
    private String address;

    @ApiModelProperty("职业")
    private String occupation;

    @ApiModelProperty("照片")
    private String imageUrl;

    @ApiModelProperty("关系证明附件")
    private String relationsAttch;
}
