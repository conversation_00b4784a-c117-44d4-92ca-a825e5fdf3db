package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-服务大厅信息发布新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HallInfoSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("信息标题")
    @NotEmpty(message = "信息标题不能为空")
    private String title;

    @ApiModelProperty("信息内容")
    private String infoContent;

    @ApiModelProperty("附件文件类型")
    private String attachmentType;

    @ApiModelProperty("附件上传地址")
    private String attachmentUrl;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("排序")
    private Short sort;

    @ApiModelProperty("附件名称")
    private String attachmentName;
}
