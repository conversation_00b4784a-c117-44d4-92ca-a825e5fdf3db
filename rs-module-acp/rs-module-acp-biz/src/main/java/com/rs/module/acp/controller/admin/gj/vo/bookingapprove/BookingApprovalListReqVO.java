package com.rs.module.acp.controller.admin.gj.vo.bookingapprove;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@ApiModel(description = "管理后台 - 实战平台-管教业务-预约审核管理列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BookingApprovalListReqVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("预约类别")
    private String bookingCategory;

    @ApiModelProperty("服务类别")
    private String serviceCategory;

    @ApiModelProperty("申请时间")
    private Date[] applicationTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批结果")
    private String approvalResultName;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("类型 type为空查询全部   1=今天  2=昨天  3=近一周")
    private Integer type;

    @ApiModelProperty("入所时间")
    private Date rssj;
}
