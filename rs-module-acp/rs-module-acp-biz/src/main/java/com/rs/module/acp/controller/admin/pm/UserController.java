package com.rs.module.acp.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.*;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.UserDO;
import com.rs.module.acp.service.pm.UserService;

@Api(tags = "实战平台-监管管理-用户")
@RestController
@RequestMapping("/acp/pm/user")
@Validated
public class UserController {

    @Resource
    private UserService userService;

   /* @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-用户")
    public CommonResult<String> createUser(@Valid @RequestBody UserSaveReqVO createReqVO) {
        return success(userService.createUser(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-用户")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody UserSaveReqVO updateReqVO) {
        userService.updateUser(updateReqVO);
        return success(true);
    }

    */
   @GetMapping("/deleteGq")
   @ApiOperation(value = "删除实战平台-监管管理-工勤工作人员")
   @ApiImplicitParam(name = "ids", value = "编号")
   public CommonResult<Boolean> deleteUserGq(@RequestParam("ids") String ids) {
       String[] strings = ids.split(",");
       for (String id : strings) {
           userService.deleteUserGq(id);
       }
       return success(true);
   }
    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-用户")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<UserRespVO> getUser(@RequestParam("id") String id) {
        UserDO user = userService.getUser(id);
        return success(BeanUtils.toBean(user, UserRespVO.class));
    }
    @PostMapping("/mjSave")
    @ApiOperation(value = "实战平台-监管管理-民警信息保存")
    public CommonResult<String> mjSave(@Valid @RequestBody UserMJSaveReqVO reqVO, HttpServletRequest request) {
        try {
            return success(userService.mjSave(reqVO,request));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @PostMapping("/fjSave")
    @ApiOperation(value = "实战平台-监管管理-辅警信息保存")
    public CommonResult<String> fjSave(@Valid @RequestBody UserFJSaveReqVO reqVO, HttpServletRequest request) {
        try {
            return success(userService.fjSave(reqVO,request));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @PostMapping("/ywSave")
    @ApiOperation(value = "实战平台-监管管理-医务信息保存")
    public CommonResult<String> ywSave(@Valid @RequestBody UserYWSaveReqVO reqVO, HttpServletRequest request) {
        try {
            return success(userService.ywSave(reqVO,request));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @PostMapping("/gqSave")
    @ApiOperation(value = "实战平台-监管管理-工勤信息保存")
    public CommonResult<String> gqSave(@Valid @RequestBody UserGQSaveReqVO reqVO, HttpServletRequest request) {
        try {
            return success(userService.gqSave(reqVO,request));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @PostMapping("/syncFromToBspUser")
    @ApiOperation(value = "实战平台-监管管理-从BSP同步用户")
    public CommonResult<String> syncFromToBspUser(@RequestParam(required = false) String orgId, HttpServletRequest request) {
        try {
            return success(userService.syncFromToBspUser(orgId,request));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @PostMapping("/syncToBspUserBatch")
    @ApiOperation(value = "实战平台-监管管理-同步指定用户到bsp")
    public CommonResult<String> syncToBspUserBatch(@RequestParam String userIds, HttpServletRequest request) {
        try {
            userService.syncToBspUserBatch(userIds,request);
            return success();
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
}
