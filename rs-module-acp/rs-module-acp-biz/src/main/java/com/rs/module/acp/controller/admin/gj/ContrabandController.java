package com.rs.module.acp.controller.admin.gj;

import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandRespVO;
import com.rs.module.acp.controller.admin.gj.vo.contraband.ContrabandSaveReqVO;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.ContrabandDO;
import com.rs.module.acp.service.gj.contraband.ContrabandService;

@Api(tags = "实战平台-管教业务-违禁品登记")
@RestController
@RequestMapping("/acp/gj/contraband")
@Validated
public class ContrabandController {

    @Resource
    private ContrabandService contrabandService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-违禁品登记")
    public CommonResult<String> createContraband(@Valid @RequestBody ContrabandSaveReqVO createReqVO) {
        createReqVO.setDataSources(DataSourceAppEnum.ACP.getCode());
        return success(contrabandService.createContraband(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-违禁品登记")
    public CommonResult<Boolean> updateContraband(@Valid @RequestBody ContrabandSaveReqVO updateReqVO) {
        contrabandService.updateContraband(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-违禁品登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteContraband(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            contrabandService.deleteContraband(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-违禁品登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ContrabandRespVO> getContraband(@RequestParam("id") String id) {
        ContrabandDO contraband = contrabandService.getContraband(id);
        return success(BeanUtils.toBean(contraband, ContrabandRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-违禁品登记分页")
    public CommonResult<PageResult<ContrabandRespVO>> getContrabandPage(@Valid @RequestBody ContrabandPageReqVO pageReqVO) {
        PageResult<ContrabandDO> pageResult = contrabandService.getContrabandPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContrabandRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-违禁品登记列表")
    public CommonResult<List<ContrabandRespVO>> getContrabandList(@Valid @RequestBody ContrabandListReqVO listReqVO) {
        List<ContrabandDO> list = contrabandService.getContrabandList(listReqVO);
        return success(BeanUtils.toBean(list, ContrabandRespVO.class));
    }

    @GetMapping("/history/list")
    @ApiOperation(value = "获得实战平台-管教业务-历史违禁品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码"),
            @ApiImplicitParam(name = "id", value = "当前违规物品信息")
    })

    public CommonResult<List<ContrabandRespVO>> getContrabandhistoryList(@RequestParam("jgrybm") String jgrybm,
                                                                         @RequestParam(name = "id", required = false) String id) {
        List<ContrabandDO> list = contrabandService.getContrabandhistoryList(jgrybm, id);
        return success(BeanUtils.toBean(list, ContrabandRespVO.class));
    }
}
