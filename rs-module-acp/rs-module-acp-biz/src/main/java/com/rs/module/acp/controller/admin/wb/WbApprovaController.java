package com.rs.module.acp.controller.admin.wb;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.wb.vo.WbApprovaReqVO;
import com.rs.module.acp.service.wb.WbApprovaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-业务审核")
@RestController
@RequestMapping("/acp/wb/approveProcess")
@Validated
public class WbApprovaController {

    @Resource
    private WbApprovaService wbApprovaService;

    @PostMapping("/approve")
    @ApiOperation(value = "实战平台-窗口业务-审批流程")
    @LogRecordAnnotation(bizModule = "acp:approveProcess:approve", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-启动流程审批",
            success = "实战平台-窗口业务-启动流程审批成功", fail = "实战平台-窗口业务-启动流程审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Map<String,Object>> approvalProcess(@RequestBody WbApprovaReqVO approvaReqVO) {
        wbApprovaService.approve(approvaReqVO);
        return success();
    }

    @GetMapping("/getApproveTrack")
    @ApiOperation(value = "实战平台-窗口业务-(测试使用)根据流程实例ID获取流程轨迹")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "actInstId", value = "参数跟BSP一致", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:approveProcess:getApproveTrack", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据流程实例ID获取流程轨迹",
            success = "实战平台-窗口业务-根据流程实例ID获取流程轨迹成功", fail = "实战平台-窗口业务-根据流程实例ID获取流程轨迹失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Map<String,Object>> getApproveTrack(@RequestParam("actInstId") String actInstId) {
        return success(wbApprovaService.getApproveTrack(actInstId));
    }
}
