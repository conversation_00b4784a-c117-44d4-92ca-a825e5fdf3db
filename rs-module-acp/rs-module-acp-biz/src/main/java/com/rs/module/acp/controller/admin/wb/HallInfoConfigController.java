package com.rs.module.acp.controller.admin.wb;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigRespVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoConfigSaveReqVO;
import com.rs.module.acp.service.wb.HallInfoConfigService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;

import com.rs.framework.common.pojo.CommonResult;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-服务大厅信息发布配置")
@RestController
@RequestMapping("/acp/wb/hallInfoConfig")
@Validated
public class HallInfoConfigController {

    @Resource
    private HallInfoConfigService hallInfoConfigService;

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新服务大厅信息发布配置")
    @LogRecordAnnotation(bizModule = "acp:hallInfoConfig:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新服务大厅信息发布配置",
            success = "实战平台-窗口业务-更新服务大厅信息发布配置成功",
            fail = "实战平台-窗口业务-更新服务大厅信息发布配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateHallInfoConfig(@Valid @RequestBody HallInfoConfigSaveReqVO updateReqVO) {
        hallInfoConfigService.updateHallInfoConfig(updateReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得服务大厅信息发布配置")
    @LogRecordAnnotation(bizModule = "acp:hallInfoConfig:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得服务大厅信息发布配置",
            success = "实战平台-窗口业务-获得服务大厅信息发布配置成功",
            fail = "实战平台-窗口业务-获得服务大厅信息发布配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "")
    public CommonResult<HallInfoConfigRespVO> getHallInfoConfig() {
        return success(hallInfoConfigService.getHallInfoConfig());
    }

}
