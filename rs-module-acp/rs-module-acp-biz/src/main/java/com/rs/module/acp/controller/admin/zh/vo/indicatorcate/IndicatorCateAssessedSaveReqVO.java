package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类与被考评人关联新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorCateAssessedSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("指标分类ID")
    //@NotEmpty(message = "指标分类ID不能为空")
    private String indicatorCateId;

    @ApiModelProperty("被考核对象类型，01:岗位、02：角色、03：用户")
    //@NotEmpty(message = "被考核对象类型，01:岗位、02：角色、03：用户不能为空")
    private String assessedObjectType;

    @ApiModelProperty("被考核对象id, 选择岗位时 此值为岗位code, 选择校色时，值此为角色code, 选择用户时，此值为用户sfzh")
    @NotEmpty(message = "被考核对象id不能为空")
    private String assessedObjectId;

    @ApiModelProperty("被考核对象name, 选择岗位时 此值为岗位name, 选择校色时，值此为角色name, 选择用户时，此值为用户name")
    @NotEmpty(message = "被考核对象name不能为空")
    private String assessedObjectName;

}
