package com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-协助破案新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssistanceSolveCaseClueApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键不能为空")
    private String id;

    @ApiModelProperty("状态 02: 通过  05 不通过")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("审核意见")
    private String approvalComments;

}
