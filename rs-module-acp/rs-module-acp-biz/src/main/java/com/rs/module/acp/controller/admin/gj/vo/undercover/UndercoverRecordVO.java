package com.rs.module.acp.controller.admin.gj.vo.undercover;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-耳目管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UndercoverRecordVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    private String status;

    @ApiModelProperty("布建申请人")
    private String addUserName;


    @ApiModelProperty("布建理由")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJXXYBJLY")
    private String arrangeReason;


    @ApiModelProperty("布建申请时间")
    private Date addTime;
}
