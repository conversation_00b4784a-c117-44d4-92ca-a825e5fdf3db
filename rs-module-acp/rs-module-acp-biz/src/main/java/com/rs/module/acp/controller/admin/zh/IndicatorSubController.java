package com.rs.module.acp.controller.admin.zh;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorSubSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.zh.IndicatorSubDO;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorSubService;

@Api(tags = "综合管理-绩效考核子指标")
@RestController
@RequestMapping("/acp/zh/indicatorSub")
@Validated
public class IndicatorSubController {

    @Resource
    private IndicatorSubService indicatorSubService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核子指标")
    public CommonResult<String> createIndicatorSub(@Valid @RequestBody IndicatorSubSaveReqVO createReqVO) {
        return success(indicatorSubService.createIndicatorSub(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核子指标")
    public CommonResult<Boolean> updateIndicatorSub(@Valid @RequestBody IndicatorSubSaveReqVO updateReqVO) {
        indicatorSubService.updateIndicatorSub(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核子指标")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteIndicatorSub(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           indicatorSubService.deleteIndicatorSub(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核子指标")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<IndicatorSubRespVO> getIndicatorSub(@RequestParam("id") String id) {
        IndicatorSubDO indicatorSub = indicatorSubService.getIndicatorSub(id);
        return success(BeanUtils.toBean(indicatorSub, IndicatorSubRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核子指标分页")
    public CommonResult<PageResult<IndicatorSubRespVO>> getIndicatorSubPage(@Valid @RequestBody IndicatorSubPageReqVO pageReqVO) {
        PageResult<IndicatorSubDO> pageResult = indicatorSubService.getIndicatorSubPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndicatorSubRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核子指标列表")
    public CommonResult<List<IndicatorSubRespVO>> getIndicatorSubList(@Valid @RequestBody IndicatorSubListReqVO listReqVO) {
        List<IndicatorSubDO> list = indicatorSubService.getIndicatorSubList(listReqVO);
        return success(BeanUtils.toBean(list, IndicatorSubRespVO.class));
    }
}
