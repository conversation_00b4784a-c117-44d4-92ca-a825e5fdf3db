package com.rs.module.acp.controller.admin.gj.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-戒具检查新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseCheckSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("械具使用ID")
    private String equipmentUseId;

    @ApiModelProperty("检查时间")
    private Date checkTime;

    @ApiModelProperty("检查人身份证号")
    private String checkUserSfzh;

    @ApiModelProperty("检查人")
    private String checkUser;

    @ApiModelProperty("检查结果(01:正常,02:异常)")
    private String checkResult;

    @ApiModelProperty("检查情况")
    private String checkSituation;

}
