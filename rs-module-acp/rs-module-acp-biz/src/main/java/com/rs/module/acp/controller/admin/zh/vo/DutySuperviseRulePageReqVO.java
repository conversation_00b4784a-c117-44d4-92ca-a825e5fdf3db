package com.rs.module.acp.controller.admin.zh.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;

import java.time.LocalTime;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-值班管理-值班督导规则配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DutySuperviseRulePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("排班模板ID")
    private String staffDutyTemplateId;

    @ApiModelProperty("值班岗位ID")
    private String staffDutyPostId;

    @ApiModelProperty("值班模板关联角色ID")
    private String staffDutyRoleId;

    @ApiModelProperty("签到验证模式")
    private String signinValidMode;

    @ApiModelProperty("超时阀值")
    private Integer timeoutThreshold;

    @ApiModelProperty("超时推送对象")
    private String timeoutPushTarget;

    @ApiModelProperty("超时次数阀值")
    private Integer todayTimeoutCount;

    @ApiModelProperty("本日超时次数推送对象")
    private String todayTimeoutCountPushTarget;

    @ApiModelProperty("签到间隔")
    private Integer signinInterval;

    @ApiModelProperty("签到时段开始")
//    @JsonFormat(pattern = "HH:mm")
    private String signinStartTime;

    @ApiModelProperty("签到时段结束")
//    @JsonFormat(pattern = "HH:mm")
    private String signinEndTime;

    @ApiModelProperty("补签时间")
    private Integer lateSigninTime;

    @ApiModelProperty("超时推送对象身份证号")
    private String timeoutPushTargetSfzh;

    @ApiModelProperty("本日超时次数推送对象身份证号")
    private String todayTimeoutCountPushTargetSfzh;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
