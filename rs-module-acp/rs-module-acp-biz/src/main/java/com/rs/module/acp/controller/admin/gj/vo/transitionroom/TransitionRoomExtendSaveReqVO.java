package com.rs.module.acp.controller.admin.gj.vo.transitionroom;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡监室延长呈批新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransitionRoomExtendSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("过渡监室ID")
    @NotEmpty(message = "过渡监室ID不能为空")
    private String transitionRoomId;

    @ApiModelProperty("延长理由")
    @NotEmpty(message = "延长理由不能为空")
    private String reason;

    @ApiModelProperty("延长天数")
    @NotNull(message = "延长天数不能为空")
    private Integer extendDay;

    //@ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    //private String status;
    //
    //@ApiModelProperty("审批人身份证号")
    //private String approverSfzh;
    //
    //@ApiModelProperty("审批人姓名")
    //private String approverXm;
    //
    //@ApiModelProperty("审批时间")
    //private Date approverTime;
    //
    //@ApiModelProperty("审批结果")
    //private String approvalResult;
    //
    //@ApiModelProperty("审批人签名")
    //private String approvalAutograph;
    //
    //@ApiModelProperty("审批人签名日期")
    //private Date approvalAutographTime;
    //
    //@ApiModelProperty("审核意见")
    //private String approvalComments;
    //
    //@ApiModelProperty("ACT流程实例Id")
    //private String actInstId;
    //
    //@ApiModelProperty("任务ID")
    //private String taskId;

}
