package com.rs.module.acp.controller.admin.pm;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bsp.common.cons.CommonConstants;
import com.bsp.common.util.R;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.exception.ExceptionLogUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.controller.admin.pm.vo.CusAppRespVO;
import com.rs.module.base.controller.admin.pm.vo.CusAppSaveReqVO;
import com.rs.module.base.controller.admin.pm.vo.CusAppUacRespVO;
import com.rs.module.base.controller.admin.pm.vo.CusAppUacSaveReqVO;
import com.rs.module.base.entity.pm.CusAppDO;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.module.base.service.pm.CusAppService;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "实战平台-自定义应用管理")
@RestController
@RequestMapping("/acp/pm/cusApp")
@Validated
public class CusAppController {

    @Resource
    private CusAppService cusAppService;

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "创建或者更新自定义应用管理")
    public CommonResult<String> createCusApp(@Valid @RequestBody CusAppSaveReqVO createReqVO) {
        return success(cusAppService.createOrUpdateCusApp(createReqVO));
    }

    @ApiOperation("根据ID批量删除应用信息（ids，用逗号隔开）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "ids（用逗号隔开）", required = true, paramType = "query", dataType = "String")
    })
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public CommonResult<?> delByIds(@RequestParam(value = "ids") String ids) {
        try {
            List<String> idList = StrUtil.splitTrim(ids, CommonConstants.DEFAULT_SPLIT_STR);
            if (!CollectionUtils.isEmpty(idList)) {
                cusAppService.delByIds(idList);
            }
            return success("删除成功");
        } catch (Exception e) {
            ExceptionLogUtil.handle(e);
            return error("删除失败,原因:" + e.getMessage());
        }
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得自定义应用管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CusAppRespVO> getCusApp(@RequestParam("id") String id) {
        CusAppDO cusApp = cusAppService.getCusApp(id);
        return success(BeanUtils.toBean(cusApp, CusAppRespVO.class));
    }

    @ApiOperation(value = "设置应用权限")
    @PostMapping("/assignPermissions")
    public CommonResult<String> assignPermissions(@Valid CusAppUacSaveReqVO saveReqVO) {
        cusAppService.savePermissions(saveReqVO);
        return success("设置成功");
    }

    @ApiOperation(value = "获取应用权限", response = CusAppUacRespVO.class)
    @ApiImplicitParam(name = "yyid", value = "应用ID", required = true, paramType = "query", dataType = "String")
    @GetMapping("/getPermissions")
    public CommonResult<CusAppUacRespVO> getPermissions(@RequestParam(value = "yyid") String yyid) {
        CusAppUacRespVO result = cusAppService.getPermissions(yyid);
        return success(result);
    }

    @ApiOperation(value = "获取全部应用")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "yylx", value = "应用类型(0:pc,1:移动端)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "systemId", value = "系统ID", paramType = "query", dataType = "String")
    })
    @GetMapping("/getAllApply")
    public R getAllApply(@RequestParam(value = "yylx", required = false) String yylx,
                         @RequestParam(value = "systemId", required = true) String systemId) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String roleIds = sessionUser.getRoleIds();
        String orgCode = sessionUser.getOrgCode();
        String regCode = sessionUser.getRegCode();
        List<Map<String, Object>> allApply = cusAppService.getAllApply(roleIds, orgCode, regCode, yylx, systemId, null);
        Map<String, List<Map<String, Object>>> tempMap = new LinkedHashMap<>();
        for (Map<String, Object> map : allApply) {
            String flmc = (String) map.get("flmc");
            String preUrl = (String) map.get("pre_url");
            String ljdz = (String) map.get("ljdz");
            boolean sfnb = StringUtil.getBoolean((String)map.get("sfnb"));
            
            //处理链接地址
            if(StringUtil.isNotEmpty(preUrl) && StringUtil.isNotEmpty(ljdz) && !ljdz.toLowerCase().startsWith("http") && !sfnb) {
            	map.put("ljdz", preUrl + ljdz);
            }
            
            //处理分类
            List<Map<String, Object>> list = tempMap.get(flmc);
            if (list == null) {
                list = new LinkedList<>();
                tempMap.put(flmc, list);
            }
            list.add(map);
        }
        List<Map<String, Object>> result = new ArrayList<>();
        // sorted by flmc
        Set<String> keySet = tempMap.keySet();
        String[] array = keySet.toArray(new String[0]);
        Arrays.stream(array).forEach(flmc -> {
            Map<String, Object> temp = new HashMap<>();
            temp.put("typeName", flmc);
            temp.put("typeList", tempMap.get(flmc));
            result.add(temp);
        });
        return R.success().putData(result);
    }

    @ApiOperation(value = "获取指定分类的应用")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "yylx", value = "应用类型(0:pc,1:移动端)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "systemId", value = "系统ID", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "mark", value = "应用分类标识", paramType = "query", dataType = "String")
    })
    @GetMapping("/getCatApply")
    @BusTrace(busType = BusTypeEnum.YEWU_AQJC, condition = "false",
    	content = "{\"系统Id\":\"{{#systemId}}\", \"业务标识\":\"{{#mark}}\"}")
    public R getCatApply(@RequestParam(value = "yylx", required = false) String yylx,
                         @RequestParam(value = "systemId", required = true) String systemId,
                         @RequestParam(value = "mark", required = true) String mark) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        String roleIds = sessionUser.getRoleIds();
        String orgCode = sessionUser.getOrgCode();
        String regCode = sessionUser.getRegCode();
        List<Map<String, Object>> catApply = cusAppService.getAllApply(roleIds, orgCode, regCode, yylx, systemId, mark);
        
        //处理链接地址
        for (Map<String, Object> map : catApply) {
        	String preUrl = (String) map.get("pre_url");
            String ljdz = (String) map.get("ljdz");
            boolean sfnb = StringUtil.getBoolean((String)map.get("sfnb"));
        	if(StringUtil.isNotEmpty(preUrl) && StringUtil.isNotEmpty(ljdz) && !ljdz.toLowerCase().startsWith("http") && !sfnb) {
            	map.put("ljdz", preUrl + ljdz);
            }
        }
        
        return R.success().putData(catApply);
    }
}
