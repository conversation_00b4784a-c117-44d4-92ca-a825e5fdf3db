package com.rs.module.acp.controller.admin.gj.vo.prisonroom;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonRoomChangeApproveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @NotBlank(message = "ID不能为空")
    private String id;

    @ApiModelProperty(value = "状态-字典-ZD_GJ_JSTZZT")
    private String status;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批意见")
    private String approvalComments;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approverTime;


}
