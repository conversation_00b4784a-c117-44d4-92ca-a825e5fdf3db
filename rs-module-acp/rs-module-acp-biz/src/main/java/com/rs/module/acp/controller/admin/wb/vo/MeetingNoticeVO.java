package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-会见通知 Response VO")
@Data
public class MeetingNoticeVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("业务类型名称")
    private String businessTypeName;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("会见方式")
    private String meetingMethod;

    @ApiModelProperty("会见事由")
    private String reason;

    @ApiModelProperty("会见事由名称")
    private String reasonName;

    @ApiModelProperty("预约会见时间")
    private String meetingTime;
}
