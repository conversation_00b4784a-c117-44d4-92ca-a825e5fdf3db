package com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明个人登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedPersonneRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("评比月份")
    private String evalMonth;
    @ApiModelProperty("文明个人")
    private String civilizedPersonne;
    @ApiModelProperty("申请时间")
    private Date applyTime;
    @ApiModelProperty("申请人身份证号")
    private String applyUserSfzh;
    @ApiModelProperty("申请人")
    private String applyUser;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WMGRZT")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("文明个人信息列表")
    private List<CivilizedPersonneDetailRespVO> list;
}
