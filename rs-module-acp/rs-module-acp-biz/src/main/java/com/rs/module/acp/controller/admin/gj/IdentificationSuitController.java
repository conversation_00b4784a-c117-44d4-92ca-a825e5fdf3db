package com.rs.module.acp.controller.admin.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.identificationsuit.IdentificationSuitSaveReqVO;
import com.rs.module.acp.service.gj.identificationsuit.IdentificationSuitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-识别服管理")
@RestController
@RequestMapping("/acp/gj/identificationSuit")
@Validated
public class IdentificationSuitController {

    @Resource
    private IdentificationSuitService identificationSuitService;

    @GetMapping("/getSuitNumByRoomId")
    @ApiOperation(value = "获得可用的识别服号")
    @ApiImplicitParam(name = "roomId", value = "监室号")
    public CommonResult<List<String>> getSuitNumByRoomId(@RequestParam("roomId") String roomId) {
        return success(identificationSuitService.getSuitNumByRoomId(roomId));
    }

    @PostMapping("/editByJgrybm")
    @ApiOperation(value = "编辑识别服")
    public CommonResult<Boolean> editByJgrybm(@Valid @RequestBody IdentificationSuitSaveReqVO createReqVO) {
        identificationSuitService.editByJgrybm(createReqVO);
        return success(true);
    }
}
