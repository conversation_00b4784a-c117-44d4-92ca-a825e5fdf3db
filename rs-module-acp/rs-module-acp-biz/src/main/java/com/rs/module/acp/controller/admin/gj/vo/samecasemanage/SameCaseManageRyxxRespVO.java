package com.rs.module.acp.controller.admin.gj.vo.samecasemanage;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-同案人员管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SameCaseManageRyxxRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("案件编号")
    private String ajbh;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("主键")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String xb;

    @ApiModelProperty("涉嫌罪名")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SXZM")
    private String sxzm;

    @ApiModelProperty("入所时间")
    private Date rssj;

    @ApiModelProperty("关押期限")
    private Date gyqx;

    @ApiModelProperty("办案环节")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SSJD")
    private String sshj;

    @ApiModelProperty("办案单位")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BADW_GAJG")
    private String badw;

    @ApiModelProperty("添加时间")
    private Date addTime;

    @ApiModelProperty("同案人类型 0 不可删除 1 可删除")
    private String tarType;
}
