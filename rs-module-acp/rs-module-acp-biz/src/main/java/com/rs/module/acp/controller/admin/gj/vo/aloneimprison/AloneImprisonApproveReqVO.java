package com.rs.module.acp.controller.admin.gj.vo.aloneimprison;

import com.rs.module.base.vo.ApproveReqVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


@ApiModel(description = "管理后台 - 实战平台-管教业务-单独关押登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class AloneImprisonApproveReqVO extends ApproveReqVO {
    private static final long serialVersionUID = 1L;

}
