package com.rs.module.acp.controller.admin.zh.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-值班管理-值班督导记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DutySuperviseRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("排班模板ID")
    private String staffDutyTemplateId;

    @ApiModelProperty("值班岗位ID")
    private String staffDutyPostId;

    @ApiModelProperty("值班模板关联角色ID")
    private String staffDutyRoleId;

    @ApiModelProperty("值班日期")
    private Date dutyDate;

    @ApiModelProperty("签到验证模式")
    private String signinValidMode;

    @ApiModelProperty("签到状态")
    private String signinStatus;

    @ApiModelProperty("签到时间")
    private Date signinTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
