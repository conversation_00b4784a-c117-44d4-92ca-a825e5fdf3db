package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情关联业务 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventBusinessRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("业务类型")
    private String businessType;
    @ApiModelProperty("业务对象")
    private String businessObject;
    @ApiModelProperty("办理人")
    private String operatePerson;
    @ApiModelProperty("办理时间")
    private Date operateTime;
    @ApiModelProperty("业务主表id")
    private String businessId;
    @ApiModelProperty("警情主表id")
    private String eventId;
    @ApiModelProperty("0-登记 1-已完成可编辑")
    private String status;
    @ApiModelProperty("登记人岗位名称")
    private String createUserRoleName;
    @ApiModelProperty("业务对象ID")
    private String businessObjectId;
    @ApiModelProperty("处置id")
    private String handleId;
    @ApiModelProperty("处理方式")
    private String handleMethod;
    @ApiModelProperty("医疗变动内容")
    private String changeContent;
    @ApiModelProperty("附件地址")
    private String attUrl;
}
