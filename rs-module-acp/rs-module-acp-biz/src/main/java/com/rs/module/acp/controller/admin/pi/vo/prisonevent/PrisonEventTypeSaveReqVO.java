package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件类型新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventTypeSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("事件类型名称")
    private String typeName;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("事件明细树形List")
    private List<PrisonEventTypeItemSaveReqVO> eventItemList;

    @ApiModelProperty("处置模板List")
    private List<PrisonEventDisposeTemplateSaveReqVO> disposeTemplateList;

    @ApiModelProperty("推送岗位List")
    private List<PrisonEventPushSettingSaveReqVO> pushSettingList;

}
