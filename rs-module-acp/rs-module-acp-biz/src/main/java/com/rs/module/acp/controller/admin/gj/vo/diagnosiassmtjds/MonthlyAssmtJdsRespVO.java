package com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-月度考核(戒毒所) Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MonthlyAssmtJdsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("推送时间")
    private Date pushTime;
    @ApiModelProperty("考核内容值1")
    private String assmtContentValue1;
    @ApiModelProperty("考核内容值2")
    private String assmtContentValue2;
    @ApiModelProperty("考核内容值3")
    private String assmtContentValue3;
    @ApiModelProperty("考核内容值4")
    private String assmtContentValue4;
    @ApiModelProperty("考核内容值5")
    private String assmtContentValue5;
    @ApiModelProperty("考核内容值6")
    private String assmtContentValue6;
    @ApiModelProperty("考核内容值7")
    private String assmtContentValue7;
    @ApiModelProperty("考核内容值8")
    private String assmtContentValue8;
    @ApiModelProperty("考核内容值9")
    private String assmtContentValue9;
    @ApiModelProperty("考核内容值10")
    private String assmtContentValue10;
    @ApiModelProperty("考核内容值10")
    private String assmtContentTotalScore;
    @ApiModelProperty("加分或扣分情况")
    private String bonusOrPenaltyPointsSituati;
    @ApiModelProperty("评估内容JSON")
    private String asstmContentJson;
    @ApiModelProperty("评估人身份证号")
    private String assmtUserSfzh;
    @ApiModelProperty("评估人姓名")
    private String assmtUser;
    @ApiModelProperty("评估时间")
    private String assmtTime;
    @ApiModelProperty("办理状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PGZD_YDKHZT")
    private String status;
    @ApiModelProperty("确认时间")
    private Date confirmTime;
    @ApiModelProperty("在押人员签名地址")
    private String prisonerSignature;

    @ApiModelProperty("月度考核周期")
    private String monthPeriod;

    @ApiModelProperty("依据")
    private String according;
}
