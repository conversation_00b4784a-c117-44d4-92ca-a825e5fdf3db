package com.rs.module.acp.controller.admin.gj;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.entity.gj.confinement.ConfinementExtendDO;
import com.rs.module.acp.service.gj.confinement.ConfinementExtendService;

@Api(tags = "实战平台-管教业务-延长禁闭呈批")
@RestController
@RequestMapping("/acp/gj/confinementExtend")
@Validated
public class ConfinementExtendController {

    @Resource
    private ConfinementExtendService confinementExtendService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-延长禁闭呈批")
    public CommonResult<String> createConfinementExtend(@Valid @RequestBody ConfinementExtendSaveReqVO createReqVO) {
        try{
            return success(confinementExtendService.createConfinementExtend(createReqVO));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
/*
    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-延长禁闭呈批")
    public CommonResult<Boolean> updateConfinementExtend(@Valid @RequestBody ConfinementExtendSaveReqVO updateReqVO) {
        confinementExtendService.updateConfinementExtend(updateReqVO);
        return success(true);
    }*/

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-延长禁闭呈批")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteConfinementExtend(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           confinementExtendService.deleteConfinementExtend(id);
        }
        return success(true);
    }
/*
    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-延长禁闭呈批")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ConfinementExtendRespVO> getConfinementExtend(@RequestParam("id") String id) {
        ConfinementExtendDO confinementExtend = confinementExtendService.getConfinementExtend(id);
        return success(BeanUtils.toBean(confinementExtend, ConfinementExtendRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-延长禁闭呈批分页")
    public CommonResult<PageResult<ConfinementExtendRespVO>> getConfinementExtendPage(@Valid @RequestBody ConfinementExtendPageReqVO pageReqVO) {
        PageResult<ConfinementExtendDO> pageResult = confinementExtendService.getConfinementExtendPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ConfinementExtendRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-延长禁闭呈批列表")
    public CommonResult<List<ConfinementExtendRespVO>> getConfinementExtendList(@Valid @RequestBody ConfinementExtendListReqVO listReqVO) {
        List<ConfinementExtendDO> list = confinementExtendService.getConfinementExtendList(listReqVO);
        return success(BeanUtils.toBean(list, ConfinementExtendRespVO.class));
    }*/
    @PostMapping("/getApproveTrack")
    @ApiOperation(value = "获得实战平台-管教业务-解除禁闭业务轨迹")
    public CommonResult<List<ConfinementFlowApproveTrackVO>> getApproveTrack(@RequestParam("id") String id) {
        List<ConfinementFlowApproveTrackVO> list = confinementExtendService.getApproveTrack(id);
        return success(list);
    }
    @PostMapping("/approve")
    @ApiOperation(value = "领导审批")
    public CommonResult<Boolean> approve(@Valid @RequestBody ConfinementExtendApproveVO approveReqVO) {
        try {
            return success(confinementExtendService.approve(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
}
