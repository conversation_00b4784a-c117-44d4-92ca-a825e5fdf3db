package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("courses_code")
    private String coursesCode;
    @ApiModelProperty("courses_name")
    private String coursesName;
    @ApiModelProperty("分类颜色")
    private String coursesColor;
    @ApiModelProperty("是否启用")
    private Short isEnabled;
}
