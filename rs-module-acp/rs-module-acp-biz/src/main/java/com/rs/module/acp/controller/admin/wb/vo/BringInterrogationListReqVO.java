package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-提询登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BringInterrogationListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("第一位办案人ID")
    private String handler1Id;

    @ApiModelProperty("第一位办案人姓名")
    private String handler1Xm;

    @ApiModelProperty("第二位办案人ID")
    private String handler2Id;

    @ApiModelProperty("第二位办案人姓名")
    private String handler2Xm;

    @ApiModelProperty("第三位办案人ID")
    private String handler3Id;

    @ApiModelProperty("第三位办案人姓名")
    private String handler3Xm;

    @ApiModelProperty("审讯室ID")
    private String roomId;

    @ApiModelProperty("提询原因")
    private String arraignmentReason;

    @ApiModelProperty("申请提询开始时间")
    private Date[] startApplyArraignmentTime;

    @ApiModelProperty("申请提询结束时间")
    private Date[] endApplyArraignmentTime;

    @ApiModelProperty("介绍信编号")
    private String arraignmentLetterNumber;

    @ApiModelProperty("提询凭证类型")
    private String evidenceType;

    @ApiModelProperty("所选的凭证证号")
    private String evidenceNumber;

    @ApiModelProperty("上传提询凭证URL")
    private String evidenceUrl;

    @ApiModelProperty("未成年代理人")
    private String minorAgent;

    @ApiModelProperty("未成年监护人")
    private String minorGuardian;

    @ApiModelProperty("翻译人员")
    private String translator;

    @ApiModelProperty("其他专业人员")
    private String otherProfessionals;

    @ApiModelProperty("其他专业人员书面证明存储路径")
    private String professionalCertUrl;

    @ApiModelProperty("提解机关类型")
    private String tjjglx;

    @ApiModelProperty("提解机关名称")
    private String tjjgmc;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("签到时间")
    private Date[] checkInTime;

    @ApiModelProperty("签到用户身份证号")
    private String checkInPoliceSfzh;

    @ApiModelProperty("签到用户")
    private String checkInPolice;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date[] escortingTime;

    @ApiModelProperty("带出操作人身份证号")
    private String escortingOperatorSfzh;

    @ApiModelProperty("带出操作人")
    private String escortingOperator;

    @ApiModelProperty("带出操作时间")
    private Date[] escortingOperatorTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;

    @ApiModelProperty("检查时间")
    private Date[] inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("提讯开始时间")
    private Date[] arraignmentStartTime;

    @ApiModelProperty("提讯结束时间")
    private Date[] arraignmentEndTime;

    @ApiModelProperty("会毕检查人")
    private String returnInspectorSfzh;

    @ApiModelProperty("会毕检查人")
    private String returnInspector;

    @ApiModelProperty("会毕检查时间")
    private Date[] returnInspectionTime;

    @ApiModelProperty("会毕检查结果")
    private String returnInspectionResult;

    @ApiModelProperty("带回违禁物品登记")
    private String returnProhibitedItems;

    @ApiModelProperty("带回体表检查登记")
    private String returnPhysicalExam;

    @ApiModelProperty("带回异常情况登记")
    private String returnAbnormalSituations;

    @ApiModelProperty("带回监室时间")
    private Date[] returnTime;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

    @ApiModelProperty("带回民警身份证号")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回操作人身份证号")
    private String returnOperatorSfzh;

    @ApiModelProperty("带回操作人")
    private String returnOperator;

    @ApiModelProperty("带回操作时间")
    private Date[] returnOperatorTime;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
