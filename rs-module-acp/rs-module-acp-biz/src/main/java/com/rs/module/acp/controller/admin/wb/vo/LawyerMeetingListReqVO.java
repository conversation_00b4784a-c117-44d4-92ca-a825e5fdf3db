package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师会见登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerMeetingListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("第一位律师ID")
    private String lawyer1Id;

    @ApiModelProperty("第一位律师姓名")
    private String lawyer1Name;

    @ApiModelProperty("第一位律师性别")
    private String lawyer1Gender;

    @ApiModelProperty("第一位律师证件号码")
    private String lawyer1IdNumber;

    @ApiModelProperty("第一位律师联系方式")
    private String lawyer1Contact;

    @ApiModelProperty("第一位律师类型")
    private String lawyer1Type;

    @ApiModelProperty("第一位律师执业证号码")
    private String lawyer1PracticeLicenseNumber;

    @ApiModelProperty("第一位律师委托截止阶段")
    private String lawyer1EntrustStage;

    @ApiModelProperty("第一位律师所属单位")
    private String lawyer1Firm;

    @ApiModelProperty("第一位律师委托类型")
    private String lawyer1EntrustType;

    @ApiModelProperty("第一位律师委托人姓名")
    private String lawyer1PrincipalName;

    @ApiModelProperty("第一位律师委托人证件号")
    private String lawyer1PrincipalIdNumber;

    @ApiModelProperty("第一位律师委托书类型")
    private String lawyer1PowerOfAttorneyType;

    @ApiModelProperty("第一位律师介绍信编号")
    private String lawyer1LetterNumber;

    @ApiModelProperty("第一位律师委托书上传后存储路径")
    private String lawyer1PowerOfAttorneyPath;

    @ApiModelProperty("第一位律师执业证书上传后存储路径")
    private String lawyer1PracticeCertificatePa;

    @ApiModelProperty("第二位律师ID")
    private String lawyer2Id;

    @ApiModelProperty("第二位律师姓名")
    private String lawyer2Name;

    @ApiModelProperty("第二位律师性别")
    private String lawyer2Gender;

    @ApiModelProperty("第二位律师证件号码")
    private String lawyer2IdNumber;

    @ApiModelProperty("第二位律师联系方式")
    private String lawyer2Contact;

    @ApiModelProperty("第二位律师类型")
    private String lawyer2Type;

    @ApiModelProperty("第二位律师执业证号码")
    private String lawyer2PracticeLicenseNumber;

    @ApiModelProperty("第二位律师委托截止阶段")
    private String lawyer2EntrustStage;

    @ApiModelProperty("第二位律师所属单位")
    private String lawyer2Firm;

    @ApiModelProperty("第二位律师委托类型")
    private String lawyer2EntrustType;

    @ApiModelProperty("第二位律师介绍信编号")
    private String lawyer2LetterNumber;

    @ApiModelProperty("第二位律师委托人姓名")
    private String lawyer2PrincipalName;

    @ApiModelProperty("第二位律律师委托人证件号")
    private String lawyer2PrincipalIdNumber;

    @ApiModelProperty("第二位律律师委托书类型")
    private String lawyer2PowerOfAttorneyType;

    @ApiModelProperty("第二位律律师委托书上传后的存储路径")
    private String lawyer2PowerOfAttorneyPath;

    @ApiModelProperty("第二位律律师执业证书上传后存储路径")
    private String lawyer2PracticeCertificatePa;

    @ApiModelProperty("第三位律师ID")
    private String lawyer3Id;

    @ApiModelProperty("第三位律师姓名")
    private String lawyer3Name;

    @ApiModelProperty("第三位律师性别")
    private String lawyer3Gender;

    @ApiModelProperty("第三位律师证件号码")
    private String lawyer3IdNumber;

    @ApiModelProperty("第三位律师联系方式")
    private String lawyer3Contact;

    @ApiModelProperty("第三位律师类型")
    private String lawyer3Type;

    @ApiModelProperty("第三位律师执业证号码")
    private String lawyer3PracticeLicenseNumber;

    @ApiModelProperty("第三位律师委托截止阶段")
    private String lawyer3EntrustStage;

    @ApiModelProperty("第三位律师所属单位")
    private String lawyer3Firm;

    @ApiModelProperty("第三位律师委托类型")
    private String lawyer3EntrustType;

    @ApiModelProperty("第三位律师介绍信编号")
    private String lawyer3LetterNumber;

    @ApiModelProperty("第三位律师委托人姓名")
    private String lawyer3PrincipalName;

    @ApiModelProperty("第三位律律师委托人证件号")
    private String lawyer3PrincipalIdNumber;

    @ApiModelProperty("第三位律律师委托书类型")
    private String lawyer3PowerOfAttorneyType;

    @ApiModelProperty("第三位律律师委托书上传后的存储路径")
    private String lawyer3PowerOfAttorneyPath;

    @ApiModelProperty("第三位律律师执业证书上传后存储路径")
    private String lawyer3PracticeCertificatePa;

    @ApiModelProperty("第四位律师ID")
    private String lawyer4Id;

    @ApiModelProperty("第四位律师姓名")
    private String lawyer4Name;

    @ApiModelProperty("第四位律师性别")
    private String lawyer4Gender;

    @ApiModelProperty("第四位律师证件号码")
    private String lawyer4IdNumber;

    @ApiModelProperty("第四位律师联系方式")
    private String lawyer4Contact;

    @ApiModelProperty("第四位律师类型")
    private String lawyer4Type;

    @ApiModelProperty("第四位律师执业证号码")
    private String lawyer4PracticeLicenseNumber;

    @ApiModelProperty("第四位律师委托截止阶段")
    private String lawyer4EntrustStage;

    @ApiModelProperty("第四位律师所属单位")
    private String lawyer4Firm;

    @ApiModelProperty("第四位律师委托类型")
    private String lawyer4EntrustType;

    @ApiModelProperty("第四位律师委托人姓名")
    private String lawyer4PrincipalName;

    @ApiModelProperty("第四位律律师委托人证件号")
    private String lawyer4PrincipalIdNumber;

    @ApiModelProperty("第四位律律师委托书类型")
    private String lawyer4PowerOfAttorneyType;

    @ApiModelProperty("第四位律律师委托书上传后的存储路径")
    private String lawyer4PowerOfAttorneyPath;

    @ApiModelProperty("第四位律律师执业证书上传后存储路径")
    private String lawyer4PracticeCertificatePa;

    @ApiModelProperty("第四位律师介绍信编号")
    private String lawyer4LetterNumber;

    @ApiModelProperty("会见方式")
    private String meetingMethod;

    @ApiModelProperty("预约会见时间")
    private Date[] appointmentTime;

    @ApiModelProperty("预约会见时间")
    private String appointmentTimeSlot;

    @ApiModelProperty("会见所在房间编号")
    private String roomId;

    @ApiModelProperty("会见批准机关")
    private String approvalAuthority;

    @ApiModelProperty("许可决定文书号")
    private String approvalDocumentNumber;

    @ApiModelProperty("上传会见批准材料附件")
    private String approvalAttachmentPath;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("分配房间时间")
    private Date[] assignmentRoomTime;

    @ApiModelProperty("分配民警身份证号")
    private String assignmentPoliceSfzh;

    @ApiModelProperty("assignment_police")
    private String assignmentPolice;

    @ApiModelProperty("签到时间")
    private Date[] checkInTime;

    @ApiModelProperty("签到用户身份证号")
    private String checkInPoliceSfzh;

    @ApiModelProperty("签到用户")
    private String checkInPolice;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date[] escortingTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;

    @ApiModelProperty("检查时间")
    private Date[] inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("会见开始时间")
    private Date[] meetingStartTime;

    @ApiModelProperty("会见结束时间")
    private Date[] meetingEndTime;

    @ApiModelProperty("会毕检查人")
    private String returnInspectorSfzh;

    @ApiModelProperty("会毕检查人")
    private String returnInspector;

    @ApiModelProperty("会毕检查时间")
    private Date[] returnInspectionTime;

    @ApiModelProperty("会毕检查结果")
    private String returnInspectionResult;

    @ApiModelProperty("带回违禁物品登记")
    private String returnProhibitedItems;

    @ApiModelProperty("带回体表检查登记")
    private String returnPhysicalExam;

    @ApiModelProperty("带回异常情况登记")
    private String returnAbnormalSituations;

    @ApiModelProperty("带回监室时间")
    private Date[] returnTime;

    @ApiModelProperty("return_police_sfzh")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

    @ApiModelProperty("带出操作人身份证号")
    private String escortingOperatorSfzh;

    @ApiModelProperty("带出操作人")
    private String escortingOperator;

    @ApiModelProperty("带出操作时间")
    private Date[] escortingOperatorTime;

    @ApiModelProperty("带回操作人身份证号")
    private String returnOperatorSfzh;

    @ApiModelProperty("带回操作人")
    private String returnOperator;

    @ApiModelProperty("带回操作时间")
    private Date[] returnOperatorTime;

    @ApiModelProperty("快速会见/远程会见-预约会见开始时间")
    private Date[] applyMeetingStartTime;

    @ApiModelProperty("快速会见/远程会见-预约会见结束时间")
    private Date[] applyMeetingEndTime;

    @ApiModelProperty("法律援助公函号")
    private String legalAidOfficialLetterNumber;
}
