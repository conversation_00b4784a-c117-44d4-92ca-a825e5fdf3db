package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件推送设置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventPushSettingRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("处置业务（支持多个用逗号分隔）")
    private String disposeBusiness;
    @ApiModelProperty("处置预案")
    private String disposePlans;
    @ApiModelProperty("所情事件类型ID")
    private String typeId;
    @ApiModelProperty("排序号")
    private Integer orderId;
    @ApiModelProperty("推送岗位ID")
    @Trans(type = TransType.DICTIONARY, key = "ZD_POST")
    private String pushPostId;
}
