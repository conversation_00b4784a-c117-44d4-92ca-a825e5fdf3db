package com.rs.module.acp.controller.admin.gj.vo.undercover;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-耳目反映情况列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UndercoverReportListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("耳目ID")
    private String undercoverId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("反映时间")
    private Date[] reportTime;

    @ApiModelProperty("反映情况")
    private String reportInfo;

    @ApiModelProperty("备注")
    private String remark;

}
