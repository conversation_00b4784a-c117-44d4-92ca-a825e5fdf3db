package com.rs.module.acp.controller.admin.gj.vo.bookingapprove;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-预约审核配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BookingApprovalConfigSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("预约类别")
    @NotEmpty(message = "预约类别不能为空")
    private String bookingCategory;

    @ApiModelProperty("服务类别")
    @NotEmpty(message = "服务类别不能为空")
    private String serviceCategory;

    @ApiModelProperty("是否启用")
    @NotNull(message = "是否启用不能为空")
    private Short isEnabled;

}
