package com.rs.module.acp.controller.admin.pm.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-辅警用户新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserFJSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("地市Id")
    private String cityId;

    @ApiModelProperty("区域Id")
    private String regionId;

    @ApiModelProperty("区域名称")
    private String regionName;

    @ApiModelProperty("区域代码")
    private String regionCode;

    @ApiModelProperty("机构Id")
    private String orgId;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("机构code")
    private String orgCode;
    @ApiModelProperty("登录名")
    @NotEmpty(message = "登录名不能为空")
    private String loginId;

    @ApiModelProperty("登录密码")
    private String password;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别 1:男,2:女")
    private String sex;

    @ApiModelProperty("身份证号码")
    private String idCard;

    @ApiModelProperty("邮箱地址")
    private String email;

    @ApiModelProperty("移动电话")
    private String mobile;

    @ApiModelProperty("办公电话")
    private String officeTel;

    @ApiModelProperty("IP地址")
    private String ip;

    @ApiModelProperty("拼音码")
    private String scode;

    /*@ApiModelProperty("上次访问IP")
    private String lastIp;

    @ApiModelProperty("上次访问时间")
    @NotNull(message = "上次访问时间不能为空")
    private Date lastVisit;*/

    @ApiModelProperty("是否禁用(0否1是)")
    private String isDisabled;

    @ApiModelProperty("排序Id")
    private Integer orderId;

    @ApiModelProperty("登录方式")
    private String loginMethod;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("警衔 字典:ZD_POSITION")
    private String policeRank;

    /*@ApiModelProperty("是否辅警(0否1是)")
    private String isFj;

    @ApiModelProperty("辅警类型")
    private String fjLx;*/

    @ApiModelProperty("备注")
    private String remark;

    /*@ApiModelProperty("是否同步数据(0否1是)")
    private String isSync;

    @ApiModelProperty("用户身份证号确认状态(0:否1:是)")
    private String confirm;*/

    @ApiModelProperty("帐号有效期：1:永久，2：临时")
    private String validType;

    @ApiModelProperty("帐号到期日期")
    private Date endTime;

    @ApiModelProperty("临时密码")
    private String tempPass;

    @ApiModelProperty("用户组 字典:ZD_USER_GROUP")
    private String userGroup;

    @ApiModelProperty("密码过期时间")
    private Date passExpireDate;

    /*@ApiModelProperty("密码国密加密")
    private String gmPass;

    @ApiModelProperty("身份证国密加密")
    private String gmIdCard;

    @ApiModelProperty("移动电话国密加密")
    private String gmMobile;

    @ApiModelProperty("密码国密加密签名")
    private String gmPassSign;

    @ApiModelProperty("身份证国密加密签名")
    private String gmIdCardSign;

    @ApiModelProperty("移动电话国密加密签名")
    private String gmMobileSign;*/

    @ApiModelProperty("岗位 字典:ZD_POST")
    private String post;

    @ApiModelProperty("政治面貌 字典：ZD_C_ZZMM")
    private String zzmm;

    @ApiModelProperty("学历 字典：ZD_GABBZ_XL")
    private String education;

    @ApiModelProperty("人员类型 字典:ZD_GZRYXXRYLX")
    private String rylx;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("所属中队")
    private String sszd;

    @ApiModelProperty("参加监管工作时间")
    private Date cjjggzsj;

    @ApiModelProperty("加入公安时间")
    private Date jrgasj;

    @ApiModelProperty("学位 字典 :ZD_BASE_DEGREE")
    private String xw;

    @ApiModelProperty("婚姻状况 字典 :ZD_GABBZ_HYZK")
    private String hyzk;

    @ApiModelProperty("联系地址")
    private String lxdz;

    @ApiModelProperty("宗教信仰 字典 :ZD_GABBZ_ZJXY")
    private String zjxy;

    @ApiModelProperty("办公地址")
    private String bgdz;

    @ApiModelProperty("民族 字典 :ZD_GABBZ_MZ")
    private String mz;

    @ApiModelProperty("户籍地详址")
    private String hjdxz;

}
