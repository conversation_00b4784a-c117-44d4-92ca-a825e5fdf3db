package com.rs.module.acp.controller.admin.sys.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-语音播报-待播报任务分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VbTaskPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("播报监所Id")
    private String vbPrisonId;

    @ApiModelProperty("播报监区Id")
    private String vbAreaId;

    @ApiModelProperty("播报监室Id")
    private String vbRoomId;

    @ApiModelProperty("播报设备序列号")
    private String vbSerialNumber;
    
    @ApiModelProperty("播报类型(0:定时,1:实时)")
    private Short vbType;

    @ApiModelProperty("播报名称")
    private String vbName;

    @ApiModelProperty("播报内容")
    private String content;

    @ApiModelProperty("播报次数")
    private Short vbNum;

    @ApiModelProperty("优先级")
    private Short priority;

    @ApiModelProperty("播报开始时间")
    private Date[] startTime;

    @ApiModelProperty("播报结束时间")
    private Date[] endTime;

    @ApiModelProperty("状态(0:待播报,1:已播报,2:超时自动取消)")
    private Short status;

    @ApiModelProperty("播报时间")
    private Date[] vbTime;

    @ApiModelProperty("取消时间")
    private Date[] cancelTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
