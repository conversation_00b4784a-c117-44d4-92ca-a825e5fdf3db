package com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-诊断评估(戒毒所)新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DiagnosisAssmtJdsCommitReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("手动时必填，监管人员编码")
    private String jgrybm;

    @ApiModelProperty("手动时必填，监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("手动时必填，监室id")
    private String roomId;

    @ApiModelProperty("手动时必填，监室名称")
    private String roomName;

    @ApiModelProperty("手动时必填，待评估类型")
    private String assmtType;

    @ApiModelProperty("手动时必填，评估周期")
    private String assmtPeriod;

    @ApiModelProperty("评估内容")
    private List<AssmtCommonBO> pgnr;

    @ApiModelProperty("评估结果")
    private List<AssmtCommonBO> pgjg;

}
