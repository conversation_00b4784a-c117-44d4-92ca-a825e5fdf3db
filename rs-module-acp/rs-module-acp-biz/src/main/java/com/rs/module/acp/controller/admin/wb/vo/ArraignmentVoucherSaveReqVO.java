package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-提讯登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ArraignmentVoucherSaveReqVO extends BaseVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("提讯凭证类型")
    private String evidenceType;

    @ApiModelProperty("提讯凭证类型名称")
    private String evidenceTypeName;

    @ApiModelProperty("所选的凭证证号")
    private String evidenceNumber;

    @ApiModelProperty("上传提讯凭证URL")
    private String evidenceUrl;
}
