package com.rs.module.acp.controller.admin.wb.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-服务大厅信息发布分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HallInfoPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("信息标题")
    private String title;

    @ApiModelProperty("信息内容")
    private String infoContent;

    @ApiModelProperty("附件文件类型")
    private String attachmentType;

    @ApiModelProperty("附件上传地址")
    private String attachmentUrl;

    @ApiModelProperty("状态")
    private String status;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
