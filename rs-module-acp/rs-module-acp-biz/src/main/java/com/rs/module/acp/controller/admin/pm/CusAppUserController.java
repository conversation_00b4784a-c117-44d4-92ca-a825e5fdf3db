package com.rs.module.acp.controller.admin.pm;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.common.util.R;
import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import io.swagger.annotations.ApiImplicitParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import java.util.stream.Collectors;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.CusAppUserDO;
import com.rs.module.acp.service.pm.CusAppUserService;

@Api(tags = "实战平台-监管管理-我的应用配置")
@RestController
@RequestMapping("/acp/pm/cusAppUser")
@Validated
public class CusAppUserController {

    @Resource
    private CusAppUserService cusAppUserService;

    @RequestMapping(value = "/save", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiOperation("设置我的应用，根据应用ID批量添加我的应用（ids，用逗号隔开）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "ids（用逗号隔开）,应用id", required = true, paramType = "query", dataType = "String")
    })
    public CommonResult<Boolean> createCusAppUser(@RequestParam("ids") String ids) {
        cusAppUserService.createCusAppUser(ids);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-我的应用配置")
    public CommonResult<Boolean> updateCusAppUser(@Valid @RequestBody CusAppUserSaveReqVO updateReqVO) {
        cusAppUserService.updateCusAppUser(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-我的应用配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCusAppUser(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            cusAppUserService.deleteCusAppUser(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-我的应用配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CusAppUserRespVO> getCusAppUser(@RequestParam("id") String id) {
        CusAppUserDO cusAppUser = cusAppUserService.getCusAppUser(id);
        return success(BeanUtils.toBean(cusAppUser, CusAppUserRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-我的应用配置分页")
    public CommonResult<PageResult<CusAppUserRespVO>> getCusAppUserPage(@Valid @RequestBody CusAppUserPageReqVO pageReqVO) {
        PageResult<CusAppUserDO> pageResult = cusAppUserService.getCusAppUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CusAppUserRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-我的应用配置列表")
    public CommonResult<List<CusAppUserRespVO>> getCusAppUserList(@Valid @RequestBody CusAppUserListReqVO listReqVO) {
        List<CusAppUserDO> list = cusAppUserService.getCusAppUserList(listReqVO);
        return success(BeanUtils.toBean(list, CusAppUserRespVO.class));
    }


    @GetMapping("/getWdyyList")
    @ApiOperation(value = "获取我的应用列表")
    public R getWdyyList(@RequestParam(value = "yylx",required = false) String yylx) {
        String idCard = SessionUserUtil.getSessionUser().getIdCard();
        List<CusAppUserDO> wdyyList = cusAppUserService.list(Wrappers.lambdaQuery(CusAppUserDO.class).eq(CusAppUserDO::getUserIdCard, idCard));
        List<String> yyidList = wdyyList.stream().map(CusAppUserDO::getSystemId).collect(Collectors.toList());

        List<Map<String, Object>> allApply = cusAppUserService.getWdyyList(yyidList, yylx);
        for (Map<String, Object> map : allApply) {
            //String flmc = (String) map.get("flmc");
            String preUrl = (String) map.get("pre_url");
            String ljdz = (String) map.get("ljdz");
            boolean sfnb = StringUtil.getBoolean((String)map.get("sfnb"));

            //处理链接地址
            if(StringUtil.isNotEmpty(preUrl) && StringUtil.isNotEmpty(ljdz) && !ljdz.toLowerCase().startsWith("http") && !sfnb) {
                map.put("ljdz", preUrl + ljdz);
            }
        }
        return R.success().putData(allApply);
    }


}
