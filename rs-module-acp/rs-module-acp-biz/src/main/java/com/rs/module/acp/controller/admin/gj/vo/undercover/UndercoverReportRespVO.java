package com.rs.module.acp.controller.admin.gj.vo.undercover;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-信息员管理-反映情况 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UndercoverReportRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("耳目ID")
    private String undercoverId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("反映时间")
    private Date reportTime;
    @ApiModelProperty("反映情况")
    private String reportInfo;
    @ApiModelProperty("备注")
    private String remark;
}
