package com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-协助破案新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssistanceSolveCaseClueRegistReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("线索类型")
    private String clueType;

    @ApiModelProperty("线索获取日期")
    private Date clueGetTime;

    @ApiModelProperty("是否重大案件")
    private String clueSfzdaj;

    @ApiModelProperty("检举案件性质")
    private String clueJjajxz;

    @ApiModelProperty("涉案区域")
    private String clueSaqy;

    @ApiModelProperty("线索来源")
    private String clueXsly;

    @ApiModelProperty("涉案人")
    private String clueSar;

    @ApiModelProperty("涉案人数")
    private Integer clueSars;

    @ApiModelProperty("线索内容")
    private String clueContext;

    @ApiModelProperty("线索附件")
    private String clueAttachmentUrl;

    @ApiModelProperty("线索登记人")
    private String clueRegistUser;

    @ApiModelProperty("线索登记人身份证号")
    private String clueRegistUserSfzh;

    @ApiModelProperty("线索新增时间")
    private Date clueRegistTime;

}
