package com.rs.module.acp.controller.admin.home;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.bsp.common.cons.CommonConstants;
import com.bsp.common.util.R;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.cons.MsgType;
import com.bsp.sdk.mongodb.MongodbClient;
import com.bsp.security.util.SessionUserUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoCursor;
import com.rs.module.acp.controller.admin.home.vo.MessageComponentVO;
import com.rs.util.DicUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 消息管理
 *
 * <AUTHOR>
 * @Date 2025/3/22 10:46
 */
@Api(tags = "实战平台-消息管理")
@RestController
@RequestMapping("/acp/msg")
@Validated
public class MessageController {

    @Value("${bsp.mongodb.databaseName}")
    private String databaseName;

    @Resource
    private MongodbClient mongodbClient;


    @ApiOperation(value = "分页查询待办消息", notes = "分页查询待办消息", produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "title", value = "消息标题", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgType", value = "消息类型", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "title", value = "主题", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "busType", value = "业务类型", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "sStartTime", value = "开始时间（yyyy-MM-dd或者yyyy-MM-dd HH:mm:ss）", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "sEndTime", value = "结束时间（yyyy-MM-dd或者yyyy-MM-dd HH:mm:ss）", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pStartTime", value = "开始时间（yyyy-MM-dd或者yyyy-MM-dd HH:mm:ss）", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pEndTime", value = "结束时间（yyyy-MM-dd或者yyyy-MM-dd HH:mm:ss）", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "isPrompting", value = "是否提醒, 1提醒, 0不提醒", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "isRead", value = "是否已读，0未读，1已读", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "isProc", value = "是否已处理，0未处理，1已处理", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "fOrgName", value = "机构名称", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "systemMark", value = "来源单位", required = false, paramType = "query", dataType = "String")
    })
    @RequestMapping(value = "/getDbMsgData", method = {RequestMethod.GET, RequestMethod.POST})
    public R getDbMsgData(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                          @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                          @RequestParam(value = "msgType", required = false) String msgType,
                          @RequestParam(value = "ywbh", required = false) String ywbh,
                          @RequestParam(value = "title", required = false) String title,
                          @RequestParam(value = "busType", required = false) String busType,
                          @RequestParam(value = "sStartTime", required = false) String sStartTime,
                          @RequestParam(value = "sEndTime", required = false) String sEndTime,
                          @RequestParam(value = "pStartTime", required = false) String pStartTime,
                          @RequestParam(value = "pEndTime", required = false) String pEndTime,
                          @RequestParam(value = "isPrompting", required = false, defaultValue = "1") String isPrompting,
                          @RequestParam(value = "isRead", required = false) String isRead,
                          @RequestParam(value = "isProc", required = false) String isProc,
                          @RequestParam(value = "systemMark", required = false) String systemMark,
                          @RequestParam(value = "fOrgName", required = false) String fOrgName) {

        try {
            if (StringUtil.isEmpty(msgType)) {
                isRead = "0";
                isProc = "0";
            }
            Dict result = Dict.create();
            if (StringUtil.isEmpty(msgType)) {
                System.out.println("search all msgType");
                Dict todo = handleSearch(pageNo, pageSize, ywbh, title, busType, sStartTime, sEndTime, pStartTime, pEndTime, isPrompting, isRead, isProc, fOrgName, systemMark, "01");
                result.set("todo", todo);

                Dict alert = handleSearch(pageNo, pageSize, ywbh, title, busType, sStartTime, sEndTime, pStartTime, pEndTime, isPrompting, isRead, isProc, fOrgName, systemMark, "02");
                result.set("alert", alert);
                Dict alarm = handleSearch(pageNo, pageSize, ywbh, title, busType, sStartTime, sEndTime, pStartTime, pEndTime, isPrompting, isRead, isProc, fOrgName, systemMark, "03");
                result.set("alarm", alarm);
            } else {
                Dict dict = handleSearch(pageNo, pageSize, ywbh, title, busType, sStartTime, sEndTime, pStartTime, pEndTime, isPrompting, isRead, isProc, fOrgName, systemMark, msgType);
                result.set("data", dict);
            }
            return R.success("查询消息成功").putData(result);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("按照应用标识查询消息失败，" + e.getMessage()).page(0, new JSONArray());
        }
    }

    /**
     * 前端消息组件查询消息数据接口
     */
    @ApiOperation(value = "前端消息组件查询消息数据接口", notes = "前端消息组件查询消息数据接口", produces = "application/json")
    @RequestMapping(value = "/getDbMsgDataComponent", method = {RequestMethod.GET, RequestMethod.POST})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "msgTypes", value = "消息类型, 01：待办消息 02：提醒消息 03：预警消息.多个类型用逗号分隔", required = true, paramType = "query", dataType = "String")
    })
    public R getDbMsgDataComponent(@RequestParam(value = "msgTypes") String msgTypes) {
        Map<String, Object> result = new HashMap<>();
        Date now = new Date();
        String[] split = msgTypes.split(",");
        for (String msgType : split) {
            List<MessageComponentVO> list = new ArrayList<>();
            // 今日待办
            MessageComponentVO today = getMessageComponentVO("today",
                    msgType, "0", getMsgFlagField(msgType), DateUtil.beginOfDay(now), DateUtil.endOfDay(now));
            list.add(today);
            // 本周待办
            MessageComponentVO week = getMessageComponentVO("week",
                    msgType, "0", getMsgFlagField(msgType), DateUtil.beginOfWeek(now), DateUtil.endOfWeek(now));
            list.add(week);
            // 本月待办
            MessageComponentVO month = getMessageComponentVO("month",
                    msgType, "0", getMsgFlagField(msgType), DateUtil.beginOfMonth(now), DateUtil.endOfMonth(now));
            list.add(month);
            // 本周已办
            MessageComponentVO weekDone = getMessageComponentVO("weekDone", msgType, "1", getMsgFlagField(msgType),
                    DateUtil.beginOfWeek(now), DateUtil.endOfWeek(now));
            list.add(weekDone);
            // 本月已办
            MessageComponentVO monthDone = getMessageComponentVO("monthDone",
                    msgType, "1", getMsgFlagField(msgType), DateUtil.beginOfMonth(now), DateUtil.endOfMonth(now));
            list.add(monthDone);
            result.put(msgType, list);
        }
        return R.success("查询消息成功").putData(result);

    }

    /**
     * 获取消息组件数据
     *
     * @param typeCode
     * @param msgType     消息类型 01：待办消息 02：提醒消息 03：预警消息
     * @param flag        判断标识 0：未读 1：已读
     * @param isFlagField 标识字段名称 sTime：发送时间 pTime：处理时间
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @return
     */
    public MessageComponentVO getMessageComponentVO(String typeCode, String msgType, String flag,
                                                    String isFlagField, Date startDate, Date endDate) {
        List<MessageComponentVO> result = new ArrayList<>();
        MessageComponentVO componentVO = new MessageComponentVO();
        componentVO.setTypeCode(typeCode);
        BasicDBObject filter = new BasicDBObject().append("isdel", CommonConstants.DATA_IS_DEL_FALSE);
        filter.put("rUser", SessionUserUtil.getSessionUser().getIdCard());
        filter.put("fXxpt", "pc");
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        if (StringUtil.isNotEmpty(orgCode)) {
            filter.put("rOrgCode", orgCode);
        }
        filter.put("isPrompting", "1");
        filter.put(isFlagField, flag);
        BasicDBObject todayFilter = new BasicDBObject();
        todayFilter.put("$gte", DateUtil.formatDateTime(startDate));
        todayFilter.put("$lte", DateUtil.formatDateTime(endDate));
        filter.put("sTime", todayFilter);
        String collectionName = getCollectionName(msgType);
        MongoCursor<Document> mongoCursor = mongodbClient.get(filter, databaseName, collectionName, "sTime", false);
        // 今日待办消息
        Map<String, MessageComponentVO.BusTypeItem> busTypeItemMap = new HashMap<>();
        int total = 0;
        while (mongoCursor.hasNext()) {
            Document next = mongoCursor.next();
            String busType = next.getString("busType");
            String typPrefix = "";
            if (StrUtil.isNotBlank(busType)) {
                typPrefix = busType.split("_")[0];
            } else {
                typPrefix = "199";
            }
            String prefixName = DicUtils.translate("ZD_MSG_BUSTYPE_PREFIX", typPrefix);
            if ("null".equals(prefixName)) {
                typPrefix = "199";
                prefixName = DicUtils.translate("ZD_MSG_BUSTYPE_PREFIX", typPrefix);
            }
            MessageComponentVO.BusTypeItem busTypeItem = busTypeItemMap.get(typPrefix);
            if (busTypeItem == null) {
                busTypeItem = new MessageComponentVO.BusTypeItem();
                busTypeItem.setMsgList(new ArrayList<>());
                busTypeItem.setBusType(prefixName);
                busTypeItem.setBusTypeCode(typPrefix);
                busTypeItemMap.put(typPrefix, busTypeItem);
            }
            // 字典翻译
            String systemMarkName = DicUtils.translate("ZD_APP", next.getString("systemMark"));
            next.put("systemMarkName", systemMarkName);
            String busTypeName = DicUtils.translate("ZD_MSG_BUSTYPE", busType);
            next.put("busTypeName", busTypeName);
            next.put("typPrefix", busTypeItem.getBusType());
            next.put("typPrefixName", busTypeItem.getBusTypeCode());
            busTypeItem.addMsg(next);
            total += 1;
        }
        filter.remove(isFlagField);
        Long totalNum = mongodbClient.getTotal(filter, databaseName, collectionName);
        if (flag.equals("0")) {
            componentVO.setTotalNotDone(String.valueOf(total));
            componentVO.setTotalDone(String.valueOf(totalNum-total));
        } else {
            componentVO.setTotalDone(String.valueOf(total));
            componentVO.setTotalNotDone(String.valueOf(totalNum-total));
        }
        componentVO.setTotal(String.valueOf(totalNum));
        componentVO.setItems(busTypeItemMap.values());
        return componentVO;


    }

    private Dict handleSearch(int pageNo, int pageSize, String ywbh, String title, String busType, String sStartTime, String sEndTime,
                              String pStartTime, String pEndTime, String isPrompting, String isRead, String isProc, String fOrgName, String systemMark, String msgType) {
        if (!StringUtil.isEmpty(msgType)) {
            if (MsgType.TODO.getCode().equals(msgType)) {
                isRead = "-1";
            }
//            }else {
//                isProc = "-1";
//            }
        }
        String collectionName = getCollectionName(msgType);
        BasicDBObject filter = new BasicDBObject().append("isdel", CommonConstants.DATA_IS_DEL_FALSE);
        filter.put("rUser", SessionUserUtil.getSessionUser().getIdCard());
        String orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        if (StringUtil.isNotEmpty(orgCode)) {
            filter.put("rOrgCode", orgCode);
        }
        if (StringUtil.isNotEmpty(fOrgName)) {
            // 模糊匹配正则规则
            Pattern fOrgNamePattern = Pattern.compile(fOrgName, Pattern.CASE_INSENSITIVE);
            filter.put("fOrgName", fOrgNamePattern);
        }
        if (StringUtil.isNotEmpty(systemMark)) {
            if (systemMark.contains(",")) {
                String[] idList = systemMark.split(",");
                filter.put("systemMark", new BasicDBObject("$in", idList));
            } else {
                // 模糊匹配正则规则
                Pattern systemMarkPattern = Pattern.compile(systemMark, Pattern.CASE_INSENSITIVE);
                filter.put("systemMark", systemMarkPattern);
            }

        }
        // -1为查全部
        if (!"-1".equals(isPrompting)) {
            filter.put("isPrompting", isPrompting);
        }
        if (!"-1".equals(isProc)) {
            filter.put("isProc", isProc);
        }
        if (!"-1".equals(isRead)) {
            filter.put("isRead", isRead);
        }
        if (StringUtil.isNotEmpty(ywbh)) {
            filter.put("ywbh", ywbh);
        }
        if (StringUtil.isNotEmpty(title)) {
            // 模糊匹配正则规则
            Pattern pattern = Pattern.compile(title, Pattern.CASE_INSENSITIVE);
            filter.put("title", pattern);
        }

        if (StringUtil.isNotEmpty(busType)) {
            filter.put("busType", busType);
        }
        BasicDBObject sTimeObj = new BasicDBObject();
        if (StringUtil.isNotEmpty(sStartTime)) {
            String sStart = sStartTime;
            if (isValidDate(sStartTime)) {
                sStart = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(sStartTime)));
            }
            sTimeObj.put("$gte", sStart);
            filter.put("sTime", sTimeObj);
        }
        if (StringUtil.isNotEmpty(sEndTime)) {
            String sEnd = sEndTime;
            if (isValidDate(sEndTime)) {
                sEnd = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(sEndTime)));
            }
            sTimeObj.put("$lte", sEnd);
            filter.put("sTime", sTimeObj);
        }
        BasicDBObject pTimeObj = new BasicDBObject();
        if (StringUtil.isNotEmpty(pStartTime)) {
            String pStart = pStartTime;
            if (isValidDate(pStartTime)) {
                pStart = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.parse(pStartTime)));
            }
            pTimeObj.put("$gte", pStart);
            filter.put("pTime", pTimeObj);
        }
        if (StringUtil.isNotEmpty(pEndTime)) {
            String pEnd = pEndTime;
            if (isValidDate(pEndTime)) {
                pEnd = DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.parse(pEndTime)));
            }
            pTimeObj.put("$lte", pEnd);
            filter.put("pTime", pTimeObj);
        }

        MongoCursor<Document> mongoCursor = mongodbClient.getPageBy(filter, databaseName, collectionName, pageSize, (pageNo - 1) * pageSize, "sTime", false);
        List<Document> documentList = new ArrayList<>();
        while (mongoCursor.hasNext()) {
            Document next = mongoCursor.next();
            documentList.add(next);
        }
        Long total = mongodbClient.getTotal(filter, databaseName, getCollectionName(msgType));
        Set<String> ywbhSet = documentList.stream().map(document -> document.getString("ywbh")).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(ywbhSet)) {
            // query db

        } else {
            documentList.forEach(document -> {
                document.put("ajmc", "");
            });
        }
        String[] dicMsgName = {"ZD_MSG_BUSTYPE", "ZD_APP"};
        String[] dicAppName = {"busType", "systemMark"};
        List<Map<String, Object>> list = DicUtils.translate(documentList, dicMsgName, dicAppName);
        return Dict.create().set("list", list).set("total", total);
    }

    public static boolean isValidDate(String time) {
        if (StringUtil.isEmpty(time)) {
            return false;
        }
        if (!NumberUtil.isNumber(time) && time.length() == "yyyy-MM-dd".length()) {
            return true;
        }
        return false;
    }

    public static String getCollectionName(String msgType) {
        String collectionName = "msg_other";
        if (MsgType.TODO.getCode().equals(msgType)) {
            collectionName = "msg_todo";
        } else if (MsgType.ALERT.getCode().equals(msgType)) {
            collectionName = "msg_alert";
        } else if (MsgType.ALARM.getCode().equals(msgType)) {
            collectionName = "msg_alarm";
        }
        return collectionName;
    }

    public static String getMsgFlagField(String msgType) {
        String collectionName = "";
        if (MsgType.TODO.getCode().equals(msgType)) {
            collectionName = "isProc";
        } else if (MsgType.ALERT.getCode().equals(msgType)) {
            collectionName = "isRead";
        } else if (MsgType.ALARM.getCode().equals(msgType)) {
            collectionName = "isProc";
        }
        return collectionName;
    }

}
