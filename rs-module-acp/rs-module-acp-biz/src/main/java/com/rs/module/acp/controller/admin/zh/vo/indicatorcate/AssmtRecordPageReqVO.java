package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssmtRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("被考核人身份证号")
    private String assessedSfzh;

    @ApiModelProperty("被考核人姓名")
    private String assessedName;

    @ApiModelProperty("所属岗位")
    private String postName;

    @ApiModelProperty("考核月份")
    private String assmtMonth;

    @ApiModelProperty("指标类型ID")
    private String indicatorCateId;

    @ApiModelProperty("指标类型名称")
    private String indicatorCateName;

    @ApiModelProperty("截止日期")
    private Date[] expiryDate;

    @ApiModelProperty("加减分得分")
    private BigDecimal addsubtractScore;

    @ApiModelProperty("主观得分")
    private BigDecimal subjectiveScore;

    @ApiModelProperty("总得分")
    private BigDecimal totalScore;

    @ApiModelProperty("状态")
    private String status;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
