package com.rs.module.acp.controller.admin.gj.vo.punishment;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-处罚呈批新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    //@ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）1 实战平台 2 仓外屏 3 仓内屏")
    //@NotEmpty(message = "数据来源（字典：ZD_DATA_SOURCES）不能为空")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("处罚原因 看守所字典：ZD_GJCFYY   拘留所字典：ZD_GJYGYY")
    private String reason;

    @ApiModelProperty("呈批处罚周期开始日期")
    @NotNull(message = "呈批处罚周期开始日期不能为空")
    private Date startDate;

    @ApiModelProperty("呈批处罚周期结束日期")
    @NotNull(message = "呈批处罚周期结束日期不能为空")
    private Date endDate;

    //@ApiModelProperty("实际开始日期")
    //@NotNull(message = "实际开始日期不能为空")
    //private Date actualStartDate;
    //
    //@ApiModelProperty("实际结束日期")
    //@NotNull(message = "实际结束日期不能为空")
    //private Date actualEndDate;

    @ApiModelProperty("处罚时长，自动生成（天-时-分钟）")
    @NotNull(message = "处罚时长，自动生成（天-时-分钟）不能为空")
    private Integer duration;

    //@ApiModelProperty("actual_duration")
    //@NotNull(message = "actual_duration不能为空")
    //private BigDecimal actualDuration;

    @ApiModelProperty("处罚措施逗号分割  字典：ZD_GJCFNR")
    @NotEmpty(message = "处罚措施逗号分割不能为空")
    private String measures;

    @ApiModelProperty("具体原因")
    private String specificReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否严管  1 是 0 否")
    @NotNull(message = "必传  是否严管")
    private Integer sfyg;

    //@ApiModelProperty("执行人身份证号")
    //private String executorSfzh;
    //
    //@ApiModelProperty("执行人身份证号")
    //private String executor;
    //
    //@ApiModelProperty("执行登记时间")
    //private Date executorRegTime;

    //@ApiModelProperty("执行情况")
    //private String executeSituation;
    //
    //@ApiModelProperty("是否提前解除")
    //private Short isInAdvanceRemove;
    //
    //@ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    //private String status;
    //
    //@ApiModelProperty("审批人身份证号")
    //private String approverSfzh;
    //
    //@ApiModelProperty("审批人姓名")
    //private String approverXm;
    //
    //@ApiModelProperty("审批时间")
    //private Date approverTime;
    //
    //@ApiModelProperty("审批结果")
    //private String approvalResult;
    //
    //@ApiModelProperty("审批人签名")
    //private String approvalAutograph;
    //
    //@ApiModelProperty("审批人签名日期")
    //private Date approvalAutographTime;
    //
    //@ApiModelProperty("审核意见")
    //private String approvalComments;
    //
    //@ApiModelProperty("ACT流程实例Id")
    //private String actInstId;
    //
    //@ApiModelProperty("任务ID")
    //private String taskId;

}
