package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-考核审核记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmttApprovalRecordRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("加减分审核ID")
    private String assmttApprovalId;
    @ApiModelProperty("指标类型ID")
    private String indicatorCateId;
    @ApiModelProperty("指标类型名称")
    private String indicatorCateName;
    @ApiModelProperty("所属主指标ID")
    private String mainIndicatorId;
    @ApiModelProperty("所属子指标ID")
    private String subIndicatorId;
    @ApiModelProperty("指标名称")
    private String indicatorName;
    @ApiModelProperty("指标描述")
    private String indicatorDescription;
    @ApiModelProperty("分值类型，字典：FIXED：固定分，RANGE：范围分")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_FZLX")
    private String scoreType;
    @ApiModelProperty("加减分，字典：ADD：加分、SUBTRACT：扣分")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_JJF")
    private String addSubtract;
    @ApiModelProperty("基础分值")
    private BigDecimal baseScore;
    @ApiModelProperty("最小分值")
    private BigDecimal minScore;
    @ApiModelProperty("最大分值（仅范围分有效）")
    private BigDecimal maxScore;
    @ApiModelProperty("排序序号")
    private Integer sortOrder;
    @ApiModelProperty("绩效原因")
    private String assmtReason;
    @ApiModelProperty("绩效分数")
    private BigDecimal assmtScore;
    @ApiModelProperty("综合审核原因")
    private String zhApprovalReason;
    @ApiModelProperty("综合审核分数")
    private BigDecimal zhApprovalScore;
    @ApiModelProperty("最终审核分数")
    private BigDecimal zzApprovalScore;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
