package com.rs.module.acp.controller.admin.gj.vo.identificationsuit;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-识别服管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IdentificationSuitSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("识别服号")
    @NotEmpty(message = "识别服号不能为空")
    private String suitNum;

    @ApiModelProperty("识别服标识 字典：ZD_SBFBS")
    @NotEmpty(message = "识别服标识不能为空")
    private String suitColor;

}
