package com.rs.module.acp.controller.admin.wb.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-服务大厅信息发布配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HallInfoConfigPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("是否轮播")
    private Short isCarousel;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
