package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-物品顾送明细列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsDeliveryDetailsListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("物品顾送ID")
    private String goodsDeliveryId;

    @ApiModelProperty("物品名称")
    private String goodsName;

    @ApiModelProperty("物品数量")
    private Integer goodsQuantity;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("物品备注")
    private String remark;

}
