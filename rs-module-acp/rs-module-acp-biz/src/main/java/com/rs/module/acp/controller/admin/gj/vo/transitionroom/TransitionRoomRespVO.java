package com.rs.module.acp.controller.admin.gj.vo.transitionroom;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡监室管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransitionRoomRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("过渡开始日期")
    private Date startDate;

    @ApiModelProperty("过渡结束日期")
    private Date endDate;

    @ApiModelProperty("实际开始日期")
    private Date actualStartDate;

    @ApiModelProperty("实际结束日期")
    private Date actualEndDate;

    @ApiModelProperty("过渡监室id")
    private String roomId;

    @ApiModelProperty("过渡监室名称")
    private String roomName;

    @ApiModelProperty("是否延长 1是0否")
    private Integer isExtend;

    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_TRANSITION_ROOM_STATUS")
    private String status;

    @ApiModelProperty("调整监室号ID")
    private String adjustRoomId;

    @ApiModelProperty("调整监室号名称")
    private String adjustRoomName;

    //@ApiModelProperty("过渡考核信息")
    //private List<TransitionRoomEvalRespVO> roomEvalList;

    @ApiModelProperty("过渡考核信息登记信息")
    private TransitionRoomEvalRespVO roomEval;

    @ApiModelProperty("延长过渡呈批信息")
    private List<TransitionRoomExtendRespVO> roomExtendList;


}
