package com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-协助破案新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssistanceSolveCaseForwardReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;


    @ApiModelProperty("转递部门类型")
    private String forwardType;

    @ApiModelProperty("转递部门名称")
    private String forwardName;

    @ApiModelProperty("转递日期")
    private Date forwardTime;

    @ApiModelProperty("转递材料附件")
    private String forwardUrl;

    @ApiModelProperty("转递人")
    private String forwardUser;

    @ApiModelProperty("转递人身份证号")
    private String forwardUserSfzh;

    @ApiModelProperty("转递新增时间")
    private Date forwardUserTime;

}
