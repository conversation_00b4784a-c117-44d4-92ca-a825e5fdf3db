package com.rs.module.acp.controller.admin.zh;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorConfigSaveReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.zh.IndicatorConfigDO;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorConfigService;

@Api(tags = "综合管理-绩效考核截止日期设置")
@RestController
@RequestMapping("/acp/zh/indicatorConfig")
@Validated
public class IndicatorConfigController {

    @Resource
    private IndicatorConfigService indicatorConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核截止日期设置")
    public CommonResult<String> createIndicatorConfig(@Valid @RequestBody IndicatorConfigSaveReqVO createReqVO) {
        return success(indicatorConfigService.createIndicatorConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核截止日期设置")
    public CommonResult<Boolean> updateIndicatorConfig(@Valid @RequestBody IndicatorConfigSaveReqVO updateReqVO) {
        indicatorConfigService.updateIndicatorConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核截止日期设置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteIndicatorConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           indicatorConfigService.deleteIndicatorConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核截止日期设置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<IndicatorConfigRespVO> getIndicatorConfig(@RequestParam("id") String id) {
        IndicatorConfigDO indicatorConfig = indicatorConfigService.getIndicatorConfig(id);
        return success(BeanUtils.toBean(indicatorConfig, IndicatorConfigRespVO.class));
    }

    @GetMapping("/getByOrgCode")
    @ApiOperation(value = "通过机构编码获得综合管理-绩效考核截止日期设置")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<IndicatorConfigRespVO> getIndicatorConfigByOrgCode(@RequestParam(value = "orgCode", required = false) String orgCode) {
        if(StringUtils.isEmpty(orgCode)){
            orgCode = SessionUserUtil.getSessionUser().getOrgCode();
        }
        IndicatorConfigDO indicatorConfig = indicatorConfigService.getIndicatorConfigByOrgCode(orgCode);
        return success(BeanUtils.toBean(indicatorConfig, IndicatorConfigRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核截止日期设置分页")
    public CommonResult<PageResult<IndicatorConfigRespVO>> getIndicatorConfigPage(@Valid @RequestBody IndicatorConfigPageReqVO pageReqVO) {
        PageResult<IndicatorConfigDO> pageResult = indicatorConfigService.getIndicatorConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndicatorConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核截止日期设置列表")
    public CommonResult<List<IndicatorConfigRespVO>> getIndicatorConfigList(@Valid @RequestBody IndicatorConfigListReqVO listReqVO) {
        List<IndicatorConfigDO> list = indicatorConfigService.getIndicatorConfigList(listReqVO);
        return success(BeanUtils.toBean(list, IndicatorConfigRespVO.class));
    }
}
