package com.rs.module.acp.controller.admin.wb;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.DicUtil;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.entity.wb.FamilyMeetingDO;
import com.rs.module.acp.entity.wb.LawyerMeetingDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.service.wb.FamilyMeetingService;

@Api(tags = "实战平台-窗口业务-家属会见登记")
@RestController
@RequestMapping("/acp/wb/familyMeeting")
@Validated
public class FamilyMeetingController {

    @Resource
    private FamilyMeetingService familyMeetingService;

    @Autowired
    private WbCommonService wbCommonService;

    @GetMapping("/verificationPersonnel")
    @ApiOperation(value = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:verificationPersonnel", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中",
            success = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中成功",
            fail = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<JSONObject> verificationPersonnel(@RequestParam("jgrybm") String jgrybm) {
        return success(wbCommonService.verificationPersonnel(jgrybm,WbConstants.BUSINESS_TYPE_FAMILY_MEETING));
    }


    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建家属会见登记")
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建家属会见登记",
            success = "实战平台-窗口业务-创建家属会见登记成功", fail = "实战平台-窗口业务-创建家属会见登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createFamilyMeeting(@Valid @RequestBody FamilyMeetingSaveReqVO createReqVO, HttpServletRequest request) {

        return success(familyMeetingService.createFamilyMeeting(createReqVO));
    }


    @GetMapping("/signIn")
    @ApiOperation(value = "实战平台-窗口业务-家属会见登记签到")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "checkInTime", value = "签到时间", required = true, dataType = "String"),
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:signIn", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-家属会见登记签到",
            success = "实战平台-窗口业务-家属会见登记签到成功", fail = "实战平台-窗口业务-家属会见登记签到失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> signIn(@RequestParam(value = "id", required = true) String id,
                                        @RequestParam(value = "checkInTime", required = true) String checkInTime) {
        if (familyMeetingService.signIn(id, checkInTime)) {
            return success();
        }
        return error("签到失败");
    }

    @GetMapping("/allocationRoom")
    @ApiOperation(value = "实战平台-窗口业务-分配会见室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "roomId", value = "审讯室ID", required = true, dataType = "String"),
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:allocationRoom", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-分配审讯室",
            success = "实战平台-窗口业务-分配审讯室成功", fail = "实战平台-窗口业务-分配审讯室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> allocationRoom(@RequestParam(value = "id", required = true) String id,
                                                @RequestParam(value = "roomId", required = true) String roomId) {
        if (familyMeetingService.allocationRoom(id, roomId)) {
            return success();
        }
        return error("分配审讯室失败");
    }

    @PostMapping(path = "/escortingInspect")
    @ApiOperation(value = "实战平台-窗口业务-带出安检")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "escortingPolice", value = "带出民警姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPoliceSfzh", value = "带出民警身份证号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingTime", value = "带出监室时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectionResult", value = "检查结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "prohibitedItems", value = "违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "physicalExam", value = "体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "abnormalSituations", value = "异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "inspectionTime", value = "检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectorSfzh", value = "检查人证件号码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspector", value = "检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingStartTime", value = "会见开始时间", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:escortingInspect", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-带出安检",
            success = "实战平台-窗口业务-带出安检成功", fail = "实战平台-窗口业务-带出安检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> escortingInspect(@RequestBody FamilyMeetingSaveReqVO updateReqVO) {
        if (familyMeetingService.escortingInspect(updateReqVO)) {
            return success();
        }
        return error("保存带出安检失败");
    }

    @PostMapping("/returnInspection")
    @ApiOperation(value = "实战平台-窗口业务-会毕安检")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "meetingtEndTime", value = "会见结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectorSfzh", value = "会毕检查人身份证", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspector", value = "会毕检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionTime", value = "会毕检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionResult", value = "会毕检查结果", dataType = "String"),
            @ApiImplicitParam(name = "returnProhibitedItems", value = "带回违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "returnPhysicalExam", value = "带回体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "returnAbnormalSituations", value = "带回异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "returnTime", value = "带回监室时间", dataType = "String"),
            @ApiImplicitParam(name = "returnPolice", value = "带回民警姓名", dataType = "String"),
            @ApiImplicitParam(name = "returnPoliceSfzh", value = "带回民警身份证号", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:returnInspection", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-会毕安检",
            success = "实战平台-窗口业务-会毕安检成功", fail = "实战平台-窗口业务-会毕安检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_JSHJ,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"经办时间\":\"{{#updateReqVO.addTime}}\"," +
            "\"会见类型\":\"{{#updateReqVO.meetingMethod}}\",\"会见开始时间\":\"{{#fDateTime(#updateReqVO.meetingStartTime)}}\"," +
            "\"会见结束时间\":\"{{#fDateTime(#updateReqVO.meetingEndTime)}}\"}")
    public CommonResult<Boolean> returnInspection(@RequestBody FamilyMeetingSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        FamilyMeetingDO familyMeetingDO = familyMeetingService.getById(updateReqVO.getId());
        updateReqVO.setAddTime(DateUtil.format(familyMeetingDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        updateReqVO.setMeetingMethod("当面会见");
        updateReqVO.setJgrybm(familyMeetingDO.getJgrybm());
        updateReqVO.setMeetingStartTime(familyMeetingDO.getMeetingStartTime());
        /****记录轨迹 end****/
        if (familyMeetingService.returnInspection(updateReqVO)) {
            return success();
        }
        return error("保存会毕安检失败");
    }

    @PostMapping("/additionalRecording")
    @ApiOperation(value = "实战平台-窗口业务-补录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "checkInTime", value = "签到时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "room_id", value = "审讯室ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPolice", value = "带出民警姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPoliceSfzh", value = "带出民警身份证号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingTime", value = "带出监室时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectionResult", value = "检查结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "prohibitedItems", value = "违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "physicalExam", value = "体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "abnormalSituations", value = "异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "inspectionTime", value = "检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectorSfzh", value = "检查人证件号码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspector", value = "检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingStartTime", value = "会见开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingEndTime", value = "会见结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectorSfzh", value = "会毕检查人身份证", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspector", value = "会毕检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionTime", value = "会毕检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionResult", value = "会毕检查结果", dataType = "String"),
            @ApiImplicitParam(name = "returnProhibitedItems", value = "带回违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "returnPhysicalExam", value = "带回体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "returnAbnormalSituations", value = "带回异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "returnTime", value = "带回监室时间", dataType = "String"),
            @ApiImplicitParam(name = "returnPolice", value = "带回民警姓名", dataType = "String"),
            @ApiImplicitParam(name = "returnPoliceSfzh", value = "带回民警身份证号", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:additionalRecording", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-补录",
            success = "实战平台-窗口业务-补录成功", fail = "实战平台-窗口业务-补录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_JSHJ,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"经办时间\":\"{{#updateReqVO.addTime}}\"," +
            "\"会见类型\":\"{{#updateReqVO.meetingMethod}}\",\"会见开始时间\":\"{{#fDateTime(#updateReqVO.meetingStartTime)}}\"," +
            "\"会见结束时间\":\"{{#fDateTime(#updateReqVO.meetingEndTime)}}\"}")
    public CommonResult<Boolean> additionalRecording(@RequestBody FamilyMeetingSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        FamilyMeetingDO familyMeetingDO = familyMeetingService.getById(updateReqVO.getId());
        updateReqVO.setAddTime(DateUtil.format(familyMeetingDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        updateReqVO.setMeetingMethod("当面会见");
        updateReqVO.setJgrybm(familyMeetingDO.getJgrybm());
        /****记录轨迹 end****/
        if (familyMeetingService.additionalRecording(updateReqVO)) {
            return success();
        }
        return error("保存补录失败");
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得家属会见登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得家属会见登记",
            success = "实战平台-窗口业务-获得家属会见登记成功", fail = "实战平台-窗口业务-获得家属会见登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<FamilyMeetingRespVO> getFamilyMeeting(@RequestParam("id") String id) {
        return success(familyMeetingService.getFamilyMeetingById(id));
    }

    @GetMapping("/getHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:getHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录",
            success = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录成功", fail = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<FamilyMeetingRespVO>> getHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                                   @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                                   @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(familyMeetingService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize));
    }


    @GetMapping("/getOnSiteNumbering")
    @ApiOperation(value = "实战平台-窗口业务-家属现场会见排号信息")
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:getOnSiteNumbering", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-家属现场会见排号信息", success = "实战平台-窗口业务-家属现场会见排号信息成功",
            fail = "实战平台-窗口业务-家属现场会见排号信息失败，错误信息：{{#_ret[msg]}}")
    public CommonResult<List<JSONObject>> getOnSiteNumbering() {
        return success(familyMeetingService.getOnSiteNumbering());
    }

    @GetMapping("/getTrajectory")
    @ApiOperation(value = "实战平台-窗口业务-获得家属当面会见轨迹")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:getTrajectory", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得家属当面会见轨迹",
            success = "实战平台-窗口业务-获得家属当面会见轨迹成功", fail = "实战平台-窗口业务-获得家属当面会见轨迹失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<List<JSONObject>> getTrajectory(@RequestParam("id") String id) {
        return success(wbCommonService.getTrajectory(id,WbConstants.BUSINESS_TYPE_FAMILY_MEETING));
    }

    @GetMapping("/getNewHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:getNewHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录",
            success = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录成功",
            fail = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<JSONObject>> getNewHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                             @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(wbCommonService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize, WbConstants.BUSINESS_TYPE_FAMILY_MEETING));
    }

    @GetMapping("/getIdleMeetingRoom")
    @ApiOperation(value = "实战平台-窗口业务-获取空闲家属会见室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeeting:getIdleMeetingRoom", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-获取空闲家属会见室",
            success = "实战平台-窗口业务-获取空闲家属会见室成功", fail = "实战平台-窗口业务-获取空闲家属会见室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<List<JSONObject>> getIdleMeetingRoom(@RequestParam(value = "id", required = true) String id) {
        return success(wbCommonService.getIdleFamilyRoom(true));
    }
}
