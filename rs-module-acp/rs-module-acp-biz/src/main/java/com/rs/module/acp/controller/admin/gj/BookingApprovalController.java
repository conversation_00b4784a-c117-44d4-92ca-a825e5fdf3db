package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.adapter.bsp.enums.BspApproceStatusEnum;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalRespVO;
import com.rs.module.acp.controller.admin.gj.vo.bookingapprove.BookingApprovalSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.entity.gj.BookingApprovalDO;
import com.rs.module.acp.service.gj.bookingapproval.BookingApprovalService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-预约审核管理")
@RestController
@RequestMapping("/acp/gj/bookingApproval")
@Validated
public class BookingApprovalController {

    @Resource
    private BookingApprovalService bookingApprovalService;

    @Resource
    private PrisonerService prisonerService;

    @PostMapping("/create")
    @ApiOperation(value = "管教业务-创建预约审核信息")
    @LogRecordAnnotation(bizModule = "acp:bookingApproval:create", operateType = LogOperateType.CREATE, title = "管教业务-预约审核-创建",
            success = "管教业务-预约审核-创建成功", fail = "管教业务-预约审核-创建，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createBookingApproval(@Valid @RequestBody BookingApprovalSaveReqVO createReqVO) {
        return success(bookingApprovalService.createBookingApproval(createReqVO));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "管教业务-预约审核-管教审批")
    @LogRecordAnnotation(bizModule = "acp:bookingApproval:approve", operateType = LogOperateType.CREATE, title = "管教业务-预约审核-审批",
            success = "管教业务-预约审批-创建成功", fail = "管教业务-预约审核-审批，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> appr(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        Boolean flag = bookingApprovalService.approve(approveReqVO);
        return success(flag);
    }

    @GetMapping("/get")
    @ApiOperation(value = "管教业务-预约审核管理-详情获取")
    @LogRecordAnnotation(bizModule = "acp:bookingApproval:get", operateType = LogOperateType.QUERY, title = "管教业务-预约审核-获取详情",
            bizNo = "{{#id}}", success = "获取管教业务-预约审核估详情成功", fail = "获取管教业务-预约审核详情失败", extraInfo = "{{#id}}")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BookingApprovalRespVO> getBookingApproval(@RequestParam("id") String id) {
        BookingApprovalDO bookingApproval = bookingApprovalService.getBookingApproval(id);
        if(bookingApproval != null){
            BookingApprovalRespVO vo =  BeanUtils.toBean(bookingApproval, BookingApprovalRespVO.class);
            if(vo != null){
                PrisonerVwRespVO respVO = prisonerService.getPrisonerByJgrybm(vo.getJgrybm());
                vo.setRssj(respVO.getRssj());
                if(vo.getApprovalResult() != null && BspApproceStatusEnum.PASSED_END.getCode() == Short.parseShort( vo.getApprovalResult())){
                    vo.setApprovalResultName("同意");
                } else if(vo.getApprovalResult() != null) {
                    vo.setApprovalResultName("不同意");
                }
            }
            return success(vo);
        }
        return success(null);
    }

    @PostMapping("/page")
    @ApiOperation(value = "管教业务-预约审核管理分页")
    @LogRecordAnnotation(bizModule = "acp:bookingApproval:page", operateType = LogOperateType.QUERY, title = "管教业务-预约审核-预约审核管理分页",
            success = "管教业务-预约审核管理分页-查询成功", fail = "管教业务-预约审核管理分页-审批，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<BookingApprovalRespVO>> getBookingApprovalPage(@Valid @RequestBody BookingApprovalPageReqVO pageReqVO) {
        PageResult<BookingApprovalDO> pageResult = bookingApprovalService.getBookingApprovalPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BookingApprovalRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "管教业务-预约审核管理列表")
    @LogRecordAnnotation(bizModule = "acp:bookingApproval:list", operateType = LogOperateType.QUERY, title = "管教业务-预约审核-预约审核管理分页",
            success = "管教业务-预约审核管理分页-查询成功", fail = "管教业务-预约审核管理分页-审批，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<BookingApprovalRespVO>> getBookingApprovalList(@Valid @RequestBody BookingApprovalListReqVO listReqVO) {
        List<BookingApprovalDO> list = bookingApprovalService.getBookingApprovalList(listReqVO);
        return success(BeanUtils.toBean(list, BookingApprovalRespVO.class));
    }
}
