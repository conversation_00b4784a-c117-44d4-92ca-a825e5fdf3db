package com.rs.module.acp.controller.admin.wb.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-服务大厅信息发布配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HallInfoConfigListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("是否轮播")
    private Short isCarousel;

}
