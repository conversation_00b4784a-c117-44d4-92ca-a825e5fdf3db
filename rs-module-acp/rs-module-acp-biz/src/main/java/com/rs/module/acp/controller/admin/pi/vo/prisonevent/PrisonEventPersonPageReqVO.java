package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import com.rs.framework.common.pojo.PageParam;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@ApiModel(description = "管理后台 - 实战平台-巡视管控-人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventPersonPageReqVO extends PageParam {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("人员名称")
    private String name;

    @ApiModelProperty("人员类型(0：办案人员，1：律师，2：家属：3：领事，4：其他访客)")
    private String personType;

    @ApiModelProperty("机构编码--无需传值")
    private String orgCode;
}
