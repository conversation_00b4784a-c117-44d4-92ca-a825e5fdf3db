package com.rs.module.acp.controller.admin.gj.vo.prisonroom;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务--监室调整分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonRoomChangePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("旧监室id")
    private String oldRoomId;

    @ApiModelProperty("新监室ID")
    private String newRoomId;

    @ApiModelProperty("旧监室名称")
    private String oldRoomName;

    @ApiModelProperty("新监室名称")
    private String newRoomName;

    @ApiModelProperty("调整原因")
    private String changeReason;

    @ApiModelProperty("监室调整时间")
    private Date[] roomChangeTime;

    @ApiModelProperty("是否调整（1：已调整，0：未调整）")
    private Short isChange;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("领导签名")
    private String approvalAutograph;

    @ApiModelProperty("领导签名日期")
    private Date[] approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date[] escortingTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("带回监室时间")
    private Date[] returnTime;

    @ApiModelProperty("带回民警身份证号")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
