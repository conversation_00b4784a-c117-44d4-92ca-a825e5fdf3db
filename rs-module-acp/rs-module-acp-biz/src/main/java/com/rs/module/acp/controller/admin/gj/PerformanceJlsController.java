package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.performancejls.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.PerformanceJlsDO;
import com.rs.module.acp.service.gj.performance.PerformanceJlsService;

@Api(tags = "实战平台-管教业务-人员表现鉴定表-拘留所")
@RestController
@RequestMapping("/acp/gj/performanceJls")
@Validated
public class PerformanceJlsController {

    @Resource
    private PerformanceJlsService performanceJlsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-人员表现鉴定表-拘留所")
    public CommonResult<String> createPerformanceJls(@Valid @RequestBody PerformanceJlsSaveReqVO createReqVO) {
        return success(performanceJlsService.createPerformanceJls(createReqVO));
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody PerformanceJssApprovalReqVO approvalReqVO) {
        performanceJlsService.approval(approvalReqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-人员表现鉴定表-拘留所")
    public CommonResult<Boolean> updatePerformanceJls(@Valid @RequestBody PerformanceJlsSaveReqVO updateReqVO) {
        performanceJlsService.updatePerformanceJls(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-人员表现鉴定表-拘留所")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePerformanceJls(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            performanceJlsService.deletePerformanceJls(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-人员表现鉴定表-拘留所")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PerformanceJlsRespVO> getPerformanceJls(@RequestParam("id") String id) {
        PerformanceJlsDO performanceJls = performanceJlsService.getPerformanceJls(id);
        return success(BeanUtils.toBean(performanceJls, PerformanceJlsRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-人员表现鉴定表-拘留所分页")
    public CommonResult<PageResult<PerformanceJlsRespVO>> getPerformanceJlsPage(@Valid @RequestBody PerformanceJlsPageReqVO pageReqVO) {
        PageResult<PerformanceJlsDO> pageResult = performanceJlsService.getPerformanceJlsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PerformanceJlsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-人员表现鉴定表-拘留所列表")
    public CommonResult<List<PerformanceJlsRespVO>> getPerformanceJlsList(@Valid @RequestBody PerformanceJlsListReqVO listReqVO) {
        List<PerformanceJlsDO> list = performanceJlsService.getPerformanceJlsList(listReqVO);
        return success(BeanUtils.toBean(list, PerformanceJlsRespVO.class));
    }
}
