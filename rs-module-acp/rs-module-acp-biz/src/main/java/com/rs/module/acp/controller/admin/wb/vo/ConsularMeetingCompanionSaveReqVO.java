package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-领事外事会见同行登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsularMeetingCompanionSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("领事会见ID")
    @NotEmpty(message = "领事会见ID不能为空")
    private String consularMeetingId;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String name;

    @ApiModelProperty("性别")
    @NotEmpty(message = "性别不能为空")
    private String gender;

    @ApiModelProperty("证件号码")
    @NotEmpty(message = "证件号码不能为空")
    private String idCard;

    @ApiModelProperty("同行人类别")
    @NotEmpty(message = "同行人类别不能为空")
    private String companionType;

    @ApiModelProperty("附件URL")
    private String attachmentUrl;

}
