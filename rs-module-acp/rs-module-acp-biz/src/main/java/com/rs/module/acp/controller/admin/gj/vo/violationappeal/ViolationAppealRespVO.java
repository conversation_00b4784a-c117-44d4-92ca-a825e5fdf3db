package com.rs.module.acp.controller.admin.gj.vo.violationappeal;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-违规申诉 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ViolationAppealRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("违规登记id")
    private String violationRecordId;
    @ApiModelProperty("申诉理由")
    private String appealReason;
    @ApiModelProperty("申诉要求")
    private String appealRequirement;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WGSSSPZT")
    private String status;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("违规类型")
    private String violationContent;

    @ApiModelProperty("违规情况")
    private String pushContent;

    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    private String roomId;
    @ApiModelProperty("监室号")
    private String roomName;

    @ApiModelProperty("申诉状态 02：未申诉，03：已申诉")
    private String sszt;

    @ApiModelProperty("违规时间")
    private Date violationTime;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;


}
