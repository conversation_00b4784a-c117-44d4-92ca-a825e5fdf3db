package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-服务大厅信息发布 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HallInfoRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("信息标题")
    private String title;
    @ApiModelProperty("信息内容")
    private String infoContent;
    @ApiModelProperty("附件文件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_WJLX")
    private String attachmentType;
    @ApiModelProperty("附件上传地址")
    private String attachmentUrl;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("排序")
    private Short sort;
    @ApiModelProperty("附件名称")
    private String attachmentName;

}
