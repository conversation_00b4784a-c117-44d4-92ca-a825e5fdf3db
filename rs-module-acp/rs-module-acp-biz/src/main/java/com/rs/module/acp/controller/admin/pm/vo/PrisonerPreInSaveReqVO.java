package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 嫌疑人（待入所）信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonerPreInSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("监所编号")
    private String jsbh;

    @ApiModelProperty("网办人员编号 (法制系统唯一编号)")
    private String wbrybh;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("姓名拼音")
    private String xmpy;

    @ApiModelProperty("入所日期")
    private Date rsrq;

    @ApiModelProperty("职务 字典值：ZW")
    private String zw;

    @ApiModelProperty("户籍地详址")
    private String hjdxz;

    @ApiModelProperty("现住地详址")
    private String xzdxz;

    @ApiModelProperty("工作单位")
    private String gzdw;

    @ApiModelProperty("状态（STATE） 字典值：STATE")
    private String state;

    @ApiModelProperty("别名")
    private String bm;

    @ApiModelProperty("同案编号")
    private String tabh;

    @ApiModelProperty("审批人")
    private String spr;

    @ApiModelProperty("审批单位")
    private String spdw;

    @ApiModelProperty("特殊身份 字典值：TSSF")
    private String tssf;

    @ApiModelProperty("sdnjccjg")
    private String sdnjccjg;

    @ApiModelProperty("专长 字典值：ZC")
    private String zc;

    @ApiModelProperty("身高")
    private String sg;

    @ApiModelProperty("足长")
    private String zuc;

    @ApiModelProperty("证件号")
    private String zjh;

    @ApiModelProperty("性别 字典值：XB")
    private String xb;

    @ApiModelProperty("民族 字典值：MZ")
    private String mz;

    @ApiModelProperty("国籍 字典值：GJ")
    private String gj;

    @ApiModelProperty("文化程度 字典值：WHCD")
    private String whcd;

    @ApiModelProperty("政治面貌 字典值：ZZMM")
    private String zzmm;

    @ApiModelProperty("户籍地 字典值：XZQH")
    private String hjd;

    @ApiModelProperty("现住地 字典值：XZQH")
    private String xzd;

    @ApiModelProperty("职业 字典值：ZY")
    private String zy;

    @ApiModelProperty("籍贯 字典值：XZQH")
    private String jg;

    @ApiModelProperty("xzhjaj")
    private String xzhjaj;

    @ApiModelProperty("人员管理类别 字典值：RYGLLB")
    private String gllb;

    @ApiModelProperty("身份 字典值：SF")
    private String sf;

    @ApiModelProperty("证件类型 字典值：ZJLX")
    private String zjlx;

    @ApiModelProperty("办案单位")
    private String badw;

    @ApiModelProperty("送押单位")
    private String sydw;

    @ApiModelProperty("送押人")
    private String syr;

    @ApiModelProperty("创建时间")
    private Date createtime;

    @ApiModelProperty("更新时间")
    private Date updatetime;

    @ApiModelProperty("简要案情")
    private String jyaq;

    @ApiModelProperty("案由 字典值：看守所：AJLB，拘留所：JLSAJLB")
    private String ay;

    @ApiModelProperty("婚姻状况 字典值：HYZK")
    private String hyzk;

    @ApiModelProperty("czzt")
    private String czzt;

    @ApiModelProperty("出生日期")
    private Date csrq;

    @ApiModelProperty("jljdjg")
    private String jljdjg;

    @ApiModelProperty("gyts")
    private String gyts;

    @ApiModelProperty("拘留日期")
    private Date jlrq;

    @ApiModelProperty("关押期限")
    private Date gyqx;

    @ApiModelProperty("办案人电话")
    private String bardh;

    @ApiModelProperty("syrsj")
    private String syrsj;

    @ApiModelProperty("收押凭证（SYPZ） 字典值：SYPZ")
    private String sypz;

    @ApiModelProperty("收押凭证文书号")
    private String sypzwsh;

    @ApiModelProperty("逮捕日期")
    private Date dbrq;

    @ApiModelProperty("办案环节 字典值：BAJD")
    private String bahj;

    @ApiModelProperty("联系电话")
    private String lxdh;

    @ApiModelProperty("监所类型")
    private String jslx;

}
