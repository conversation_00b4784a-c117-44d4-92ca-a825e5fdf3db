package com.rs.module.acp.controller.admin.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportRespVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverReportSaveReqVO;
import com.rs.module.acp.entity.gj.UndercoverReportDO;
import com.rs.module.acp.service.gj.undercover.UndercoverReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-信息员反映情况")
@RestController
@RequestMapping("/acp/gj/undercoverReport")
@Validated
public class UndercoverReportController {

    @Resource
    private UndercoverReportService undercoverReportService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-信息员反映情况")
    public CommonResult<String> createUndercoverReport(@Valid @RequestBody UndercoverReportSaveReqVO createReqVO) {
        return success(undercoverReportService.createUndercoverReport(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-信息员反映情况")
    public CommonResult<Boolean> updateUndercoverReport(@Valid @RequestBody UndercoverReportSaveReqVO updateReqVO) {
        undercoverReportService.updateUndercoverReport(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-信息员反映情况")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteUndercoverReport(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           undercoverReportService.deleteUndercoverReport(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-信息员反映情况")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<UndercoverReportRespVO> getUndercoverReport(@RequestParam("id") String id) {
        UndercoverReportDO undercoverReport = undercoverReportService.getUndercoverReport(id);
        return success(BeanUtils.toBean(undercoverReport, UndercoverReportRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-信息员反映情况分页")
    public CommonResult<PageResult<UndercoverReportRespVO>> getUndercoverReportPage(@Valid @RequestBody UndercoverReportPageReqVO pageReqVO) {
        PageResult<UndercoverReportDO> pageResult = undercoverReportService.getUndercoverReportPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UndercoverReportRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-信息员反映情况列表")
    public CommonResult<List<UndercoverReportRespVO>> getUndercoverReportList(@Valid @RequestBody UndercoverReportListReqVO listReqVO) {
        List<UndercoverReportDO> list = undercoverReportService.getUndercoverReportList(listReqVO);
        return success(BeanUtils.toBean(list, UndercoverReportRespVO.class));
    }
}
