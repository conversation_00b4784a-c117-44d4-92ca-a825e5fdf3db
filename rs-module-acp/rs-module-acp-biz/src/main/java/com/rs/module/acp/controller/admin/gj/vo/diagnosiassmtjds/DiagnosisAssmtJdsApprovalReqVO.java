package com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-诊断评估(戒毒所)新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DiagnosisAssmtJdsApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键 不能为空")
    private String id;

    @ApiModelProperty("审批结果  0：不通过  1：通过")
    @NotEmpty(message = "审批结果 不能为空")
    private String approvelResult;

    @ApiModelProperty("审批意见")
    @NotEmpty(message = "审批意见 不能为空")
    private String approvelComment;

}
