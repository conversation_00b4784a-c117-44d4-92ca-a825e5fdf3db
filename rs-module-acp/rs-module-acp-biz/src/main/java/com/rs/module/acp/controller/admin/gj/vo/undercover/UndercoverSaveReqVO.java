package com.rs.module.acp.controller.admin.gj.vo.undercover;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-耳目管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UndercoverSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("布建理由及任务")
    @NotEmpty(message = "布建理由及任务不能为空")
    private String arrangeReason;

    @ApiModelProperty("状态（字典：ZD_GJEMGL）")
    //@NotEmpty(message = "状态（字典：ZD_XXBB_BBZT）不能为空")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("备注")
    private String remark;

}
