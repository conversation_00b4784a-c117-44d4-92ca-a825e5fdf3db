package com.rs.module.acp.controller.admin.gj.vo.groupinout;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-集体出入 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GroupInOutRespVO extends BaseVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("业务类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JTCR_YWLX")
    private String businessType;
    @ApiModelProperty("是否按监室(0:否，1:是)")
    private String isRoom;
    @ApiModelProperty("经办民警")
    private String updateUserName;
    @ApiModelProperty("办理时间")
    private Date updateTime;
    @ApiModelProperty("对象ID")
    private String objectId;
    @ApiModelProperty("对象名称")
    private String objectName;
    @ApiModelProperty("对象数量")
    private Integer objectCount;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("详情")
    private List<GroupInOutDetail> details;

}
