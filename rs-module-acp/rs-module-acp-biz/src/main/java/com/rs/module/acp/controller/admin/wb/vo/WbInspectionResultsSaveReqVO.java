package com.rs.module.acp.controller.admin.wb.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-检查结果 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WbInspectionResultsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键--对应出入登记的business_id")
    private String id;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date escortingTime;

    @ApiModelProperty("带出操作人身份证号")
    private String escortingOperatorSfzh;

    @ApiModelProperty("带出操作人")
    private String escortingOperator;

    @ApiModelProperty("带出操作时间")
    private Date escortingOperatorTime;

    @ApiModelProperty("检查结果（字典：ZD_WB_JCJG）")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;

    @ApiModelProperty("检查时间")
    private Date inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("提讯开始时间")
    private Date arraignmentStartTime;

    @ApiModelProperty("提讯结束时间")
    private Date arraignmentEndTime;

    @ApiModelProperty("会毕检查人")
    private String returnInspectorSfzh;

    @ApiModelProperty("会毕检查人")
    private String returnInspector;

    @ApiModelProperty("会毕检查时间")
    private Date returnInspectionTime;

    @ApiModelProperty("会毕检查结果（字典：ZD_WB_JCJG）")
    private String returnInspectionResult;

    @ApiModelProperty("带回违禁物品登记")
    private String returnProhibitedItems;

    @ApiModelProperty("带回体表检查登记")
    private String returnPhysicalExam;

    @ApiModelProperty("带回异常情况登记")
    private String returnAbnormalSituations;

    @ApiModelProperty("带回监室时间")
    private Date returnTime;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

    @ApiModelProperty("带回民警身份证号")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回操作人身份证号")
    private String returnOperatorSfzh;

    @ApiModelProperty("带回操作人")
    private String returnOperator;

    @ApiModelProperty("带回操作时间")
    private Date returnOperatorTime;

    @ApiModelProperty("预约会见开始时间--用于创建出监登记")
    private Date applMeetingTime;

    @ApiModelProperty("业务类型子类型")
    private String businessSubType;
}
