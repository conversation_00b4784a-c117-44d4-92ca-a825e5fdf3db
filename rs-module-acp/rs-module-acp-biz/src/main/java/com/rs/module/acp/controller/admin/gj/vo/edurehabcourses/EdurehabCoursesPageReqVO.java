package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EdurehabCoursesPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("courses_code")
    private String coursesCode;

    @ApiModelProperty("courses_name")
    private String coursesName;

    @ApiModelProperty("分类颜色")
    private String coursesColor;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
