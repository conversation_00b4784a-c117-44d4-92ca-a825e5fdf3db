package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-加减分考核审核新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmttApprovalSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("考核记录ID")
    @NotEmpty(message = "考核记录ID不能为空")
    private String assmtRecordId;

    @ApiModelProperty("被考核人身份证号")
    //@NotEmpty(message = "被考核人身份证号不能为空")
    private String assessedSfzh;

    @ApiModelProperty("被考核人姓名")
    //@NotEmpty(message = "被考核人姓名不能为空")
    private String assessedName;

    @ApiModelProperty("考核月份")
    //@NotEmpty(message = "考核月份不能为空")
    private String assmtMonth;

    @ApiModelProperty("指标类型，01：主观分指标，02：加减分指标")
    @NotEmpty(message = "指标类型不能为空")
    private String indicatorType;

    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;

    @ApiModelProperty("考核人姓名")
    private String assessorName;

    @ApiModelProperty("考核时间")
    private Date assessorTime;

    @ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("绩效打分记录")
    @NotEmpty(message = "绩效打分记录 不能为空")
    private List<AssmttApprovalRecordSaveReqVO> approvalRecordList;

}
