package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.time.LocalTime;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程时段 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabTimeSlotRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("课程时段编码，如“9:00-10:00”，唯一标识时段")
    private String timeSlotCode;
    @ApiModelProperty("课程时段-开始")
    private String startTime;
    @ApiModelProperty("课程时段-结束")
    private String endTime;
    @ApiModelProperty("时段时长（分钟）")
    private Integer durationMinutes;
}
