package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程计划新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesPlanSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("课程计划名称")
    private String planName;

    @ApiModelProperty("课程时段-开始")
    @NotNull(message = "课程时段-开始不能为空")
    private Date startDate;

    @ApiModelProperty("课程时段-结束")
    @NotNull(message = "课程时段-结束不能为空")
    private Date endDate;

    @ApiModelProperty("课程记录")
    @NotNull(message = "课程记录不能为空")
    private List<EdurehabCoursesRecordSaveReqVO> listRecord;

    @ApiModelProperty("戒区信息--用于构造课程为空的信息")
    @NotNull(message = "戒区信息不能为空")
    private List<JqAreaVO> jqAreaVOList;

    @ApiModelProperty("时间段信息--用于构造课程为空的信息")
    @NotNull(message = "时间段信息不能为空")
    private List<EdurehabTimeSlotSaveReqVO> timeList;

}
