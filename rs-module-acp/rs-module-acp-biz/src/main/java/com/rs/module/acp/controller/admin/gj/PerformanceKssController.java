package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.performancekss.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.PerformanceKssDO;
import com.rs.module.acp.service.gj.performance.PerformanceKssService;

@Api(tags = "实战平台-管教业务-人员表现鉴定表-看守所")
@RestController
@RequestMapping("/acp/gj/performanceKss")
@Validated
public class PerformanceKssController {

    @Resource
    private PerformanceKssService performanceKssService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-人员表现鉴定表-看守所")
    public CommonResult<String> createPerformanceKss(@Valid @RequestBody PerformanceKssSaveReqVO createReqVO) {
        return success(performanceKssService.createPerformanceKss(createReqVO));
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody PerformanceKssApprovalReqVO approvalReqVO) {
        performanceKssService.approval(approvalReqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-人员表现鉴定表-看守所")
    public CommonResult<Boolean> updatePerformanceKss(@Valid @RequestBody PerformanceKssSaveReqVO updateReqVO) {
        performanceKssService.updatePerformanceKss(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-人员表现鉴定表-看守所")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePerformanceKss(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           performanceKssService.deletePerformanceKss(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-人员表现鉴定表-看守所")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PerformanceKssRespVO> getPerformanceKss(@RequestParam("id") String id) {
        PerformanceKssDO performanceKss = performanceKssService.getPerformanceKss(id);
        return success(BeanUtils.toBean(performanceKss, PerformanceKssRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-人员表现鉴定表-看守所分页")
    public CommonResult<PageResult<PerformanceKssRespVO>> getPerformanceKssPage(@Valid @RequestBody PerformanceKssPageReqVO pageReqVO) {
        PageResult<PerformanceKssDO> pageResult = performanceKssService.getPerformanceKssPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PerformanceKssRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-人员表现鉴定表-看守所列表")
    public CommonResult<List<PerformanceKssRespVO>> getPerformanceKssList(@Valid @RequestBody PerformanceKssListReqVO listReqVO) {
        List<PerformanceKssDO> list = performanceKssService.getPerformanceKssList(listReqVO);
        return success(BeanUtils.toBean(list, PerformanceKssRespVO.class));
    }
}
