package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师关联被监管人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerPrisonerRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("律师表ID")
    private String lawyerId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("委托类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_LSWT")
    private String entrustType;
    @ApiModelProperty("委托阶段")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WTJD")
    private String entrustStage;
    @ApiModelProperty("委托人")
    private String principal;
    @ApiModelProperty("委托人证件号")
    private String principalId;
    @ApiModelProperty("委托书类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WTSLX")
    private String powerOfAttorneyType;
    @ApiModelProperty("委托书存储路径")
    private String powerOfAttorneyUrl;
    @ApiModelProperty("介绍信编号")
    private String letterNumber;
    @ApiModelProperty("会见批准机关")
    private String meetingApprovalAuthority;
    @ApiModelProperty("委托状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_LSWTZT")
    private String status;
    @ApiModelProperty("被监管人姓名")
    private String xm;
    @ApiModelProperty("被监管人姓名所在监室")
    private String roomName;
    @ApiModelProperty("被监管人正面照片")
    private String frontPhoto;
    @ApiModelProperty("创建时间")
    private Date addTime;
}
