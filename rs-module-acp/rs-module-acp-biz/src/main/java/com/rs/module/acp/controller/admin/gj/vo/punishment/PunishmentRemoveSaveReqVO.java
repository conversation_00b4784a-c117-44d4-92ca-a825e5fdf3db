package com.rs.module.acp.controller.admin.gj.vo.punishment;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-管教业务解除处罚呈批新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentRemoveSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("处罚呈批ID")
    @NotEmpty(message = "处罚呈批ID不能为空")
    private String id;

    @ApiModelProperty("解除理由")
    @NotEmpty(message = "解除理由不能为空")
    private String removeReason;

    //@ApiModelProperty("是否提前解除")
    //private Short isInAdvanceRemove;

    @ApiModelProperty("具体原因")
    private String specificRemoveReason;

    @ApiModelProperty("备注")
    private String remark;


    //@ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    //private String status;


    //@ApiModelProperty("审批人身份证号")
    //private String approverSfzh;
    //
    //@ApiModelProperty("审批人姓名")
    //private String approverXm;
    //
    //@ApiModelProperty("审批时间")
    //private Date approverTime;
    //
    //@ApiModelProperty("审批结果")
    //private String approvalResult;
    //
    //@ApiModelProperty("审批人签名")
    //private String approvalAutograph;
    //
    //@ApiModelProperty("审批人签名日期")
    //private Date approvalAutographTime;
    //
    //@ApiModelProperty("审核意见")
    //private String approvalComments;
    //
    //@ApiModelProperty("ACT流程实例Id")
    //private String actInstId;
    //
    //@ApiModelProperty("任务ID")
    //private String taskId;

}
