package com.rs.module.acp.controller.admin.pi.vo.shifthandover;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-巡控交接班登记 人员分布情况查询Response VO")
@Data
public class ShiftHandoverStatisticsNumVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("总人数")
    private Integer totalNumber=0;
    @ApiModelProperty("点名人数")
    private Integer rollCall=0;//todo 待查询
    @ApiModelProperty("出所就医")
    private Integer outForMedicalTreatment=0;//todo 待查询
    @ApiModelProperty("提讯/询")
    private Integer arraignment=0;
    @ApiModelProperty("律师会见")
    private Integer lawyerToMeet=0;
    @ApiModelProperty("家属会见")
    private Integer familyMeeting=0;
    @ApiModelProperty("提解")
    private Integer suggestion=0;
    @ApiModelProperty("请假未归")
    private Integer leaveButNotReturn=0;//todo 待查询
}
