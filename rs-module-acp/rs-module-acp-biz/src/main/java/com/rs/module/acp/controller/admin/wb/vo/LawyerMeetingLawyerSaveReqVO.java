package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerMeetingLawyerSaveReqVO extends BaseVO implements TransPojo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("性别")
    private String xb;
    @ApiModelProperty("证件类型")
    private String zjlx;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("联系方式")
    private String lxfs;
    @ApiModelProperty("律师类型")
    private String lslx;
    @ApiModelProperty("执业证号码")
    private String zyzhm;
    @ApiModelProperty("执业证有效期")
    private Date ksZyzyxq;
    @ApiModelProperty("执业证有效期-结束")
    private Date jsZyzyxq;
    @ApiModelProperty("律师单位")
    private String lsdw;
    @ApiModelProperty("照片存储url")
    private String zpUrl;
    @ApiModelProperty("执业证书url")
    private String zyzsUrl;
    @ApiModelProperty("委托类型")
    private String entrustType;
    @ApiModelProperty("委托阶段")
    private String entrustStage;
    @ApiModelProperty("委托人")
    private String principal;
    @ApiModelProperty("委托人证件号")
    private String principalId;
    @ApiModelProperty("委托书类型")
    private String powerOfAttorneyType;
    @ApiModelProperty("委托书存储路径")
    private String powerOfAttorneyUrl;
    @ApiModelProperty("介绍信编号")
    private String letterNumber;
    @ApiModelProperty("会见批准机关")
    private String meetingApprovalAuthority;
    @ApiModelProperty("委托状态")
    private String status;

    @ApiModelProperty("委托时间")
    private String entrustTime;

}
