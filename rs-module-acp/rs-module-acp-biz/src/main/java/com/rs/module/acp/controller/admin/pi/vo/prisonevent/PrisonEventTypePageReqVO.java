package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件类型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonEventTypePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("事件类型名称")
    private String typeName;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("机构编码-无需传值")
    private String orgCode;

    @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
