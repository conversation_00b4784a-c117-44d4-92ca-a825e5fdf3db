package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-家属会见登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyMeetingSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("办案机关批准会见文书号")
    private String approvalDocumentNumber;

    @ApiModelProperty("会见批准材料附件上传后存储路径")
    private String approvalAttachmentPath;

    @ApiModelProperty("关于此次会见的备注信息")
    private String remarks;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("会见所在房间编号")
    private String roomId;

    @ApiModelProperty("分配房间时间")
    private Date assignmentRoomTime;

    @ApiModelProperty("分配民警身份证号")
    private String assignmentPoliceSfzh;

    @ApiModelProperty("assignment_police")
    private String assignmentPolice;

    @ApiModelProperty("签到时间")
    private Date checkInTime;

    @ApiModelProperty("签到用户身份证号")
    private String checkInPoliceSfzh;

    @ApiModelProperty("签到用户")
    private String checkInPolice;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date escortingTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("检查时间")
    private Date inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("会见开始时间")
    private Date meetingStartTime;

    @ApiModelProperty("会见结束时间")
    private Date meetingEndTime;

    @ApiModelProperty("会毕检查人")
    private String returnInspectorSfzh;

    @ApiModelProperty("会毕检查人")
    private String returnInspector;

    @ApiModelProperty("会毕检查时间")
    private Date returnInspectionTime;

    @ApiModelProperty("会毕检查结果")
    private String returnInspectionResult;

    @ApiModelProperty("带回违禁物品登记")
    private String returnProhibitedItems;

    @ApiModelProperty("带回体表检查登记")
    private String returnPhysicalExam;

    @ApiModelProperty("带回异常情况登记")
    private String returnAbnormalSituations;

    @ApiModelProperty("带回监室时间")
    private Date returnTime;

    @ApiModelProperty("return_police_sfzh")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

    @ApiModelProperty("带出操作人身份证号")
    private String escortingOperatorSfzh;

    @ApiModelProperty("带出操作人")
    private String escortingOperator;

    @ApiModelProperty("带出操作时间")
    private Date escortingOperatorTime;

    @ApiModelProperty("带回操作人身份证号")
    private String returnOperatorSfzh;

    @ApiModelProperty("带回操作人")
    private String returnOperator;

    @ApiModelProperty("带回操作时间")
    private Date returnOperatorTime;

    @ApiModelProperty("预约会见开始时间")
    private Date applyMeetingStartTime;

    @ApiModelProperty("预约会见结束时间")
    private Date applyMeetingEndTime;

    @ApiModelProperty("数据来源（0：PC手动录入，1：智能终端）")
    private String dataSources = "0";

    @ApiModelProperty("添加时间-记录轨迹使用，无需传值")
    private String addTime;

    @ApiModelProperty("家属列表")
    private List<SocialRelationsChildSaveReqVO> familyList;

    @ApiModelProperty("同行人列表")
    private List<FamilyMeetingCompanionSaveReqVO> companionList;

    @ApiModelProperty("会见方式--无需传值")
    private String meetingMethod;
}
