package com.rs.module.acp.controller.admin.wb;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.WbCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LegalAssistanceDO;
import com.rs.module.acp.service.wb.LegalAssistanceService;

@Api(tags = "实战平台-窗口业务-法律帮助申请")
@RestController
@RequestMapping("/acp/wb/legalAssistance")
@Validated
public class LegalAssistanceController {

    @Resource
    private LegalAssistanceService legalAssistanceService;

    @Autowired
    private WbCommonService wbCommonService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建法律帮助申请")
    @LogRecordAnnotation(bizModule = "acp:legalAssistance:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建法律帮助申请",
            success = "实战平台-窗口业务-创建法律帮助申请成功", fail = "实战平台-窗口业务-创建法律帮助申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createLegalAssistance(@Valid @RequestBody LegalAssistanceSaveReqVO createReqVO) {
        return success(legalAssistanceService.createLegalAssistance(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新法律帮助申请")
    @LogRecordAnnotation(bizModule = "acp:legalAssistance:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新法律帮助申请",
            success = "实战平台-窗口业务-更新法律帮助申请成功", fail = "实战平台-窗口业务-更新法律帮助申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> updateLegalAssistance(@Valid @RequestBody LegalAssistanceSaveReqVO updateReqVO) {
        legalAssistanceService.updateLegalAssistance(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-窗口业务-删除法律帮助申请")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:legalAssistance:delete", operateType = LogOperateType.DELETE, title = "实战平台-窗口业务-删除法律帮助申请",
            success = "实战平台-窗口业务-删除法律帮助申请成功", fail = "实战平台-窗口业务-删除法律帮助申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteLegalAssistance(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           legalAssistanceService.deleteLegalAssistance(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得法律帮助申请")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:legalAssistance:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得法律帮助申请",
            success = "实战平台-窗口业务-获得法律帮助申请成功", fail = "实战平台-窗口业务-获得法律帮助申请失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<LegalAssistanceRespVO> getLegalAssistance(@RequestParam("id") String id) {
        return success(legalAssistanceService.getLegalAssistanceById(id));
    }

    @GetMapping("/getMeetingRoomList")
    @ApiOperation(value = "实战平台-窗口业务-获取律师会见室")
    @LogRecordAnnotation(bizModule = "acp:legalAssistance:getMeetingRoomList", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获取律师会见室",
            success = "实战平台-窗口业务-获获取律师会见室成功", fail = "实战平台-窗口业务-获取律师会见室失败，错误信息：{{#_ret[msg]}}")
    public CommonResult<List<JSONObject>> getLegalAssistance() {
        return success(wbCommonService.getMeetingRoomList(WbConstants.BUSINESS_TYPE_LAWYER_MEETING));
    }

}
