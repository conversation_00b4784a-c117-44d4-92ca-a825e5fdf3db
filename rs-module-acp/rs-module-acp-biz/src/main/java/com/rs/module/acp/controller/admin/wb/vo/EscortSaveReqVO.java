package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-提解登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EscortSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("审讯室ID")
    private String roomId;

    @ApiModelProperty("提解原因")
    @NotEmpty(message = "提解原因不能为空")
    private String escortReason;

    @ApiModelProperty("申请提解日期")
    @NotNull(message = "申请提解日期不能为空")
    private Date applyEscortDate;

    @ApiModelProperty("详细事由")
    private String detailReason;

    @ApiModelProperty("提解目的地")
    private String destination;

    @ApiModelProperty("提解文书号提解文书号")
    private String wsh;

    @ApiModelProperty("上传提讯凭证URL")
    private String evidenceUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("提解机关类型")
    private String tjjglx;

    @ApiModelProperty("提解机关名称")
    private String tjjgmc;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date escortingTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;

    @ApiModelProperty("检查时间")
    private Date inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("提讯开始时间")
    private Date arraignmentStartTime;

    @ApiModelProperty("提讯结束时间")
    private Date arraignmentEndTime;

    @ApiModelProperty("带回检查人")
    private String returnInspectorSfzh;

    @ApiModelProperty("带回检查人")
    private String returnInspector;

    @ApiModelProperty("带回检查时间")
    private Date returnInspectionTime;

    @ApiModelProperty("带回检查结果")
    private String returnInspectionResult;

    @ApiModelProperty("带回违禁物品登记")
    private String returnProhibitedItems;

    @ApiModelProperty("带回体表检查登记")
    private String returnPhysicalExam;

    @ApiModelProperty("带回异常情况登记")
    private String returnAbnormalSituations;

    @ApiModelProperty("带回监室时间")
    private Date returnTime;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

    @ApiModelProperty("带回民警身份证号")
    private String returnPoliceSfzh;

    @ApiModelProperty("办案人员")
    private List<CasePersonnelSaveReqVO> casePersonnelList;

    @ApiModelProperty("添加时间-记录轨迹使用，无需传值")
    private String addTime;

    @ApiModelProperty("提解事由-记录轨迹使用，无需传值")
    private String  escortReasonName;

    @ApiModelProperty("带出操作人身份证号")
    private String escortingOperatorSfzh;

    @ApiModelProperty("带出操作人")
    private String escortingOperator;

    @ApiModelProperty("带出操作时间")
    private Date escortingOperatorTime;

    @ApiModelProperty("带回操作人身份证号")
    private String returnOperatorSfzh;

    @ApiModelProperty("带回操作人")
    private String returnOperator;

    @ApiModelProperty("带回操作时间")
    private Date returnOperatorTime;

    @ApiModelProperty("数据来源（0：PC手动录入，1：智能终端）")
    private String dataSources = "0";
}
