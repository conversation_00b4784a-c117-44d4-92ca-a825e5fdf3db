package com.rs.module.acp.controller.admin.gj.vo.undercover;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-耳目反映情况分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UndercoverReportPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("耳目ID")
    private String undercoverId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("反映时间")
    private Date[] reportTime;

    @ApiModelProperty("反映情况")
    private String reportInfo;

    @ApiModelProperty("备注")
    private String remark;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
