package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-单向视频家属会见列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyMeetingVideoListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("第一位家属ID")
    private String familyMember1Id;

    @ApiModelProperty("第一位家属姓名")
    private String familyMember1Name;

    @ApiModelProperty("第一位家属性别")
    private String familyMember1Gender;

    @ApiModelProperty("第一位家属证件类型")
    private String familyMember1IdType;

    @ApiModelProperty("第一位家属证件号码")
    private String familyMember1IdNumber;

    @ApiModelProperty("第一位家属与被会见人社会关系")
    private String familyMember1Relationship;

    @ApiModelProperty("第一位家属联系方式")
    private String familyMember1Contact;

    @ApiModelProperty("第一位家属工作单位")
    private String familyMember1WorkUnit;

    @ApiModelProperty("第一位家属居住地址")
    private String familyMember1Address;

    @ApiModelProperty("第一位家属关系证明附件")
    private String familyMember1RelationsAttch;

    @ApiModelProperty("第一位家属照片")
    private String familyMember1ImageUrl;

    @ApiModelProperty("第二位家属ID")
    private String familyMember2Id;

    @ApiModelProperty("第二位家属姓名")
    private String familyMember2Name;

    @ApiModelProperty("第二位家属性别")
    private String familyMember2Gender;

    @ApiModelProperty("第二位家属证件类型")
    private String familyMember2IdType;

    @ApiModelProperty("第二位家属的证件号码")
    private String familyMember2IdNumber;

    @ApiModelProperty("第二位家属与被会见人社会关系")
    private String familyMember2Relationship;

    @ApiModelProperty("第二位家属联系方式")
    private String familyMember2Contact;

    @ApiModelProperty("第二位家属工作单位")
    private String familyMember2WorkUnit;

    @ApiModelProperty("第二位家属居住地址")
    private String familyMember2Address;

    @ApiModelProperty("第二位家属关系证明附件")
    private String familyMember2RelationsAttch;

    @ApiModelProperty("第二位家属照片")
    private String familyMember2ImageUrl;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("通知家属时间")
    private Date[] notificationFamilyTime;

    @ApiModelProperty("通知会见日期")
    private Date[] notificationMeetingDate;

    @ApiModelProperty("会见开始时间")
    private Date[] meetingStartTime;

    @ApiModelProperty("会见结束时间")
    private Date[] meetingEndTime;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date[] approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("第三位家属ID")
    private String familyMember3Id;

    @ApiModelProperty("第三位家属姓名")
    private String familyMember3Name;

    @ApiModelProperty("第三位家属性别")
    private String familyMember3Gender;

    @ApiModelProperty("第三位家属证件类型")
    private String familyMember3IdType;

    @ApiModelProperty("第三位家属的证件号码")
    private String familyMember3IdNumber;

    @ApiModelProperty("第三位家属与被会见人社会关系")
    private String familyMember3Relationship;

    @ApiModelProperty("第三位家属联系方式")
    private String familyMember3Contact;

    @ApiModelProperty("第三位家属工作单位")
    private String familyMember3WorkUnit;

    @ApiModelProperty("第三位家属居住地址")
    private String familyMember3Address;

    @ApiModelProperty("第三位家属照片")
    private String familyMember3ImageUrl;

    @ApiModelProperty("第一位家属职业")
    private String familyMember1Occupation;

    @ApiModelProperty("第二位家属职业")
    private String familyMember2Occupation;

    @ApiModelProperty("第三位家属职业")
    private String familyMember3Occupation;

    @ApiModelProperty("通知操作人身份证号")
    private String notificationOperatorSfzh;
    @ApiModelProperty("通知操作人")
    private String notificationOperator;
    @ApiModelProperty("通知操作时间")
    private Date notificationOperatorTime;

    @ApiModelProperty("登记操作人身份证号")
    private String checkOperatorSfzh;
    @ApiModelProperty("登记操作人")
    private String checkOperator;
    @ApiModelProperty("登记操作时间")
    private Date checkOperatorTime;

}
