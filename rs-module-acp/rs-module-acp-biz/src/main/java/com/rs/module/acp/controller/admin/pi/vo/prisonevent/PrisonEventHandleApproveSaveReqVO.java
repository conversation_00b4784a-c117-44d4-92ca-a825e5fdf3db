package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情处置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventHandleApproveSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "处置ID不能为空")
    private String id;

    @ApiModelProperty("所属警情")
    @NotEmpty(message = "所属警情不能为空")
    private String eventId;

    @NotEmpty(message = "审批情况列表")
    private List<PrisonEventHandleApproveChildSaveReqVO> approveResultList;
}
