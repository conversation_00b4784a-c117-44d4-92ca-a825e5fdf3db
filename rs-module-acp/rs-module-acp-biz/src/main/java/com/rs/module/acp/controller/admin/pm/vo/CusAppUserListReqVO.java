package com.rs.module.acp.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-监管管理-我的应用配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CusAppUserListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("系统ID")
    private String systemId;

    @ApiModelProperty("用户证件号码")
    private String userIdCard;

}
