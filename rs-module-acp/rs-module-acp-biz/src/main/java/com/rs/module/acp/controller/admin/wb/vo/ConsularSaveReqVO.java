package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-领事外事信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsularSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String name;

    @ApiModelProperty("性别")
    @NotEmpty(message = "性别不能为空")
    private String gender;

    @ApiModelProperty("证件类型")
    @NotEmpty(message = "证件类型不能为空")
    private String idType;

    @ApiModelProperty("证件号码")
    @NotEmpty(message = "证件号码不能为空")
    private String idNumber;

    @ApiModelProperty("国籍")
    private String nationality;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("工作单位")
    private String workUnit;

    @ApiModelProperty("照片")
    private String imageUrl;

}
