package com.rs.module.acp.controller.admin.gj.vo.samecasemanage;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-同案人员管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SameCaseManageSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("案件编号")
    @NotEmpty(message = "案件编号不能为空")
    private String ajbh;

    @ApiModelProperty("同案人监管人员编码")
    @NotEmpty(message = "同案人监管人员编码不能为空")
    private String sameCaseJgrybm;

}
