package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.aloneimprison.AloneImprisonApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.gj.vo.confinement.*;
import com.rs.module.acp.entity.gj.confinement.ConfinementRemoveDO;
import com.rs.module.acp.service.gj.confinement.ConfinementRemoveService;

@Api(tags = "实战平台-管教业务-解除禁闭呈批")
@RestController
@RequestMapping("/acp/gj/confinementRemove")
@Validated
public class ConfinementRemoveController {

    @Resource
    private ConfinementRemoveService confinementRemoveService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-解除禁闭呈批")
    public CommonResult<String> createConfinementRemove(@Valid @RequestBody ConfinementRemoveSaveReqVO createReqVO) {
        try  {
            return success(confinementRemoveService.createConfinementRemove(createReqVO));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-解除禁闭呈批")
    public CommonResult<Boolean> updateConfinementRemove(@Valid @RequestBody ConfinementRemoveSaveReqVO updateReqVO) {
        confinementRemoveService.updateConfinementRemove(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-解除禁闭呈批")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteConfinementRemove(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           confinementRemoveService.deleteConfinementRemove(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-解除禁闭呈批")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ConfinementRemoveRespVO> getConfinementRemove(@RequestParam("id") String id) {
        ConfinementRemoveDO confinementRemove = confinementRemoveService.getConfinementRemove(id);
        return success(BeanUtils.toBean(confinementRemove, ConfinementRemoveRespVO.class));
    }
/*
    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-解除禁闭呈批分页")
    public CommonResult<PageResult<ConfinementRemoveRespVO>> getConfinementRemovePage(@Valid @RequestBody ConfinementRemovePageReqVO pageReqVO) {
        PageResult<ConfinementRemoveDO> pageResult = confinementRemoveService.getConfinementRemovePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ConfinementRemoveRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-解除禁闭呈批列表")
    public CommonResult<List<ConfinementRemoveRespVO>> getConfinementRemoveList(@Valid @RequestBody ConfinementRemoveListReqVO listReqVO) {
        List<ConfinementRemoveDO> list = confinementRemoveService.getConfinementRemoveList(listReqVO);
        return success(BeanUtils.toBean(list, ConfinementRemoveRespVO.class));
    }*/
    @GetMapping("/getDetail")
    @ApiOperation(value = "获得实战平台-管教业务-禁闭详情包含登记，呈批，解除")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ConfinementDetailRespVO> getDetail(@RequestParam("id") String id) {
        ConfinementDetailRespVO detail = confinementRemoveService.getDetailTrack(id);
        return success(detail);
    }
    @PostMapping("/saveInOutRecords")
    @ApiOperation(value = "带入带出登记")
    public CommonResult<Boolean> saveInOutRecords(@Valid @RequestBody InOutRecordsConfinementRemoveSaveVO respVO) {
        return success(confinementRemoveService.saveInOutRecords(respVO));
    }
    //解除登记获取 解除理由
    @GetMapping("/initRemoveReason")
    @ApiOperation(value = "获得实战平台-管教业务-解除禁闭登记页面初始化")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ConfinementDetailRespVO> initRemoveReason(@RequestParam("id") String id) {
        ConfinementDetailRespVO detail = confinementRemoveService.initRemoveReason(id);
        return success(detail);
    }
    @PostMapping("/approve")
    @ApiOperation(value = "领导审批")
    public CommonResult<Boolean> approve(@Valid @RequestBody ConfinementRemoveApproveVO approveReqVO) {
        try  {
            return success(confinementRemoveService.approve(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
}
