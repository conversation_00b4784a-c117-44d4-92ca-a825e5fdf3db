package com.rs.module.acp.controller.admin.gj.vo.undercover;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-耳目反映情况新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UndercoverReportSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("耳目ID")
    @NotEmpty(message = "耳目ID不能为空")
    private String undercoverId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("反映时间")
    @NotNull(message = "反映时间不能为空")
    private Date reportTime;

    @ApiModelProperty("反映情况")
    @NotEmpty(message = "反映情况不能为空")
    private String reportInfo;

    @ApiModelProperty("备注")
    private String remark;

}
