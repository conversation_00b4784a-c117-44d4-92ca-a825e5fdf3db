package com.rs.module.acp.controller.admin.gj.vo.confinement;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsACPSaveVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-出入登记-解除出入登记 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InOutRecordsConfinementRemoveSaveVO extends InOutRecordsACPSaveVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("解除理由")
    private String removeReason;
    @ApiModelProperty("执行情况")
    private String executeSituation;

}
