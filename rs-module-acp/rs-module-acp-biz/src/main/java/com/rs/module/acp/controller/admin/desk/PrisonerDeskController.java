package com.rs.module.acp.controller.admin.desk;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-人员总台")
@RestController
@RequestMapping("/acp/desk/prisoner")
@Validated
public class PrisonerDeskController {

    @Resource
    private PrisonerService prisonerService;

    @GetMapping("/getDetentionPeriod")
    @ApiOperation(value = "获取监管人员关押期限")
    public CommonResult<JSONObject> getDetentionPeriod(@RequestParam("jgrybm") String jgrybm) {
        PrisonerVwRespVO prisoner = prisonerService.getPrisonerByJgrybm(jgrybm);
        Date gyqx = prisoner.getGyqx();
        Date rssj = prisoner.getRssj();
        // 计算已关押天数
        long inDays = 0;
        Date now = DateUtil.beginOfDay(new Date());
        if (ObjectUtil.isNotEmpty(rssj) && now.compareTo(rssj) > 0) {
            inDays = DateUtil.between(rssj, now, DateUnit.DAY, false);
        }
        // 计算剩余关押天数
        long remainDays = 0;
        if (ObjectUtil.isNotEmpty(gyqx) && now.compareTo(gyqx) < 0) {
            remainDays = DateUtil.between(now, gyqx, DateUnit.DAY, false);
        }
        // 计算关押总天数
        Long days = null;
        if (ObjectUtil.isNotEmpty(gyqx) && ObjectUtil.isNotEmpty(rssj)) {
            days = DateUtil.between(rssj, gyqx, DateUnit.DAY, false);
        } else {
            days = inDays;
        }

        JSONObject result = new JSONObject();
        result.put("gyqx", gyqx);
        result.put("rssj", rssj);
        result.put("days", days);
        result.put("inDays", inDays);
        result.put("remainDays", remainDays);

        return success(result);
    }

}
