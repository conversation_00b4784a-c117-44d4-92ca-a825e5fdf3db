package com.rs.module.acp.controller.admin.gj.vo.transitionroom;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡考核登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransitionRoomEvalListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("过渡监室ID")
    private String transitionRoomId;

    @ApiModelProperty("考核依据")
    private String evalBasis;

    @ApiModelProperty("考核内容")
    private String evalContent;

    @ApiModelProperty("考核情况")
    private String evalResult;

    @ApiModelProperty("考核时间")
    private Date[] evalTime;

}
