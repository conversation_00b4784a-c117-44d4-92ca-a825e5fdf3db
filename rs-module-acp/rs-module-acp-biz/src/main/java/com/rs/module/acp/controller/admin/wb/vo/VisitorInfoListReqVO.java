package com.rs.module.acp.controller.admin.wb.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-来访人员信息列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VisitorInfoListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("对外开放登记ID")
    private String visitorRegId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件号码")
    private String idNumber;

}
