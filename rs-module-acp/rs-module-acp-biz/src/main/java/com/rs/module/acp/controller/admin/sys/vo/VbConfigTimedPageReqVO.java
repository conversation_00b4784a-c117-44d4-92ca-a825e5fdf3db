package com.rs.module.acp.controller.admin.sys.vo;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 实战平台-语音播报-定时配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VbConfigTimedPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("播报名称")
    private String vbName;

    @ApiModelProperty("查询脚本")
    private String queryScript;

    @ApiModelProperty("播报内容")
    private String content;

    @ApiModelProperty("播报次数")
    private Short vbNum;

    @ApiModelProperty("优先级")
    private Short priority;

    @ApiModelProperty("是否启用(0否1是)")
    private Short isEnabled;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
