package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-物品顾送明细 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsDeliveryDetailsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("物品顾送ID")
    private String goodsDeliveryId;
    @ApiModelProperty("物品名称")
    private String goodsName;
    @ApiModelProperty("物品数量")
    private Integer goodsQuantity;
    @ApiModelProperty("单位")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GOODS_CONSIGNMENT_UNIT")
    private String unit;
    @ApiModelProperty("物品备注")
    private String remark;
}
