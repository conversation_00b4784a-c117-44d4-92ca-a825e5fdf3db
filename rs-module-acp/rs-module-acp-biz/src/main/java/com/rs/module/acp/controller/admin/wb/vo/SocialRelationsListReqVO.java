package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-社会关系列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SocialRelationsListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("家属姓名")
    private String name;

    @ApiModelProperty("家属性别")
    private String gender;

    @ApiModelProperty("家属证件类型")
    private String idType;

    @ApiModelProperty("家属证件号码")
    private String idNumber;

    @ApiModelProperty("家属与被会见人社会关系")
    private String relationship;

    @ApiModelProperty("家属联系方式")
    private String contact;

    @ApiModelProperty("家属工作单位")
    private String workUnit;

    @ApiModelProperty("家属居住地址")
    private String address;

    @ApiModelProperty("职业")
    private String occupation;

    @ApiModelProperty("照片")
    private String imageUrl;

    @ApiModelProperty("关系证明附件")
    private String relationsAttch;

}
