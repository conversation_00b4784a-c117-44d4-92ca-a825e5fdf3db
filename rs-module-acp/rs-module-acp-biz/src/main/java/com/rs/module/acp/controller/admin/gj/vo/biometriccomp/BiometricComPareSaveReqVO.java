package com.rs.module.acp.controller.admin.gj.vo.biometriccomp;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-生物特征比对新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricComPareSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("提交生物特征类型")
    @NotEmpty(message = "提交生物特征类型不能为空")
    private String biometricType;

    @ApiModelProperty("提交时间")
    @NotNull(message = "提交时间不能为空")
    private Date submitTime;

    @ApiModelProperty("提交办案单位类型  字典：ZD_KSS_BADWLX")
    @NotEmpty(message = "提交办案单位类型不能为空")
    private String caseUnitType;

    @ApiModelProperty("提交办案单位名称  字典：ZD_BADW_GAJG")
    @NotEmpty(message = "提交办案单位名称不能为空")
    private String caseUnitName;

    @ApiModelProperty("提交方式")
    @NotEmpty(message = "提交方式不能为空")
    private String submitMethod;

    @ApiModelProperty("备注")
    private String remark;


}
