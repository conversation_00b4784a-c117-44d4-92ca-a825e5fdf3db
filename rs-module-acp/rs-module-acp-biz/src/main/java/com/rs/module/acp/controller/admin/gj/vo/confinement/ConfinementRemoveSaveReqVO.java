package com.rs.module.acp.controller.admin.gj.vo.confinement;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-解除禁闭呈批新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConfinementRemoveSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("禁闭登记ID")
    @NotEmpty(message = "禁闭登记ID不能为空")
    private String confinementId;

    @ApiModelProperty("使用械具的理由")
    @NotEmpty(message = "使用械具的理由不能为空")
    private String removeReason;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
