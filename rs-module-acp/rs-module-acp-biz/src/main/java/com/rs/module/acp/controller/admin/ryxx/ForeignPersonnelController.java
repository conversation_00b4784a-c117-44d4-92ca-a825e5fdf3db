package com.rs.module.acp.controller.admin.ryxx;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.ForeignPersonnelListRespVO;
import com.rs.module.base.controller.admin.pm.vo.PoreginPersonnelPageReqVO;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 * 外来人员
 *
 * <AUTHOR>
 * @Date 2025/7/18 20:53
 */
@Api(tags = "实战平台 - 外来人员管理")
@RestController
@RequestMapping("/base/pm/foreinPersionnel")
public class ForeignPersonnelController {

    @Resource
    private PrisonerService prisonerService;

    @PostMapping("/getForeinPersionnelSelectCompoment")
    @ApiOperation(value = " 外来人员选择组件查询接口")
    public CommonResult<PageResult<ForeignPersonnelListRespVO>> getForeinPersionnelSelectCompoment(
            @Valid @RequestBody PoreginPersonnelPageReqVO pageReqVO) {
        pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        PageResult<ForeignPersonnelListRespVO> pageResult =
                prisonerService.getForeinPersionnelSelectCompoment(pageReqVO);
        return success(pageResult);
    }

}
