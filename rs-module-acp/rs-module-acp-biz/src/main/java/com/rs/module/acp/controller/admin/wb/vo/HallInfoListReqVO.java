package com.rs.module.acp.controller.admin.wb.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-服务大厅信息发布列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HallInfoListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("信息标题")
    private String title;

    @ApiModelProperty("信息内容")
    private String infoContent;

    @ApiModelProperty("附件文件类型")
    private String attachmentType;

    @ApiModelProperty("附件上传地址")
    private String attachmentUrl;

    @ApiModelProperty("状态")
    private String status;

}
