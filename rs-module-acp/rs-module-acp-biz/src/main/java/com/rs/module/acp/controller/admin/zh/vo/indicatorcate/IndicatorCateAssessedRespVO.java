package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类与被考评人关联 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorCateAssessedRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("指标分类ID")
    private String indicatorCateId;
    @ApiModelProperty("被考核对象类型，01:岗位、02：角色、03：用户")
    private String assessedObjectType;
    @ApiModelProperty("assessed_object_id")
    private String assessedObjectId;
    @ApiModelProperty("assessed_object_name")
    private String assessedObjectName;
}
