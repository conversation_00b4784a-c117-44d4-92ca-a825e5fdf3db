package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件终端预警新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventTerminalWarningSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("警情地点")
    private String areaId;

    @ApiModelProperty("警情地点名称")
    private String areaName;

    @ApiModelProperty("发生时间")
    private Date happenTime;

    @ApiModelProperty("设备类型ID")
    private String deviceTypeId;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;

    @ApiModelProperty("设备ID")
    private String deviceId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("警情详情")
    private String eventDetails;

    @ApiModelProperty("事件来源（字典：ZD_JJKS_SQLY）")
    @NotNull(message = "事件来源不能为空")
    private String eventSrc;

    @ApiModelProperty("关联报警录像文件")
    private String eventVideo;

    @ApiModelProperty("机构编码")
    @NotNull(message = "机构编码不能为空")
    private String orgCode;

}
