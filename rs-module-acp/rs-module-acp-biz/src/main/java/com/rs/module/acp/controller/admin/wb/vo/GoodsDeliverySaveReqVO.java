package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-物品顾送登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsDeliverySaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("顾送编号")
    private String deliveryNo;

    @ApiModelProperty("顾送日期")
    private Date deliveryDate;

    @ApiModelProperty("顾送人姓名")
    @NotEmpty(message = "顾送人姓名不能为空")
    private String senderName;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("证件类型")
    @NotEmpty(message = "证件类型不能为空")
    private String idType;

    @ApiModelProperty("证件号码")
    @NotEmpty(message = "证件号码不能为空")
    private String idNumber;

    @ApiModelProperty("与被监管人关系")
    private String relationship;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("户籍地址")
    private String householdAddress;

    @ApiModelProperty("物品照片存储路径")
    private String goodsPhotoPath;

    @ApiModelProperty("物品信息")
    private String goodsInfo;

    @ApiModelProperty("物品总数")
    private Integer goodsTotal;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("签收时间")
    private Date receiptTime;

    @ApiModelProperty("签名（存储签名文件路径或文本）")
    private String signature;

    @ApiModelProperty("拒签原因")
    private String rejectionReason;

    @ApiModelProperty("顾送物品列表")
    List<GoodsDeliveryDetailsSaveReqVO> goodsList;

}
