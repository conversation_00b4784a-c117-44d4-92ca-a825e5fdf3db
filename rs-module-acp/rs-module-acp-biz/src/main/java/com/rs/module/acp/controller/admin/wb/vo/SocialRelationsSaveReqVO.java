package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-社会关系新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SocialRelationsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("家属信息")
    private List<SocialRelationsChildSaveReqVO> socialRelationList;
}
