package com.rs.module.acp.controller.admin.gj;

import com.bsp.common.util.CollectionUtil;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.*;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.InOutRecordsDO;
import com.rs.module.acp.service.gj.InOutRecordsService;

@Api(tags = "实战平台-管教业务-出入登记")
@RestController
@RequestMapping("/acp/gj/inOutRecords")
@Validated
public class InOutRecordsController {

    @Resource
    private InOutRecordsService inOutRecordsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-出入登记")
    public CommonResult<String> createInOutRecords(@Valid @RequestBody InOutRecordsSaveReqVO createReqVO) {
        return success(inOutRecordsService.createInOutRecords(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-出入登记")
    public CommonResult<Boolean> updateInOutRecords(@Valid @RequestBody InOutRecordsSaveReqVO updateReqVO) {
        inOutRecordsService.updateInOutRecords(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-出入登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteInOutRecords(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           inOutRecordsService.deleteInOutRecords(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-出入登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<InOutRecordsRespVO> getInOutRecords(@RequestParam("id") String id) {
        InOutRecordsDO inOutRecords = inOutRecordsService.getInOutRecords(id);
        return success(BeanUtils.toBean(inOutRecords, InOutRecordsRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-出入登记分页")
    public CommonResult<PageResult<InOutRecordsRespVO>> getInOutRecordsPage(@Valid @RequestBody InOutRecordsPageReqVO pageReqVO) {
        PageResult<InOutRecordsDO> pageResult = inOutRecordsService.getInOutRecordsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InOutRecordsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-出入登记列表")
    public CommonResult<List<InOutRecordsRespVO>> getInOutRecordsList(@Valid @RequestBody InOutRecordsListReqVO listReqVO) {
        List<InOutRecordsDO> list = inOutRecordsService.getInOutRecordsList(listReqVO);
        return success(BeanUtils.toBean(list, InOutRecordsRespVO.class));
    }
    @PostMapping("/getListByJgrybm")
    @ApiOperation(value = "获得实战平台-管教业务-出入登记列表,默认desc inoutTime")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true),
            @ApiImplicitParam(name = "businessId", value = "业务ID", required = false),
            @ApiImplicitParam(name = "inoutType", value = "进出监室类型（字典：ZD_GJJCJSLX）01 出监,02 入监", required = false)
    })
    public CommonResult<List<InOutRecordsRespVO>> getListByJgrybm(@RequestParam String jgrybm, @RequestParam(required = false) String businessId,@RequestParam(required = false) String inoutType) {
        List<InOutRecordsDO> list = inOutRecordsService.getListInOutRecordsByJgrybm(jgrybm,businessId,inoutType);
        return success(BeanUtils.toBean(list, InOutRecordsRespVO.class));
    }
    @GetMapping("/getJgrmBusiness")
    @ApiOperation(value = "获得实战平台-管教业务-出入登记事由")
    public CommonResult<List<InOutRecordsBusinessRespVO>> getJgrmBusiness(@RequestParam("jgrybms") String jgrybms){
        String[] strings = jgrybms.split(",");
        if(CollectionUtil.isNotNull(strings)){
            if(strings.length == 1){
                List<InOutRecordsDO> list = inOutRecordsService.getListInOutRecordsByJgrybm(strings[0]);
                return success(BeanUtils.toBean(list, InOutRecordsBusinessRespVO.class));
            }
            //批量查询
        }
        return error("jgrybm 参数有误");
    }

    @PostMapping("/initOutRecords")
    @ApiOperation(value = "初始化带出记录")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "jgrybm", name = "jgrybm", value = "被监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "roomId", name = "roomId", value = "被监管人员监室号", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "businessType", name = "businessType", value = "业务类型（字典：ZD_GJXZDCSY）", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "businessId", name = "businessId", value = "业务id后续用于查询出入记录", required = true, dataType = "String")
    })
    public CommonResult<String> initOutRecords(@RequestParam String jgrybm,@RequestParam String roomId,@RequestParam String businessType,@RequestParam String businessId) {
        return success(inOutRecordsService.initOutRecords(jgrybm,roomId,businessType,businessId));
    }

    //调用InOutRecordsService.saveOutRecordsAndInitInRecords 保存带出记录并初始化带入记录
    @PostMapping("/saveOutRecordsAndInitInRecords")
    @ApiOperation(value = "保存带出记录并初始化带入记录")
    public CommonResult<String> saveOutRecordsAndInitInRecords(@Valid @RequestBody InOutRecordsSaveReqVO saveReqVO) {
        return success(inOutRecordsService.saveOutRecordsAndInitInRecords(saveReqVO));
    }

    /*@PostMapping("/saveInRecords")
    @ApiOperation(value = "保存带入记录")
    public CommonResult<String> saveInRecords(@Valid @RequestBody InOutRecordsSaveReqVO saveReqVO) {
        return success(inOutRecordsService.saveInRecords(saveReqVO));
    }*/
    //监室人员列表(当天业务状态展示业务状态) 参数:监室编码
   /* @PostMapping("/getJgryList")
    @ApiOperation(value = "获得实战平台-管教业务-出入登记列表")
    public CommonResult<List<InOutRecordsRespVO>> getJgryList(@Valid @RequestBody InOutRecordsListReqVO listReqVO) {
        List<InOutRecordsDO> list = inOutRecordsService.getInOutRecordsList(listReqVO);
        return success(BeanUtils.toBean(list, InOutRecordsRespVO.class));
    }


    //出入登记人员统计
    @PostMapping("/getInOutRecordsCount")
    @ApiOperation(value = "获得实战平台-管教业务-出入登记列表")
    public CommonResult<List<InOutRecordsCountRespVO>> getInOutRecordsCount() {
        // 监室总人数  外出人数，根据业务类型字典统计对应人数
        List<InOutRecordsDO> list = inOutRecordsService.getInOutRecordsList();
        return success(BeanUtils.toBean(list, InOutRecordsCountRespVO.class));
    }*/

}
