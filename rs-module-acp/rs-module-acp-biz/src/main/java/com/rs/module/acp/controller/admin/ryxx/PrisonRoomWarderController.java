package com.rs.module.acp.controller.admin.ryxx;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderListReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderSaveReqVO;
import com.rs.module.base.entity.pm.PrisonRoomWarderDO;
import com.rs.module.base.service.pm.PrisonRoomWarderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-监室主协管人员")
@RestController
@RequestMapping("/base/pm/prisonRoomWarder")
@Validated
public class PrisonRoomWarderController {

    @Resource
    private PrisonRoomWarderService prisonRoomWarderService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-监室主协管人员")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomWarder:create", operateType = LogOperateType.CREATE, title = "创建实战平台-监管管理-监室主协管人员",
    success = "创建实战平台-监管管理-监室主协管人员成功", fail = "创建实战平台-监管管理-监室主协管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonRoomWarder(@Valid @RequestBody PrisonRoomWarderSaveReqVO createReqVO) {
        return success(prisonRoomWarderService.createPrisonRoomWarder(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-监室主协管人员")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomWarder:update", operateType = LogOperateType.UPDATE, title = "更新实战平台-监管管理-监室主协管人员",
    success = "更新实战平台-监管管理-监室主协管人员成功", fail = "更新实战平台-监管管理-监室主协管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrisonRoomWarder(@Valid @RequestBody PrisonRoomWarderSaveReqVO updateReqVO) {
        prisonRoomWarderService.updatePrisonRoomWarder(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-监室主协管人员")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomWarder:delete", operateType = LogOperateType.DELETE, title = "删除实战平台-监管管理-监室主协管人员",
    success = "删除实战平台-监管管理-监室主协管人员成功", fail = "删除实战平台-监管管理-监室主协管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrisonRoomWarder(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonRoomWarderService.deletePrisonRoomWarder(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-监室主协管人员")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomWarder:get", operateType = LogOperateType.QUERY, title = "获取实战平台-监管管理-监室主协管人员", bizNo = "{{#id}}", success = "获取实战平台-监管管理-监室主协管人员成功", fail = "获取实战平台-监管管理-监室主协管人员失败", extraInfo = "{{#id}}")
    public CommonResult<PrisonRoomWarderRespVO> getPrisonRoomWarder(@RequestParam("id") String id) {
        PrisonRoomWarderDO prisonRoomWarder = prisonRoomWarderService.getPrisonRoomWarder(id);
        return success(BeanUtils.toBean(prisonRoomWarder, PrisonRoomWarderRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-监室主协管人员分页")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomWarder:page", operateType = LogOperateType.QUERY, title = "获得实战平台-监管管理-监室主协管人员分页",
    success = "获得实战平台-监管管理-监室主协管人员分页成功", fail = "获得实战平台-监管管理-监室主协管人员分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrisonRoomWarderRespVO>> getPrisonRoomWarderPage(@Valid @RequestBody PrisonRoomWarderPageReqVO pageReqVO) {
        PageResult<PrisonRoomWarderDO> pageResult = prisonRoomWarderService.getPrisonRoomWarderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonRoomWarderRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-监室主协管人员列表")
    @LogRecordAnnotation(bizModule = "acp:prisonRoomWarder:list", operateType = LogOperateType.QUERY, title = "获得实战平台-监管管理-监室主协管人员列表",
    success = "获得实战平台-监管管理-监室主协管人员列表成功", fail = "获得实战平台-监管管理-监室主协管人员列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrisonRoomWarderRespVO>> getPrisonRoomWarderList(@Valid @RequestBody PrisonRoomWarderListReqVO listReqVO) {
    List<PrisonRoomWarderDO> list = prisonRoomWarderService.getPrisonRoomWarderList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonRoomWarderRespVO.class));
    }

}
