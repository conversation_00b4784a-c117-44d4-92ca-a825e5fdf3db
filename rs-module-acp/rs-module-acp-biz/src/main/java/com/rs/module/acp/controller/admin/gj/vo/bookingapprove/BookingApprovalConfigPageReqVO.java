package com.rs.module.acp.controller.admin.gj.vo.bookingapprove;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-预约审核配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookingApprovalConfigPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("预约类别")
    private String bookingCategory;

    @ApiModelProperty("服务类别")
    private String serviceCategory;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
