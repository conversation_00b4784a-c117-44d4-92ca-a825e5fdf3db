package com.rs.module.acp.controller.admin.pi.vo.shifthandover;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.PatrolRecordRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-巡控交接班登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShiftHandoverRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("人员情况")
    private String ryqk;
    @ApiModelProperty("重点关注人员")
    private String zdgzry;
    @ApiModelProperty("巡控登记")
    private String xkdj;
    @ApiModelProperty("交班总人数")
    private Integer totalPersons;
    @ApiModelProperty("交班点名总人数")
    private Integer rollCallTotal;
    @ApiModelProperty("待处理问题")
    private String pendingIssues;
    @ApiModelProperty("交班人身份证号")
    private String handoverPersonSfzh;
    @ApiModelProperty("交班人")
    private String handoverPerson;
    @ApiModelProperty("交班时间")
    private Date handoverTime;
    @ApiModelProperty("接班总人数")
    private Integer takeoverTotalPersons;
    @ApiModelProperty("接班人身份证号")
    private String takeoverPersonSfzh;
    @ApiModelProperty("接班人")
    private String takeoverPerson;
    @ApiModelProperty("接班时间")
    private Date takeoverTime;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("巡控登记内容")
    List<PatrolRecordRespVO> xkdjList;
}
