package com.rs.module.acp.controller.admin.pi.vo.shifthandover;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-巡控交接班登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShiftRegReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("人员情况 json")
    @NotEmpty(message = "人员情况不能为空")
    private String ryqk;

    @ApiModelProperty("重点关注人员 json")
    @NotEmpty(message = "重点关注人员不能为空")
    private String zdgzry;

    @ApiModelProperty("巡控登记 id")
    private String xkdj;

    @ApiModelProperty("交班总人数")
    //@NotNull(message = "交班总人数不能为空")
    private Integer totalPersons;

    @ApiModelProperty("交班点名总人数")
    //@NotNull(message = "交班点名总人数不能为空")
    private Integer rollCallTotal;

    @ApiModelProperty("待处理问题")
    private String pendingIssues;

    @ApiModelProperty("交班人身份证号")
    @NotEmpty(message = "交班人身份证号不能为空")
    private String handoverPersonSfzh;

    @ApiModelProperty("交班人")
    @NotEmpty(message = "交班人不能为空")
    private String handoverPerson;

    @ApiModelProperty("交班时间")
    @NotNull(message = "交班时间不能为空")
    private Date handoverTime;

    @ApiModelProperty("交接班对象")
    @NotNull(message = "交接班对象不能为空")
    private String handoverObject;

    @ApiModelProperty("交接班对象身份证号")
    @NotNull(message = "交接班对象身份证号不能为空")
    private String handoverObjectSfzh;

}
