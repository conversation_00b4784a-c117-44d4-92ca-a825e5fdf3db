package com.rs.module.acp.controller.admin.gj.vo.emergencydeparture;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-紧急出所登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EmergencyDepartureSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("就诊医院")
    @NotEmpty(message = "就诊医院不能为空")
    private String hospital;

    @ApiModelProperty("病情说明")
    @NotEmpty(message = "病情说明不能为空")
    private String symptomDesc;

    @ApiModelProperty("经办民警身份证号, 若不填，从session获取")
    private String operatePoliceSfzh;

    @ApiModelProperty("经办民警, 若不填，从session获取")
    private String operatePolice;

    @ApiModelProperty("经办时间, 若不填，默认后台时间")
    private Date operateTime;

}
