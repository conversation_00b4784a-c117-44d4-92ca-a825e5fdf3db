package com.rs.module.acp.controller.admin.wb;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.PrisonAreaInoutDO;
import com.rs.module.acp.service.wb.PrisonAreaInoutService;

@Api(tags = "实战平台-窗口业务-出入监区登记")
@RestController
@RequestMapping("/acp/wb/prisonAreaInout")
@Validated
public class PrisonAreaInoutController {

    @Resource
    private PrisonAreaInoutService prisonAreaInoutService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建出入监区登记")
    @LogRecordAnnotation(bizModule = "acp:prisonAreaInout:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建出入监区登记",
            success = "实战平台-窗口业务-创建出入监区登记成功", fail = "实战平台-窗口业务-创建出入监区登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonAreaInout(@Valid @RequestBody PrisonAreaInoutSaveReqVO createReqVO) {
        return success(prisonAreaInoutService.createPrisonAreaInout(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新出入监区登记")
    @LogRecordAnnotation(bizModule = "acp:prisonAreaInout:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新出入监区登记",
            success = "实战平台-窗口业务-更新出入监区登记成功", fail = "实战平台-窗口业务-更新出入监区登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrisonAreaInout(@Valid @RequestBody PrisonAreaInoutSaveReqVO updateReqVO) {
        prisonAreaInoutService.updatePrisonAreaInout(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得出入监区登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonAreaInout:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得出入监区登记",
            success = "实战平台-窗口业务-获得出入监区登记成功", fail = "实战平台-窗口业务-获得出入监区登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<PrisonAreaInoutRespVO> getPrisonAreaInout(@RequestParam("id") String id) {
        PrisonAreaInoutDO prisonAreaInout = prisonAreaInoutService.getPrisonAreaInout(id);
        return success(BeanUtils.toBean(prisonAreaInout, PrisonAreaInoutRespVO.class));
    }

}
