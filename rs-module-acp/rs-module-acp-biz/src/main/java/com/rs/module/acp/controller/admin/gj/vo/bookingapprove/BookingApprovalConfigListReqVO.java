package com.rs.module.acp.controller.admin.gj.vo.bookingapprove;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-预约审核配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BookingApprovalConfigListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("预约类别")
    private String bookingCategory;

    @ApiModelProperty("服务类别")
    private String serviceCategory;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty(value = "机构代码", hidden = true)
    private String orgCode;

}
