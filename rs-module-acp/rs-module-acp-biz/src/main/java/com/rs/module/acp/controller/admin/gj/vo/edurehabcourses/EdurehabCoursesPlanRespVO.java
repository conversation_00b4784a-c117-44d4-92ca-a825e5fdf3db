package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程计划 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesPlanRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("课程计划名称")
    private String planName;
    @ApiModelProperty("plan_code")
    private String planCode;
    @ApiModelProperty("课程时段-开始")
    private Date startDate;
    @ApiModelProperty("课程时段-结束")
    private Date endDate;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JYKFKBZT")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("课程时段-开始")
    private String startDateStr;
    @ApiModelProperty("课程时段-结束")
    private String endDateStr;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;

    @ApiModelProperty("监区")
    private List<JqAreaVO> jqAreaVOList;

    @ApiModelProperty("时段")
    private List<EdurehabTimeSlotRespVO> timeList;

    @ApiModelProperty("课表记录")
    private List<EdurehabCoursesRecordRespVO> listRecord;

    @ApiModelProperty("课表信息整合list")
    private List<EdurehabDayRespVO> edurehabList;

}
