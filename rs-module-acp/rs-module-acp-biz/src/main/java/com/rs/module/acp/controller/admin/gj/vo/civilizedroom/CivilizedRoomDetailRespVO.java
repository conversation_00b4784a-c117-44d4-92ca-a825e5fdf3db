package com.rs.module.acp.controller.admin.gj.vo.civilizedroom;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明监室登记明细 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedRoomDetailRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("文明监室登记ID")
    private String civilizedRoomId;
    @ApiModelProperty("room_id")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("违规数")
    private Integer numberOfViolations;
    @ApiModelProperty("评选理由")
    private String selectionReason;
    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("监室信息")
    private AreaPrisonRoomRespVO areaPrisonRoomRespVO;
}
