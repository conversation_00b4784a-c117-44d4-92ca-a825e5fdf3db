package com.rs.module.acp.controller.admin.pm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtListReqVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtRespVO;
import com.rs.module.base.controller.admin.pm.vo.RiskAssmtSaveReqVO;
import com.rs.module.base.entity.pm.RiskAssmtDO;
import com.rs.module.base.service.pm.RiskAssmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-风险评估")
@RestController
@RequestMapping("/acp/pm/riskAssmt")
@Validated
public class RiskAssmtController {

    @Resource
    private RiskAssmtService riskAssmtService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-风险评估")
    @LogRecordAnnotation(bizModule = "acp:riskAssmt:create", operateType = LogOperateType.CREATE, title = "创建实战平台-监管管理-风险评估",
    success = "创建实战平台-监管管理-风险评估成功", fail = "创建实战平台-监管管理-风险评估失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createRiskAssmt(@Valid @RequestBody RiskAssmtSaveReqVO createReqVO) {
        return success(riskAssmtService.createRiskAssmt(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-风险评估")
    @LogRecordAnnotation(bizModule = "acp:riskAssmt:update", operateType = LogOperateType.UPDATE, title = "更新实战平台-监管管理-风险评估",
    success = "更新实战平台-监管管理-风险评估成功", fail = "更新实战平台-监管管理-风险评估失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateRiskAssmt(@Valid @RequestBody RiskAssmtSaveReqVO updateReqVO) {
        riskAssmtService.updateRiskAssmt(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-风险评估")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:riskAssmt:delete", operateType = LogOperateType.DELETE, title = "删除实战平台-监管管理-风险评估",
    success = "删除实战平台-监管管理-风险评估成功", fail = "删除实战平台-监管管理-风险评估失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteRiskAssmt(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           riskAssmtService.deleteRiskAssmt(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-风险评估")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<RiskAssmtRespVO> getRiskAssmt(@RequestParam("id") String id) {
        RiskAssmtDO riskAssmt = riskAssmtService.getRiskAssmt(id);
        return success(BeanUtils.toBean(riskAssmt, RiskAssmtRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-风险评估分页")
    public CommonResult<PageResult<RiskAssmtRespVO>> getRiskAssmtPage(@Valid @RequestBody RiskAssmtPageReqVO pageReqVO) {
        PageResult<RiskAssmtDO> pageResult = riskAssmtService.getRiskAssmtPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RiskAssmtRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-风险评估列表")
    public CommonResult<List<RiskAssmtRespVO>> getRiskAssmtList(@Valid @RequestBody RiskAssmtListReqVO listReqVO) {
        List<RiskAssmtDO> list = riskAssmtService.getRiskAssmtList(listReqVO);
        return success(BeanUtils.toBean(list, RiskAssmtRespVO.class));
    }
}
