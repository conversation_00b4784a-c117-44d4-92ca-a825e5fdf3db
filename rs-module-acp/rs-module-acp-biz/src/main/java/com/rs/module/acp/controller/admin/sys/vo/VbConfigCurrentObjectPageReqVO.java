package com.rs.module.acp.controller.admin.sys.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-语音播报-即时播报对象分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VbConfigCurrentObjectPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("配置Id")
    private String configId;

    @ApiModelProperty("播报监所Id")
    private String vbPrisonId;

    @ApiModelProperty("播报监区Id")
    private String vbAreaId;

    @ApiModelProperty("播报监室Id")
    private String vbRoomId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
