package com.rs.module.acp.controller.admin.pi.vo.patrolrecord;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-巡视登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PatrolRecordRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("巡控室ID")
    private String patrolRoomId;
    @ApiModelProperty("巡控室名称")
    private String patrolRoomName;
    @ApiModelProperty("监室号")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JSQY")
    private String roomId;
    @ApiModelProperty("登记类型 01:自动,02 手动")
    private String recordType;
    @ApiModelProperty("巡控人员身份证号")
    private String patrolStaffSfzh;
    @ApiModelProperty("巡控人员")
    private String patrolStaff;
    @ApiModelProperty("登记内容")
    private String recordContent;
    @ApiModelProperty("是否岗位协同：1-是/0-否")
    private Short isPostCoordination;
    @ApiModelProperty("岗位协同，多选项 ZD_XSGKXTGW")
    //@Trans(type = TransType.DICTIONARY, key = "ZD_XSGKXTGW")
    private String coordinationPosts;
    @ApiModelProperty("岗位协同人员名称")
    private String coordinationPostsName;
    @ApiModelProperty("待办来源 字典：ZD_XSGKDBLY")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XSGKDBLY")
    private String todoSource;
    @ApiModelProperty("待办事由")
    private String todoReason;
    @ApiModelProperty("待办推送人身份证号")
    private String todoPersonSfzh;
    @ApiModelProperty("待办推送人")
    private String todoPerson;
    @ApiModelProperty("待办推送岗位")
    private String todoPost;
    @ApiModelProperty("待办推送时间")
    private Date todoPushTime;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_XSGKYWLX")
    private String status;
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记经办时间")
    private Date operatorTime;
    @ApiModelProperty("岗位协同推送对象证件号码")
    private String pushTargetIdCard;
    @ApiModelProperty("岗位协同推送对象")
    private String pushTarget;
    @ApiModelProperty("岗位协同推送内容")
    private String pushContent;

}
