package com.rs.module.acp.controller.admin.gj.vo.transitionroom;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡考核登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransitionRoomEvalPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("过渡监室ID")
    private String transitionRoomId;

    @ApiModelProperty("考核依据")
    private String evalBasis;

    @ApiModelProperty("考核内容")
    private String evalContent;

    @ApiModelProperty("考核情况")
    private String evalResult;

    @ApiModelProperty("考核时间")
    private Date[] evalTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
