package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-服务大厅信息发布 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class HallInfoLargeScreenRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("是否轮播(0:否，1：是)")
    private Short isCarousel;

    @ApiModelProperty("信息列表")
    private List<HallInfoRespVO> infoList;
}
