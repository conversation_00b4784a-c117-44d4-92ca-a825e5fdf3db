package com.rs.module.acp.controller.admin.gj.vo.transitionroom;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡考核登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransitionRoomEvalRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("考核人")
    private String addUserName;

    @ApiModelProperty("过渡监室ID")
    private String transitionRoomId;

    @ApiModelProperty("考核依据")
    private String evalBasis;

    @ApiModelProperty("考核内容")
    private String evalContent;

    @ApiModelProperty("考核情况")
    private String evalResult;

    @ApiModelProperty("考核时间")
    private Date evalTime;

    @ApiModelProperty("备注")
    private String remark;
}
