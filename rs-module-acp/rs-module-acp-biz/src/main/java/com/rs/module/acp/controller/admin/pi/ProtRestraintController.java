package com.rs.module.acp.controller.admin.pi;

import com.rs.module.base.vo.ApproveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pi.vo.*;
import com.rs.module.acp.entity.pi.ProtRestraintDO;
import com.rs.module.acp.service.pi.ProtRestraintService;

@Api(tags = "实战平台-巡视管控-保护性约束")
@RestController
@RequestMapping("/acp/pi/protRestraint")
@Validated
public class ProtRestraintController {

    @Resource
    private ProtRestraintService protRestraintService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-巡视管控-保护性约束")
    public CommonResult<String> createProtRestraint(@Valid @RequestBody ProtRestraintSaveReqVO createReqVO) {
        try{
            return success(protRestraintService.createProtRestraint(createReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-巡视管控-保护性约束")
    public CommonResult<Boolean> updateProtRestraint(@Valid @RequestBody ProtRestraintSaveReqVO updateReqVO) {
        protRestraintService.updateProtRestraint(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-巡视管控-保护性约束")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteProtRestraint(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           protRestraintService.deleteProtRestraint(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-巡视管控-保护性约束")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ProtRestraintRespVO> getProtRestraint(@RequestParam("id") String id) {
        ProtRestraintDO protRestraint = protRestraintService.getProtRestraint(id);
        return success(BeanUtils.toBean(protRestraint, ProtRestraintRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-巡视管控-保护性约束分页")
    public CommonResult<PageResult<ProtRestraintRespVO>> getProtRestraintPage(@Valid @RequestBody ProtRestraintPageReqVO pageReqVO) {
        PageResult<ProtRestraintDO> pageResult = protRestraintService.getProtRestraintPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProtRestraintRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-巡视管控-保护性约束列表")
    public CommonResult<List<ProtRestraintRespVO>> getProtRestraintList(@Valid @RequestBody ProtRestraintListReqVO listReqVO) {
        List<ProtRestraintDO> list = protRestraintService.getProtRestraintList(listReqVO);
        return success(BeanUtils.toBean(list, ProtRestraintRespVO.class));
    }
    @PostMapping("/approve")
    @ApiOperation(value = "审批实战平台-巡视管控-保护性约束申请")
    public CommonResult<Boolean> approve(@Valid @RequestBody ApproveReqVO approveReqVO) {
        try{
            return success(protRestraintService.approve(approveReqVO));
        }catch (Exception e){
            return CommonResult.error(e.getMessage());
        }
    }
}
