package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复活动 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabActivityRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("活动时间")
    private Date activityTime;
    @ApiModelProperty("康复民警身份证号")
    private String rehabPoliceSfzh;
    @ApiModelProperty("康复民警姓名")
    private String rehabPolice;
    @ApiModelProperty("活动主题")
    @Trans(type = TransType.DICTIONARY, key = "ZD_KFJYHD_HDZT")
    private String activityTopic;
    @ApiModelProperty("参加人员身份证号")
    private String jgrybm;
    @ApiModelProperty("参加人员姓名")
    private String jgryxm;
    @ApiModelProperty("参加人数")
    private Integer participantCount;
    @ApiModelProperty("活动详情")
    private String activityDetails;
    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("参加人员信息集合")
    private List<PrisonerVwRespVO> jgryxxList;
}
