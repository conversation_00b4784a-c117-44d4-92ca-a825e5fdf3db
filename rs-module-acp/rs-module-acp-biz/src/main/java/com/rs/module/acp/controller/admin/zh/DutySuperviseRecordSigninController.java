package com.rs.module.acp.controller.admin.zh;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRecordSigninDO;
import com.rs.module.acp.service.zh.DutySuperviseRecordSigninService;

@Api(tags = "综合管理-值班管理-值班督导记录签至")
@RestController
@RequestMapping("/acp/zh/dutySuperviseRecordSignin")
@Validated
public class DutySuperviseRecordSigninController {

    @Resource
    private DutySuperviseRecordSigninService dutySuperviseRecordSigninService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-值班管理-值班督导记录签至")
    public CommonResult<String> createDutySuperviseRecordSignin(@Valid @RequestBody DutySuperviseRecordSigninSaveReqVO createReqVO) {
        return success(dutySuperviseRecordSigninService.createDutySuperviseRecordSignin(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-值班管理-值班督导记录签至")
    public CommonResult<Boolean> updateDutySuperviseRecordSignin(@Valid @RequestBody DutySuperviseRecordSigninSaveReqVO updateReqVO) {
        dutySuperviseRecordSigninService.updateDutySuperviseRecordSignin(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-值班管理-值班督导记录签至")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDutySuperviseRecordSignin(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           dutySuperviseRecordSigninService.deleteDutySuperviseRecordSignin(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-值班管理-值班督导记录签至")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DutySuperviseRecordSigninRespVO> getDutySuperviseRecordSignin(@RequestParam("id") String id) {
        DutySuperviseRecordSigninDO dutySuperviseRecordSignin = dutySuperviseRecordSigninService.getDutySuperviseRecordSignin(id);
        return success(BeanUtils.toBean(dutySuperviseRecordSignin, DutySuperviseRecordSigninRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-值班管理-值班督导记录签至分页")
    public CommonResult<PageResult<DutySuperviseRecordSigninRespVO>> getDutySuperviseRecordSigninPage(@Valid @RequestBody DutySuperviseRecordSigninPageReqVO pageReqVO) {
        PageResult<DutySuperviseRecordSigninDO> pageResult = dutySuperviseRecordSigninService.getDutySuperviseRecordSigninPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DutySuperviseRecordSigninRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-值班管理-值班督导记录签至列表")
    public CommonResult<List<DutySuperviseRecordSigninRespVO>> getDutySuperviseRecordSigninList(@Valid @RequestBody DutySuperviseRecordSigninListReqVO listReqVO) {
        List<DutySuperviseRecordSigninDO> list = dutySuperviseRecordSigninService.getDutySuperviseRecordSigninList(listReqVO);
        return success(BeanUtils.toBean(list, DutySuperviseRecordSigninRespVO.class));
    }
}
