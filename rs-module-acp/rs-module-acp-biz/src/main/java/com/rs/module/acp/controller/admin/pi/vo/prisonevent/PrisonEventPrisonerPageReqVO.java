package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件在押人员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonEventPrisonerPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所情事件ID")
    private String eventId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("扣分值")
    private Integer deductPoint;

    @ApiModelProperty("应扣分值")
    private Integer shouldDeductPoint;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
