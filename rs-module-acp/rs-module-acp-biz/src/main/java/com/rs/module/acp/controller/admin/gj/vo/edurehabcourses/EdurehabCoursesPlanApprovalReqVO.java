package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程计划新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesPlanApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("审批结果  02：不通过  04：通过")
    @NotEmpty(message = "审批结果 不能为空")
    private String status;

    @ApiModelProperty("审批意见")
    @NotEmpty(message = "审批意见 不能为空")
    private String approvelComment;

}
