package com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明监室登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedPersonneApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键 不能为空")
    private String id;

    @ApiModelProperty("审批结果  03：不通过  04：通过")
    @NotEmpty(message = "审批结果 不能为空")
    private String approvelResult;

    @ApiModelProperty("审批意见")
    @NotEmpty(message = "审批意见 不能为空")
    private String approvelComment;

}
