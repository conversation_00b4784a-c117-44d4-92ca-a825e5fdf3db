package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 综合管理-绩效考核子指标 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorSubRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所属主指标ID")
    private String mainIndicatorId;
    @ApiModelProperty("是否必填")
    private Short isRequired;
    @ApiModelProperty("指标描述")
    private String description;
    @ApiModelProperty("加减分，字典：ADD：加分、SUBTRACT：扣分")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_JJF")
    private String addSubtract;
    @ApiModelProperty("分值类型，字典：FIXED：固定分，RANGE：范围分")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_FZLX")
    private String scoreType;
    @ApiModelProperty("基础分值")
    private BigDecimal baseScore;
    @ApiModelProperty("最小分值")
    private BigDecimal minScore;
    @ApiModelProperty("最大分值（仅范围分有效）")
    private BigDecimal maxScore;
    @ApiModelProperty("排序序号")
    private Integer sortOrder;
}
