package com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明个人登记明细新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedPersonneDetailSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("文明个人登记ID")
    @NotEmpty(message = "文明个人登记ID不能为空")
    private String civilizedPersonneId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("room_id")
    @NotEmpty(message = "room_id不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("奖励次数")
    private Integer numberOfReward;

    @ApiModelProperty("违规次数")
    @NotNull(message = "违规次数不能为空")
    private Integer numberOfViolations;

    @ApiModelProperty("惩罚次数")
    private Integer numberOfPunishment;

    @ApiModelProperty("评选理由")
    @NotEmpty(message = "评选理由不能为空")
    private String selectionReason;

    @ApiModelProperty("附件地址")
    private String attUrl;

}
