package com.rs.module.acp.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-定位标签与人员绑定-智能腕带新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LocalsenseTagPersonZnwdSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("标签id")
    @NotEmpty(message = "标签id不能为空")
    private String tagId;

    @ApiModelProperty("绑定人员ID")
    @NotEmpty(message = "绑定人员ID不能为空")
    private String bindPersonId;

    @ApiModelProperty("bind_person_name")
    @NotEmpty(message = "bind_person_name不能为空")
    private String bindPersonName;

    @ApiModelProperty("绑定时间")
    @NotNull(message = "绑定时间不能为空")
    private Date bindTime;

}
