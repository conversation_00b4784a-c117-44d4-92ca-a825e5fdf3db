package com.rs.module.acp.controller.admin.pi.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-夜间开启监室门新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class NightDoorRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监室ID")
    @NotEmpty(message = "监室ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("开门原因")
    @NotEmpty(message = "开门原因不能为空")
    private String openReason;

    @ApiModelProperty("申请类型(01:开门申请,02:信息补录)")
    private String applyType;

    @ApiModelProperty("申请人身份证号")
    private String applyUserSfzh;

    @ApiModelProperty("申请人")
    private String applyUser;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("申请开门日期")
    private Date applyOpenDoorDate;

    @ApiModelProperty("进入监室民辅警身份证号，多个用逗号分隔")
    @NotEmpty(message = "进入监室民辅警身份证号，多个用逗号分隔不能为空")
    private String entryPoliceSfzh;

    @ApiModelProperty("进入监室民辅警名称，多个用逗号分隔")
    @NotEmpty(message = "进入监室民辅警名称，多个用逗号分隔不能为空")
    private String entryPoliceName;

    @ApiModelProperty("监室外警戒民辅警身份证号，多个用逗号分隔")
    @NotEmpty(message = "监室外警戒民辅警身份证号，多个用逗号分隔不能为空")
    private String guardPoliceSfzh;

    @ApiModelProperty("监室外警戒民辅名称，多个用逗号分隔")
    @NotEmpty(message = "监室外警戒民辅名称，多个用逗号分隔不能为空")
    private String guardPoliceName;

    @ApiModelProperty("实际开门时间")
    private Date actualOpenTime;

    @ApiModelProperty("实际关门时间")
    private Date actualCloseTime;

    @ApiModelProperty("开门时长（分钟）")
    private Integer durationMinutes;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
