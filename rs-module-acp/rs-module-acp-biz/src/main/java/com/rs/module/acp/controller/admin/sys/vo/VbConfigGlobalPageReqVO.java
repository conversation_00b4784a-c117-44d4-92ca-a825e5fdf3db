package com.rs.module.acp.controller.admin.sys.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-语音播报-全局配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VbConfigGlobalPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("播报静音时段，格式：00:00:00-10:00:00")
    private String silentTimeSlots;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
