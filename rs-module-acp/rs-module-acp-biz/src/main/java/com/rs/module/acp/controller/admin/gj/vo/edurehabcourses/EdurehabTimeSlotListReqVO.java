package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalTime;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程时段列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabTimeSlotListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("课程时段编码，如“9:00-10:00”，唯一标识时段")
    private String timeSlotCode;

    @ApiModelProperty("课程时段-开始")
    private String startTime;

    @ApiModelProperty("课程时段-结束")
    private String endTime;

    @ApiModelProperty("时段时长（分钟）")
    private Integer durationMinutes;

    @ApiModelProperty("机构编码")
    private String orgCode;

}
