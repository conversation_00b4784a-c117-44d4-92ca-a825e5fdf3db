package com.rs.module.acp.controller.admin.sys.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-系统基础配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BasicConfigListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("系统名字")
    private String systemName;

    @ApiModelProperty("系统名字缩写")
    private String systemNameShort;

    @ApiModelProperty("系统上线日期")
    private Date[] onlineDate;

    @ApiModelProperty("登录页logo图片")
    private String logoLoginUrl;

    @ApiModelProperty("系统名称图片")
    private String systemNameUrl;

    @ApiModelProperty("版权信息")
    private String versionInfo;

    @ApiModelProperty("系统说明")
    private String systemInfo;

    @ApiModelProperty("登录页logo图片图片名字")
    private String logoLoginUrlName;

    @ApiModelProperty("系统名称图片名字")
    private String systemNameUrlName;

    @ApiModelProperty("1-pki 0-默认")
    private String loginType;

    @ApiModelProperty("公匙")
    private String publicKey;

    @ApiModelProperty("背景图")
    private String backgroundImage;

    @ApiModelProperty("登录页效果 1：警务蓝（默认） 2：科技蓝")
    private String loginPageEffect;

    @ApiModelProperty("是否展示插件列表 0：否 1：是")
    private String isShowPlugin;

    @ApiModelProperty("是否展示浏览器列表 0：否 1：是")
    private String isShowBrowser;

    @ApiModelProperty("是否展示超链接列表 0：否 1：是")
    private String isShowHyperlink;

    @ApiModelProperty("base_system_info_url")
    private String baseSystemInfoUrl;

}
