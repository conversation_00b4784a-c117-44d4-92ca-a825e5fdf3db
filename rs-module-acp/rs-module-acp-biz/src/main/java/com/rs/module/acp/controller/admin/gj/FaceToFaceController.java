package com.rs.module.acp.controller.admin.gj;

import cn.hutool.core.lang.Assert;
import com.rs.framework.common.enums.DataSourceAppEnum;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFacePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceRespVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceSaveReqVO;
import com.rs.module.acp.job.facetoface.FaceToFaceJob;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.FaceToFaceDO;
import com.rs.module.acp.service.gj.face2face.FaceToFaceService;

@Api(tags = "实战平台-管教业务-面对面管理")
@RestController
@RequestMapping("/acp/gj/faceToFace")
@Validated
public class FaceToFaceController {

    @Resource
    private FaceToFaceService faceToFaceService;

    @Autowired
    private FaceToFaceJob faceToFaceJob;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-面对面管理")
    @BusTrace(busType = BusTypeEnum.YEWU_MDMGL, roomId = "{{#createReqVO.roomId}}", content = "{\"检查民警\":\"{{#createReqVO.checkPolice}}\"," +
            "\"检查时间\":\"{{#fDateTime(#createReqVO.checkTime)}}\",\"情况记录\":\"{{#createReqVO.situationRecord}}\"}", businessId = "{{#result.data}}")
    public CommonResult<String> createFaceToFace(@Valid @RequestBody FaceToFaceSaveReqVO createReqVO) {
        Assert.notBlank(createReqVO.getCheckPoliceSfzh(), "检查民警身份证号不能为空");
        createReqVO.setDataSources(DataSourceAppEnum.ACP.getCode());
        return success(faceToFaceService.createFaceToFace(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-面对面管理")
    public CommonResult<Boolean> updateFaceToFace(@Valid @RequestBody FaceToFaceSaveReqVO updateReqVO) {
        faceToFaceService.updateFaceToFace(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-面对面管理")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteFaceToFace(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            faceToFaceService.deleteFaceToFace(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-面对面管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<FaceToFaceRespVO> getFaceToFace(@RequestParam("id") String id) {
        FaceToFaceDO faceToFace = faceToFaceService.getFaceToFace(id);
        return success(BeanUtils.toBean(faceToFace, FaceToFaceRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-面对面管理分页")
    public CommonResult<PageResult<FaceToFaceRespVO>> getFaceToFacePage(@Valid @RequestBody FaceToFacePageReqVO pageReqVO) {
        PageResult<FaceToFaceDO> pageResult = faceToFaceService.getFaceToFacePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FaceToFaceRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-面对面管理列表")
    public CommonResult<List<FaceToFaceRespVO>> getFaceToFaceList(@Valid @RequestBody FaceToFaceListReqVO listReqVO) {
        List<FaceToFaceDO> list = faceToFaceService.getFaceToFaceList(listReqVO);
        return success(BeanUtils.toBean(list, FaceToFaceRespVO.class));
    }


    @GetMapping("/test")
    @ApiOperation(value = "面对面管理定时任务测试")
    public CommonResult<Boolean> test() {
        faceToFaceJob.handleFaceToFace();
        return success(true);
    }

}
