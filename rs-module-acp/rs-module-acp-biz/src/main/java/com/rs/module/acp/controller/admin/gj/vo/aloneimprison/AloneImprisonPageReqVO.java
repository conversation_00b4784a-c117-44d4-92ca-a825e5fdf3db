package com.rs.module.acp.controller.admin.gj.vo.aloneimprison;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-单独关押登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AloneImprisonPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("旧监室id")
    private String oldRoomId;

    @ApiModelProperty("新监室ID")
    private String newRoomId;

    @ApiModelProperty("旧监室名称")
    private String oldRoomName;

    @ApiModelProperty("新监室名称")
    private String newRoomName;

    @ApiModelProperty("单独关押原因")
    private String registerReason;

    @ApiModelProperty("具体原因")
    private String specificReason;

    @ApiModelProperty("开始时间")
    private Date[] startTime;

    @ApiModelProperty("结束时间")
    private Date[] endTime;

    @ApiModelProperty("关入单独监室时间")
    private Date[] inTime;

    @ApiModelProperty("移出单独监室时间")
    private Date[] outTime;

    @ApiModelProperty("报备状态（字典：ZD_XXBB_BBZT）")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("领导签名")
    private String approvalAutograph;

    @ApiModelProperty("领导签名日期")
    private Date[] approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
