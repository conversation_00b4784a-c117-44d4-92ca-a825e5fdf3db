package com.rs.module.acp.controller.admin.zh;

import com.rs.module.acp.controller.admin.zh.vo.CaptchaVO;
import com.rs.module.acp.util.CaptchaUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/captcha")
@RequiredArgsConstructor
public class CaptchaController {

    private final CaptchaUtil captchaUtil;

    // 存储当前验证码答案（生产环境可调整为使用 Redis）
    private String currentAnswer;

    /**
     * 生成验证码
     *
     * 此方法用于生成数学验证码，并将正确答案保存在当前会话中
     * 验证码生成由 captchaUtil 工具类完成，确保验证码的多样性和安全性
     *
     * @return ResponseEntity<CaptchaVO> 返回包含验证码信息的响应实体
     */
    @GetMapping("/generate")
    public ResponseEntity<CaptchaVO> generateCaptcha() {
        // 生成数学验证码
        CaptchaVO captchaVO = captchaUtil.generateMathCaptcha();
        // 保存当前答案，用于后续验证
        currentAnswer = captchaVO.getAnswer();
        // 返回验证码信息
        return ResponseEntity.ok(captchaVO);
    }

    /**
     * 验证用户输入的验证码答案是否正确
     * 
     * @param userAnswer 用户提交的答案
     * @return 返回验证结果（true 为正确，false 为错误）
     */
    @PostMapping("/verify")
    public ResponseEntity<Boolean> verifyAnswer(@RequestParam String userAnswer) {
        // 去除用户输入前后空格后进行比对
        boolean result = currentAnswer != null && currentAnswer.equals(userAnswer.trim());
        return ResponseEntity.ok(result);
    }
}

