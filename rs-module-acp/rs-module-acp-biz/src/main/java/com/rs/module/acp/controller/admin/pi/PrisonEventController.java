package com.rs.module.acp.controller.admin.pi;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.security.util.SessionUserUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.common.exception.ServerException;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.pi.PrisonEventDO;
import com.rs.module.acp.service.pi.PrisonEventService;

@Api(tags = "实战平台-巡视管控-所情登记")
@RestController
@RequestMapping("/acp/pi/prisonEvent")
@Validated
public class PrisonEventController {

    @Resource
    private PrisonEventService prisonEventService;

    @Autowired
    private UserApi userApi;

    @PostMapping("/create")
    @ApiOperation(value = "创建所情登记")
    @LogRecordAnnotation(bizModule = "acp:prisonEvent:create", operateType = LogOperateType.CREATE, title = "实战平台-巡视管控-创建所情登记",
            success = "实战平台-巡视管控-创建所情登记成功", fail = "实战平台-巡视管控-创建所情登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonEvent(@Valid @RequestBody PrisonEventSaveReqVO createReqVO) {
        return success(prisonEventService.createPrisonEvent(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所情登记详情")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PrisonEventRespVO> getPrisonEvent(@RequestParam("id") String id) {
        return success(prisonEventService.getPrisonEventById(id));
    }

    @PostMapping("/getOutsiderPersonPage")
    @ApiOperation(value = "获取外来人员分页")
    @LogRecordAnnotation(bizModule = "acp:prisonEvent:getOutsiderPersonPage", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获取外来人员分页",
            success = "实战平台-巡视管控-获取外来人员分页成功", fail = "实战平台-巡视管控-获取外来人员分页失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrisonEventPersonRespVO>> getOutsiderPersonPage(@Valid @RequestBody PrisonEventPersonPageReqVO pageReqVO) {
        return success(prisonEventService.getOutsiderPersonPage(pageReqVO));
    }

    @GetMapping("/getUserByPostCode")
    @ApiOperation(value = "根据岗位编码获取岗位下的用户")
    @ApiImplicitParam(name = "postCode", value = "岗位编码")
    @LogRecordAnnotation(bizModule = "acp:prisonEvent:getOutsiderPersonPage", operateType = LogOperateType.QUERY,
            title = "实战平台-巡视管控-根据岗位编码获取岗位下的用户",
            success = "实战平台-巡视管控-根据岗位编码获取岗位下的用户成功", fail = "实战平台-巡视管控-根据岗位编码获取岗位下的用户失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#postCode}}")
    public CommonResult<List<UserRespDTO>> getUserByPostCode(@RequestParam("postCode") String postCode) {
        return success(userApi.getUserByOrgAndPost(SessionUserUtil.getSessionUser().getOrgCode(),postCode));
    }

    @GetMapping("/getAllPostCode")
    @ApiOperation(value = "获取全部岗位")
    @ApiImplicitParam(name = "eventTypeId", value = "所情事件类型ID")
    @LogRecordAnnotation(bizModule = "acp:prisonEvent:getAllPostCode", operateType = LogOperateType.QUERY,
            title = "实战平台-巡视管控-获取全部岗位",
            success = "实战平台-巡视管控-获取全部岗位成功", fail = "实战平台-巡视管控-获取全部岗位失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "")
    public CommonResult<List<PrisonEventDisposePostSaveVO>> getAllPostCode(@RequestParam("eventTypeId") String eventTypeId) {
        return success(prisonEventService.getAllPostCode(eventTypeId));
    }

    @GetMapping("/getCurrentNodeByPostCode")
    @ApiOperation(value = "根据当前岗位获取所情处置的节点信息")
    @ApiImplicitParam(name = "eventId", value = "所情事件ID")
    @LogRecordAnnotation(bizModule = "acp:prisonEvent:getAllPostCode", operateType = LogOperateType.QUERY,
            title = "实战平台-巡视管控-根据当前岗位获取所情处置的节点信息",
            success = "实战平台-巡视管控-根据当前岗位获取所情处置的节点信息成功", fail = "实战平台-巡视管控-根据当前岗位获取所情处置的节点信息失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "")
    public CommonResult<PrisonEventHandleRespVO> getCurrentNodeByPostCode(@RequestParam("eventId") String eventId) {
        return success(prisonEventService.getCurrentNodeByPostCode(eventId));
    }

    @GetMapping("/noNeeddisposal")
    @ApiOperation(value = "无需处置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<Boolean> noNeeddisposal(@RequestParam("id") String id) {
        return success(prisonEventService.noNeeddisposal(id));
    }

    @PostMapping("/patrolControlDispose")
    @ApiOperation(value = "巡控处置")
    @LogRecordAnnotation(bizModule = "acp:prisonEvent:patrolControlDispose", operateType = LogOperateType.CREATE, title = "实战平台-巡视管控-巡控处置",
            success = "实战平台-巡视管控-巡控处置成功", fail = "实战平台-巡视管控-巡控处置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> patrolControlDispose(@Valid @RequestBody PrisonEventSaveReqVO createReqVO) {
        if(ObjectUtil.isEmpty(createReqVO.getId())){
            throw new ServerException("所情事件ID不可为空");
        }
        return success(prisonEventService.createPrisonEvent(createReqVO));
    }

    @PostMapping("/prisonEventDispose")
    @ApiOperation(value = "所情处置")
    @LogRecordAnnotation(bizModule = "acp:prisonEvent:prisonEventDispose", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-所情处置",
            success = "实战平台-巡视管控-所情处置成功", fail = "实战平台-巡视管控-所情处置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> prisonEventDispose(@Valid @RequestBody PrisonEventHandleSaveReqVO createReqVO) {
        return success(prisonEventService.prisonEventDispose(createReqVO));
    }

    @PostMapping("/prisonEventApprove")
    @ApiOperation(value = "所领导审批")
    @LogRecordAnnotation(bizModule = "acp:prisonEvent:prisonEventApprove", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-所领导审批",
            success = "实战平台-巡视管控-所领导审批成功", fail = "实战平台-巡视管控-所领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> prisonEventApprove(@Valid @RequestBody PrisonEventHandleApproveSaveReqVO createReqVO) {
        return success(prisonEventService.prisonEventApprove(createReqVO));
    }

}
