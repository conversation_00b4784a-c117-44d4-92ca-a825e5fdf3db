package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.dao.wb.VisitorInfoDao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-对外开放登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VisitorRegRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("来访时间")
    private Date visitTime;
    @ApiModelProperty("业务类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_DWKFYWLX")
    private String businessType;
    @ApiModelProperty("来访事由")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_DWKFLFSY")
    private String visitReason;
    @ApiModelProperty("来访人员类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_DWKFLFRYLX")
    private String visitorType;
    @ApiModelProperty("参观区域")
    private String visitArea;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("来访问人列表")
    private List<VisitorInfoRespVO> personList;
}
