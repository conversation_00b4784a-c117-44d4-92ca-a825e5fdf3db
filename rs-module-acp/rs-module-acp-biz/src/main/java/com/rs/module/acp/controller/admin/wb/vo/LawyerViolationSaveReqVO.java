package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师违规新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerViolationSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("律师表ID")
    @NotEmpty(message = "律师表ID不能为空")
    private String lawyerId;

    @ApiModelProperty("违规时间")
    @NotNull(message = "违规时间不能为空")
    private Date violationTime;

    @ApiModelProperty("违规情况")
    @NotEmpty(message = "违规情况不能为空")
    private String violationType;

    @ApiModelProperty("详细说明")
    @NotEmpty(message = "详细说明不能为空")
    private String description;

}
