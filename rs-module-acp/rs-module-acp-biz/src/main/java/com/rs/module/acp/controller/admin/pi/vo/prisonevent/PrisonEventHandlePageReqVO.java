package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情处置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonEventHandlePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所属警情")
    private String eventId;

    @ApiModelProperty("处置反馈")
    private String feedbackInfo;

    @ApiModelProperty("处置人ID")
    private Integer handleUserSfzh;

    @ApiModelProperty("处置人名称")
    private String handleUserName;

    @ApiModelProperty("处置时间")
    private Date[] handleTime;

    @ApiModelProperty("处置状态 0未办结 1已办结 2不通过 3通过")
    private String status;

    @ApiModelProperty("处置时所处岗位")
    private String handlePost;

    @ApiModelProperty("处置岗位ID")
    private String handlePostCode;

    @ApiModelProperty("历史数据：1是，0最新数据")
    private Integer history;

    @ApiModelProperty("处置类型 1.处置，2审批")
    private Integer handleType;

    @ApiModelProperty("审批内容")
    private String handleInfo;

    @ApiModelProperty("变动内容")
    private String changeContent;

    @ApiModelProperty("附件地址")
    private String attUrl;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
