package com.rs.module.acp.controller.admin.sys.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-语音播报-即时播报对象列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VbConfigCurrentObjectListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("配置Id")
    private String configId;

    @ApiModelProperty("播报监所Id")
    private String vbPrisonId;

    @ApiModelProperty("播报监区Id")
    private String vbAreaId;

    @ApiModelProperty("播报监室Id")
    private String vbRoomId;

}
