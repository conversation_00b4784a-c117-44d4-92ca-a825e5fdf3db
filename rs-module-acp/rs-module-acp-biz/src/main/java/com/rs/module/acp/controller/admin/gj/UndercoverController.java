package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverRecordVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverRespVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverSaveReqVO;
import com.rs.module.acp.entity.gj.UndercoverDO;
import com.rs.module.acp.service.gj.undercover.UndercoverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-信息员管理")
@RestController
@RequestMapping("/acp/gj/undercover")
@Validated
public class UndercoverController {

    @Resource
    private UndercoverService undercoverService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-信息员管理")
    @LogRecordAnnotation(bizModule = "acp:undercover:create", operateType = LogOperateType.CREATE, title = "管教业务-信息员管理-信息创建",
            success = "管教业务-信息员管理-布建申请登记成功", fail = "管教业务-信息员管理-布建申请登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createUndercover(@Valid @RequestBody UndercoverSaveReqVO createReqVO) {
        return success(undercoverService.createUndercover(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-信息员管理")
    @LogRecordAnnotation(bizModule = "acp:undercover:update", operateType = LogOperateType.UPDATE, title = "管教业务-信息员管理-信息编辑",
            success = "管教业务-信息员管理-信息编辑成功", fail = "管教业务-信息员管理-信息编辑失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateUndercover(@Valid @RequestBody UndercoverSaveReqVO updateReqVO) {
        undercoverService.updateUndercover(updateReqVO);
        return success(true);
    }

    @PostMapping("/updateProcessStatus")
    @ApiOperation(value = "管教业务-信息员管理-登记状态和流程修改")
    @LogRecordAnnotation(bizModule = "acp:undercover:updateProcessStatus", operateType = LogOperateType.UPDATE, title = "管教业务-信息员管理-流程状态修改",
            success = "管教业务-信息员管理-流程状态修改成功", fail = "管教业务-信息员管理-流程状态修改失败，错误信息：{{#_ret[msg]}}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "耳目记录ID", required = true, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "登记状态", required = true, paramType = "query"),
            @ApiImplicitParam(name = "actInstId", value = "流程实例ID", required = true, paramType = "query"),
            @ApiImplicitParam(name = "taskId", value = "任务ID", required = true, paramType = "query")
    })
    public CommonResult<Boolean> updateProcessStatus(@NotBlank(message = "耳目记录ID不能为空") @RequestParam(value = "id") String id,
                                                     @NotBlank(message = "状态不能为空") @RequestParam(value = "status") String status,
                                                     @NotBlank(message = "流程实例ID") @RequestParam(value = "actInstId") String actInstId,
                                                     @NotBlank(message = "任务ID") @RequestParam(value = "taskId") String taskId)
    {
        return success(undercoverService.updateProcessStatus(id, status, actInstId,taskId));
    }


    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-信息员管理")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:archiveCover:delete", operateType = LogOperateType.DELETE, title = "管教业务-信息员管理-删除",
            success = "管教业务-信息员管理-删除成功", fail = "管教业务-信息员管理-删除失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteUndercover(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           undercoverService.deleteUndercover(id);
        }
        return success(true);
    }

    @PostMapping("/approve")
    @ApiOperation(value = "领导审批-管教业务-信息员管理")
    @LogRecordAnnotation(bizModule = "acp:undercover:leaderApprove", operateType = LogOperateType.UPDATE, title = "管教业务-信息员管理-领导审批",
            success = "管教业务-信息员管理-领导审批成功", fail = "管教业务-信息员管理-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        undercoverService.leaderApprove(approveReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @ApiOperation(value = "获取信息员管理详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:undercover:get", operateType = LogOperateType.QUERY, title = "管教业务-信息员管理-信息员详情获取",
            bizNo = "{{#id}}", success = "获取信息员管理详情成功", fail = "获取信息员管理详情失败", extraInfo = "{{#id}}")
    public CommonResult<UndercoverRespVO> getUndercover(@RequestParam("id") String id) {
        UndercoverDO undercover = undercoverService.getUndercover(id);
        return success(BeanUtils.toBean(undercover, UndercoverRespVO.class));
    }

    @GetMapping("/getUndercoverRespVOByJgrybm")
    @ApiOperation(value = "获取信息员记录列表信息")
    @LogRecordAnnotation(bizModule = "acp:undercover:jgrybm", operateType = LogOperateType.QUERY, title = "管教业务-信息员管理-获取信息员记录列表",
            bizNo = "{{#jgrybm}}", success = "获取信息员记录列表信息成功", fail = "获取信息员管理详情失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<PageResult<UndercoverRecordVO>> getUndercoverRespVOByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                                    @RequestParam(value = "pageNo" ,defaultValue = "1") Integer pageNo,
                                                                                    @RequestParam(value = "pageSize", defaultValue = "10")  Integer pageSize) {
        PageResult<UndercoverRecordVO> list = undercoverService.getUndercoverRespVOByJgrybm(jgrybm, pageNo, pageSize);
        return success(list);
    }


}
