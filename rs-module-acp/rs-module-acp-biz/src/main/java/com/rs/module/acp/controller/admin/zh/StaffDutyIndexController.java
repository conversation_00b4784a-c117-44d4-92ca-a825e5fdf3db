package com.rs.module.acp.controller.admin.zh;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageCopyDTO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageHeaderVO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageSearchDTO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageVO;
import com.rs.module.base.enums.StaffDutyTypeEnum;
import com.rs.module.base.service.zh.staffduty.StaffDutyIndexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Api(value = "值班首页", tags = "值班首页")
@Slf4j
@RequestMapping("/acp/zh/staffDutyIndex")
@RestController
@Validated
public class StaffDutyIndexController {

    @Autowired
    private StaffDutyIndexService indexService;

    @ApiOperation(value = "值班排版首页列表")
    @GetMapping(value = "/indexListByDutyDate")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<List<DutyManageVO>> indexListByDutyDate(@RequestParam Date startTime, @RequestParam Date endTime) {
        return CommonResult.success(indexService.indexListByDutyDate(startTime, endTime, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
    }

    /*@ApiOperation(value = "所内交接班-值班人员")
    @GetMapping(value = "/getDutyManByPost")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<DutyMansVO> getManOnDutyChange(@RequestParam Date startTime, @RequestParam Date endTime) {

        String tempDateStr = DateUtils.format(startTime, DateUtils.DATE_PATTERN);
        startTime = DateUtils.stringToDate(tempDateStr, DateUtils.DATE_PATTERN);
        List<DutyManageVO> vos = indexService.indexListByDutyDate(startTime, startTime);
        DutyMansVO vo=new DutyMansVO();
        List<DutyManChangeVO> otherList = new ArrayList<>();
        DutyDoctorVO doctorDuty = new DutyDoctorVO();
        if (vos == null || vos.size() == 0) {
            return CommonResult.success(vo);
        }
        DutyManageVO dutyManageVOToday = vos.get(0);
        List<DutyManageHeaderVO> headerListToday = dutyManageVOToday.getHeaderList();
        JSONObject jb = dutyManageVOToday.getPersonList().getJSONObject(0);
        for (DutyManageHeaderVO h : headerListToday) {
            for (DutyManageSubPostVO sub : h.getSubPostList()) {
                DutyManChangeVO headerItem = new DutyManChangeVO();
                if (h.isHasSubPost()) {
                    headerItem.setSubPost(sub.getSubPost());
                }
                headerItem.setPost(h.getPost());
                List<JSONObject> postNameList = new ArrayList<>();//sub.getSubPostTimeVOS().stream().map(DutyManageSubPostTimeVO::getPostKey).collect(Collectors.toList());
                for (DutyManageSubPostTimeVO timeMan : sub.getSubPostTimeVOS()) {
                    JSONArray jsonArray = jb.getJSONArray(timeMan.getPostKey());
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jbb = new JSONObject();
                        String policeName = jsonArray.getJSONObject(i).getString("policeName");
                        String policeType = jsonArray.getJSONObject(i).getString("policeType");
                        String postKey = jsonArray.getJSONObject(i).getString("postKey");
                        jbb.put("policeType", policeType);
                        jbb.put("policeName", policeName);
                        jbb.put("postKey", postKey);
                        postNameList.add(jbb);
                    }
                }
                headerItem.setPersonNames(postNameList);
                headerItem.setSubPostTimeVOS(sub.getSubPostTimeVOS());

                if("10".equals(sub.getDutyPostId())){
                    doctorDuty.getToday().addAll(postNameList);
                }else {
                    otherList.add(headerItem);
                }
            }
        }
        startTime = DateUtils.addDateDays(startTime, -1);//获取前一天
        vos = indexService.indexListByDutyDate(startTime, startTime);
        if (vos != null && vos.size() > 0) {
            DutyManageVO dutyManageVOYesterday = vos.get(0);
            List<DutyManageHeaderVO> headerListYesterday = dutyManageVOYesterday.getHeaderList();
            JSONObject jb1 = dutyManageVOYesterday.getPersonList().getJSONObject(0);
            for (DutyManageHeaderVO h : headerListYesterday) {
                for (DutyManageSubPostVO sub : h.getSubPostList()) {
                    List<JSONObject> postNameList = new ArrayList<>();
                    for (DutyManageSubPostTimeVO timeMan : sub.getSubPostTimeVOS()) {
                        JSONArray jsonArray = jb1.getJSONArray(timeMan.getPostKey());
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jbb = new JSONObject();
                            String policeName = jsonArray.getJSONObject(i).getString("policeName");
                            String policeType = jsonArray.getJSONObject(i).getString("policeType");
                            jbb.put("policeType", policeType);
                            jbb.put("policeName", policeName);
                            postNameList.add(jbb);
                        }
                    }
                    if("10".equals(sub.getDutyPostId())){
                        doctorDuty.getYesterday().addAll(postNameList);
                    }
                }
            }
        }
        vo.setDoctorPost(doctorDuty);
        vo.setOtherPost(otherList);
        return CommonResult.success(vo);
    }*/

    @ApiOperation(value = "管教岗-值班排版首页列表")
    @GetMapping(value = "/getIndexHeaderListByNowDay")
    @ApiImplicitParam(paramType = "query", name = "queryType", value = "查询类型 1查询所有，不传则根据岗位区分", dataType = "Integer")
    public CommonResult<List<DutyManageHeaderVO>> getIndexHeaderListByNowDay(Integer queryType) {
        return CommonResult.success(indexService.getIndexHeaderListByNowDay(queryType, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
    }

    @ApiOperation(value = "值班导出")
    @PostMapping(value = "/exportByDutyDate")
    public void exportByDutyDate(@RequestBody DutyManageSearchDTO manageSearchDTO, HttpServletResponse response) throws IOException {
        indexService.exportByDutyDate(manageSearchDTO.getStartTime(), manageSearchDTO.getEndTime(), StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode(), response);
    }

    @ApiOperation(value = "复制排班-日期过滤")
    @GetMapping(value = "/checkCopyDate")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<List<String>> checkCopyDate(@RequestParam Date startTime, @RequestParam Date endTime) {
        return CommonResult.success(indexService.checkCopyDate(startTime, endTime, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
    }

    @ApiOperation(value = "复制排班-日期校验(下一步校验)")
    @GetMapping(value = "/checkCopyDateNext")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<String> checkCopyDateNext(@RequestParam Date startTime, @RequestParam Date endTime) {
        Integer result = indexService.checkCopyDateNext(startTime, endTime, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode());
        if (result == 1) {
            return CommonResult.success("校验成功");
        } else if (result == -1) {
            return CommonResult.error("您选择的排班记录不符合要求，请重新选择！");
        } else if (result == -2) {
            return CommonResult.error("您选择的排班记录不符合要求，请重新选择！");
        } else {
            return CommonResult.error("系统异常，请稍后再试，或者与管理员反馈");
        }

    }

    @ApiOperation(value = "复制排班-提交是否存在覆盖校验)")
    @GetMapping(value = "/checkHasData")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<Boolean> checkHasData(@RequestParam Date startTime, @RequestParam Date endTime) {
        Boolean result = indexService.checkHasData(startTime, endTime, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode());
        return CommonResult.success(result);

    }

    @ApiOperation(value = "复制排班-确认提交排班")
    @PostMapping(value = "/copyData")
    public CommonResult<String> copyData(@RequestBody DutyManageCopyDTO dutyManageCopyDTO) {
        dutyManageCopyDTO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        Integer result = indexService.copyData(dutyManageCopyDTO, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode());
        if (result == 1) {
            return CommonResult.success("复制成功");
        } else if (result == -1) {
            return CommonResult.error("源排班和目标排班日期长度不一致！");
        } else if (result == -2) {
            return CommonResult.error("目标排班时间必须在今天以后！");
        } else {
            return CommonResult.error("系统异常，请稍后再试，或者与管理员反馈");
        }
    }

}
