package com.rs.module.acp.controller.admin.sys;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigCurrentListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigCurrentPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigCurrentRespVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigCurrentSaveReqVO;
import com.rs.module.acp.entity.sys.VbConfigCurrentDO;
import com.rs.module.acp.service.sys.VbConfigCurrentService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "实战平台-语音播报-即时配置")
@RestController
@RequestMapping("/acp/sys/vbConfigCurrent")
@Validated
public class VbConfigCurrentController {

    @Resource
    private VbConfigCurrentService vbConfigCurrentService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-语音播报-即时配置")
    public CommonResult<String> createVbConfigCurrent(@Valid @RequestBody VbConfigCurrentSaveReqVO createReqVO) {
        return success(vbConfigCurrentService.createVbConfigCurrent(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-语音播报-即时配置")
    public CommonResult<Boolean> updateVbConfigCurrent(@Valid @RequestBody VbConfigCurrentSaveReqVO updateReqVO) {
        vbConfigCurrentService.updateVbConfigCurrent(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-语音播报-即时配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteVbConfigCurrent(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           vbConfigCurrentService.deleteVbConfigCurrent(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-语音播报-即时配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<VbConfigCurrentRespVO> getVbConfigCurrent(@RequestParam("id") String id) {
        VbConfigCurrentDO vbConfigCurrent = vbConfigCurrentService.getVbConfigCurrent(id);
        return success(BeanUtils.toBean(vbConfigCurrent, VbConfigCurrentRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-语音播报-即时配置分页")
    public CommonResult<PageResult<VbConfigCurrentRespVO>> getVbConfigCurrentPage(@Valid @RequestBody VbConfigCurrentPageReqVO pageReqVO) {
        PageResult<VbConfigCurrentDO> pageResult = vbConfigCurrentService.getVbConfigCurrentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VbConfigCurrentRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-语音播报-即时配置列表")
    public CommonResult<List<VbConfigCurrentRespVO>> getVbConfigCurrentList(@Valid @RequestBody VbConfigCurrentListReqVO listReqVO) {
        List<VbConfigCurrentDO> list = vbConfigCurrentService.getVbConfigCurrentList(listReqVO);
        return success(BeanUtils.toBean(list, VbConfigCurrentRespVO.class));
    }
}
