package com.rs.module.acp.controller.admin.wb.vo;
import com.alibaba.fastjson.JSONObject;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-法律帮助申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LegalAssistanceRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("律师姓名")
    @NotEmpty(message = "律师姓名不能为空")
    private String lawyerName;

    @ApiModelProperty("律师证件号码")
    private String lawyerIdNumber;

    @ApiModelProperty("律师执业证号码")
    private String lawyerPracticeLicenseNumber;

    @ApiModelProperty("律师所属单位")
    private String lawyerFirm;

    @ApiModelProperty("申请时间")
    @NotNull(message = "申请时间不能为空")
    private Date applyTime;

    @ApiModelProperty("申请帮助事项")
    @NotEmpty(message = "申请帮助事项不能为空")
    private String assistanceMatter;

    @ApiModelProperty("是否需要办案机关许可(0:不需要，1：需要)")
    private Short isNeedCaseUnitAllowed;

    @ApiModelProperty("办案机关类型(0:法院，1：检察院，2：办案单位)")
    private String caseUnitType;

    @ApiModelProperty("办案机关名称")
    private String caseUnitName;

    @ApiModelProperty("办案机关是否许可（0：否，1：是）")
    private Short isCaseUnitAllowed;

    @ApiModelProperty("办案机关反馈")
    private String caseUnitFeedback;

    @ApiModelProperty("决定时间")
    private Date decisionTime;

    @ApiModelProperty("会见方式（字典：ZD_WB_LSHJFS）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_LSHJFS")
    private String meetingMethod;

    @ApiModelProperty("会见室ID")
    private String meetingRoomId;

    @ApiModelProperty("会见室名称")
    private String meetingRoomName;

    @ApiModelProperty("是否特殊会见（0：否，1：是）")
    private Short isSpecialMeeting;

    @ApiModelProperty("带出监室时间")
    private Date escortingTime;

    @ApiModelProperty("带出检查结果（字典：ZD_WB_JCJG）")
    @NotEmpty(message = "带出检查结果不能为空")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_JCJG")
    private String inspectionResult;

    @ApiModelProperty("会见开始时间")
    @NotNull(message = "会见开始时间不能为空")
    private Date meetingStartTime;

    @ApiModelProperty("会见结束时间")
    @NotNull(message = "会见结束时间不能为空")
    private Date meetingEndTime;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带回民警身份证号")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

    @ApiModelProperty("带回监室时间")
    private Date returnTime;

    @ApiModelProperty("会毕检查结果（字典：ZD_WB_JCJG）")
    @NotEmpty(message = "会毕检查结果不能为空")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_JCJG")
    private String returnInspectionResult;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("律师是否有违规（0：否，1：是）")
    private Short isLawyerViolation;

    @ApiModelProperty("律师是否告知在押人员异常行为（0：否，1：是）")
    private Short isLawyerInformAbnormalBehav;

    @ApiModelProperty("律师违规情况")
    private String lawyerViolationDetails;

    @ApiModelProperty("在押人员异常行为情况")
    private String abnormalBehaviorDetails;

    @ApiModelProperty("法律监督民警（JSON对象，xm:姓名，zjhm:证件号码）")
    private List<JSONObject> supervisingPoliceList;
}
