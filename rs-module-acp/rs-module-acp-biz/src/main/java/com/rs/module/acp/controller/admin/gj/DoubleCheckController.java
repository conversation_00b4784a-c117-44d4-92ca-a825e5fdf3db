package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckRespVO;
import com.rs.module.acp.controller.admin.gj.vo.doublecheck.DoubleCheckSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.DoubleCheckDO;
import com.rs.module.acp.service.gj.doublecheck.DoubleCheckService;

@Api(tags = "实战平台-管教业务-双重检查登记")
@RestController
@RequestMapping("/acp/gj/doubleCheck")
@Validated
public class DoubleCheckController {

    @Resource
    private DoubleCheckService doubleCheckService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-双重检查登记")
    public CommonResult<String> createDoubleCheck(@Valid @RequestBody DoubleCheckSaveReqVO createReqVO) {
        return success(doubleCheckService.createDoubleCheck(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-双重检查登记")
    public CommonResult<Boolean> updateDoubleCheck(@Valid @RequestBody DoubleCheckSaveReqVO updateReqVO) {
        doubleCheckService.updateDoubleCheck(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-双重检查登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDoubleCheck(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           doubleCheckService.deleteDoubleCheck(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-双重检查登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DoubleCheckRespVO> getDoubleCheck(@RequestParam("id") String id) {
        DoubleCheckDO doubleCheck = doubleCheckService.getDoubleCheck(id);
        return success(BeanUtils.toBean(doubleCheck, DoubleCheckRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-双重检查登记分页")
    public CommonResult<PageResult<DoubleCheckRespVO>> getDoubleCheckPage(@Valid @RequestBody DoubleCheckPageReqVO pageReqVO) {
        PageResult<DoubleCheckDO> pageResult = doubleCheckService.getDoubleCheckPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DoubleCheckRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-双重检查登记列表")
    public CommonResult<List<DoubleCheckRespVO>> getDoubleCheckList(@Valid @RequestBody DoubleCheckListReqVO listReqVO) {
        List<DoubleCheckDO> list = doubleCheckService.getDoubleCheckList(listReqVO);
        return success(BeanUtils.toBean(list, DoubleCheckRespVO.class));
    }
}
