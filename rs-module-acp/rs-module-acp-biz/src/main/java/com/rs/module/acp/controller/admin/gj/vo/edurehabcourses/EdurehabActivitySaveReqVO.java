package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复活动新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabActivitySaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("活动时间")
    @NotNull(message = "活动时间不能为空")
    private Date activityTime;

    @ApiModelProperty("康复民警身份证号")
    @NotEmpty(message = "康复民警身份证号不能为空")
    private String rehabPoliceSfzh;

    @ApiModelProperty("康复民警姓名")
    @NotEmpty(message = "康复民警姓名不能为空")
    private String rehabPolice;

    @ApiModelProperty("活动主题 字典：ZD_KFJYHD_HDZT")
    @NotEmpty(message = "活动主题不能为空")
    private String activityTopic;

    @ApiModelProperty("参加人员编码，多个英文逗号分割")
    @NotEmpty(message = "参加人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("参加人员姓名，多个英文逗号分割")
    @NotEmpty(message = "参加人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("参加人数")
    @NotNull(message = "参加人数不能为空")
    private Integer participantCount;

    @ApiModelProperty("活动详情")
    private String activityDetails;

    @ApiModelProperty("附件地址")
    private String attUrl;

}
