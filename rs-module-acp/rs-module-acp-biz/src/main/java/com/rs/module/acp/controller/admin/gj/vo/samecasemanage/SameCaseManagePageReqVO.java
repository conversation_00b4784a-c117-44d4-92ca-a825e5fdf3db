package com.rs.module.acp.controller.admin.gj.vo.samecasemanage;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-同案人员管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SameCaseManagePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("案件编号")
    private String ajbh;

    @ApiModelProperty("同案人监管人员编码")
    private String sameCaseJgrybm;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
