package com.rs.module.acp.controller.admin.gj.vo.punishment;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 处罚登记信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentSaveRegInfoVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id不能为空")
    @NotBlank(message = "id")
    private String id;

    @ApiModelProperty("执行情况")
    private String executeSituation;


    //@ApiModelProperty("执行人身份证号")
    //private String executorSfzh;
    //
    //@ApiModelProperty("执行人身份证号")
    //private String executor;
    //
    //@ApiModelProperty("执行登记时间")
    //private Date executorRegTime;

    //@ApiModelProperty("执行情况")
    //private String executeSituation;
    //
    //@ApiModelProperty("是否提前解除")
    //private Short isInAdvanceRemove;
    //
    //@ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    //private String status;
    //
    //@ApiModelProperty("审批人身份证号")
    //private String approverSfzh;
    //
    //@ApiModelProperty("审批人姓名")
    //private String approverXm;
    //
    //@ApiModelProperty("审批时间")
    //private Date approverTime;
    //
    //@ApiModelProperty("审批结果")
    //private String approvalResult;
    //
    //@ApiModelProperty("审批人签名")
    //private String approvalAutograph;
    //
    //@ApiModelProperty("审批人签名日期")
    //private Date approvalAutographTime;
    //
    //@ApiModelProperty("审核意见")
    //private String approvalComments;
    //
    //@ApiModelProperty("ACT流程实例Id")
    //private String actInstId;
    //
    //@ApiModelProperty("任务ID")
    //private String taskId;

}
