package com.rs.module.acp.controller.admin.gj.vo.inoutrecords;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-出入登记 Response VO")
@Data
public class InOutRecordsBusinessRespVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("ID")
    private String id;
    @ApiModelProperty("业务ID")
    private String businessId;

    @ApiModelProperty("业务类型（字典：ZD_GJXZDCSY）")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJXZDCSY")
    private String businessType;
    @ApiModelProperty("业务类型子类型")
    private String businessSubType;
}
