package com.rs.module.acp.controller.admin.pi;

import com.bsp.common.util.StringUtil;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pi.vo.NightDoorApproveReqVO;
import com.rs.module.acp.controller.admin.pi.vo.NightDoorRecordRespVO;
import com.rs.module.acp.controller.admin.pi.vo.NightDoorRecordSaveReqVO;
import com.rs.module.acp.entity.pi.NightDoorRecordDO;
import com.rs.module.acp.service.pi.NightDoorRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "夜间开门")
@RestController
@RequestMapping("/acp/pi/nightDoorRecord")
@Validated
public class NightDoorRecordController {

    @Resource
    private NightDoorRecordService nightDoorRecordService;

    @PostMapping("/create")
    @ApiOperation(value = "创建夜间开门申请")
    public CommonResult<String> createNightDoorRecord(@Valid @RequestBody NightDoorRecordSaveReqVO createReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        createReqVO.setApplyUser(sessionUser.getName());
        createReqVO.setApplyUserSfzh(sessionUser.getIdCard());
        createReqVO.setApplyTime(new Date());
        return success(nightDoorRecordService.createNightDoorRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改夜间开门申请")
    public CommonResult<String> updateNightDoorRecord(@Valid @RequestBody NightDoorRecordSaveReqVO createReqVO) {
        return success(nightDoorRecordService.updateNightDoorRecord(createReqVO));
    }

//    @PostMapping("/supplement")
//    @ApiOperation(value = "补录夜间开门信息")
//    public CommonResult supplement(@Valid @RequestBody NightDoorSupplementReqVO supplementVO) {
//        NightDoorRecordDO recordDO = BeanUtils.toBean(supplementVO, NightDoorRecordDO.class);
//        return success(nightDoorRecordService.updateById(recordDO));
//    }

//    @PostMapping("/approve")
//    @ApiOperation(value = "夜间开门审批")
//    public CommonResult approve(@Valid @RequestBody NightDoorApproveReqVO approveVO) {
//        NightDoorRecordDO recordDO = BeanUtils.toBean(approveVO, NightDoorRecordDO.class);
//        return success(nightDoorRecordService.updateById(recordDO));
//    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除夜间开门")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteNightDoorRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           nightDoorRecordService.deleteNightDoorRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得夜间开门信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<NightDoorRecordRespVO> getNightDoorRecord(@RequestParam("id") String id) {
        NightDoorRecordDO nightDoorRecord = nightDoorRecordService.getNightDoorRecord(id);
        return success(BeanUtils.toBean(nightDoorRecord, NightDoorRecordRespVO.class));
    }

}
