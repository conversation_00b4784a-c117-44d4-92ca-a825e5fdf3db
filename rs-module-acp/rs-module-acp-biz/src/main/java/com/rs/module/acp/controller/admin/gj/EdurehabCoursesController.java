package com.rs.module.acp.controller.admin.gj;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRespVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesSaveReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.EdurehabCoursesDO;
import com.rs.module.acp.service.gj.edurehabcourses.EdurehabCoursesService;

@Api(tags = "实战平台-管教业务-教育康复课程")
@RestController
@RequestMapping("/acp/gj/edurehabCourses")
@Validated
public class EdurehabCoursesController {

    @Resource
    private EdurehabCoursesService edurehabCoursesService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-教育康复课程")
    public CommonResult<String> createEdurehabCourses(@Valid @RequestBody EdurehabCoursesSaveReqVO createReqVO) {
        return success(edurehabCoursesService.createEdurehabCourses(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-教育康复课程")
    public CommonResult<Boolean> updateEdurehabCourses(@Valid @RequestBody EdurehabCoursesSaveReqVO updateReqVO) {
        edurehabCoursesService.updateEdurehabCourses(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-教育康复课程")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEdurehabCourses(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            edurehabCoursesService.deleteEdurehabCourses(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EdurehabCoursesRespVO> getEdurehabCourses(@RequestParam("id") String id) {
        EdurehabCoursesDO edurehabCourses = edurehabCoursesService.getEdurehabCourses(id);
        return success(BeanUtils.toBean(edurehabCourses, EdurehabCoursesRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程分页")
    public CommonResult<PageResult<EdurehabCoursesRespVO>> getEdurehabCoursesPage(@Valid @RequestBody EdurehabCoursesPageReqVO pageReqVO) {
        PageResult<EdurehabCoursesDO> pageResult = edurehabCoursesService.getEdurehabCoursesPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EdurehabCoursesRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程列表")
    public CommonResult<List<EdurehabCoursesRespVO>> getEdurehabCoursesList(@Valid @RequestBody EdurehabCoursesListReqVO listReqVO) {
        if (StringUtils.isEmpty(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        if(Objects.isNull(listReqVO.getIsEnabled())){
            listReqVO.setIsEnabled((short)1);
        }
        List<EdurehabCoursesDO> list = edurehabCoursesService.getEdurehabCoursesList(listReqVO);
        return success(BeanUtils.toBean(list, EdurehabCoursesRespVO.class));
    }
}
