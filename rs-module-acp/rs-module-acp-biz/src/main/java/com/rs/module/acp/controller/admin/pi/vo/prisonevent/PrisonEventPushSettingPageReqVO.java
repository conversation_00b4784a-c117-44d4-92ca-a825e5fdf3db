package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件推送设置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonEventPushSettingPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("处置业务（支持多个用逗号分隔）")
    private String disposeBusiness;

    @ApiModelProperty("处置预案")
    private String disposePlans;

    @ApiModelProperty("所情事件类型ID")
    private String typeId;

    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("推送岗位ID")
    private String pushPostId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
