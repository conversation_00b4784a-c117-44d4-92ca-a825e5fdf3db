package com.rs.module.acp.controller.admin.sys.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-语音播报-即时配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VbConfigCurrentPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("播报名称")
    private String vbName;

    @ApiModelProperty("播报内容")
    private String content;

    @ApiModelProperty("播报次数")
    private Short vbNum;

    @ApiModelProperty("优先级")
    private Short priority;

    @ApiModelProperty("播报开始时间")
    private Date[] startTime;

    @ApiModelProperty("播报结束时间")
    private Date[] endTime;

    @ApiModelProperty("是否启用(0否1是)")
    private Short isEnabled;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
   
   @ApiModelProperty("播报区域")
   private String vbArea;
}
