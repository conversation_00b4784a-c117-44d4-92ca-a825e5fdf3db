package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件在押人员新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventPrisonerSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所情事件ID")
    @NotEmpty(message = "所情事件ID不能为空")
    private String eventId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("扣分值")
    private Integer deductPoint;

    @ApiModelProperty("应扣分值")
    private Integer shouldDeductPoint;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

}
