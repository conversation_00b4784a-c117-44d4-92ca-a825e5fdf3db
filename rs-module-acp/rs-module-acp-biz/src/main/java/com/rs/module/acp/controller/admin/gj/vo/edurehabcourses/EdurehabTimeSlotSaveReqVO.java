package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.time.LocalTime;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程时段新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabTimeSlotSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("课程时段编码，如“9:00-10:00”，唯一标识时段")
    private String timeSlotCode;

    @ApiModelProperty("课程时段-开始")
    @NotNull(message = "课程时段-开始不能为空")
    private String startTime;

    @ApiModelProperty("课程时段-结束")
    @NotNull(message = "课程时段-结束不能为空")
    private String endTime;

    @ApiModelProperty("时段时长（分钟）")
    private Integer durationMinutes;

}
