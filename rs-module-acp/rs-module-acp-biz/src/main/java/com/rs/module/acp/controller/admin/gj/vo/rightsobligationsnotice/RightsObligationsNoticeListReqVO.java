package com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-权力义务告知书列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RightsObligationsNoticeListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("签名url")
    private String signUrl;

    @ApiModelProperty("签名时间")
    private Date[] signTime;

    @ApiModelProperty("捺印url")
    private String fingerprintUrl;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

}
