package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailRespVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.CivilizedPersonneDetailSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.CivilizedPersonneDetailDO;
import com.rs.module.acp.service.gj.civilizedpersonne.CivilizedPersonneDetailService;

@Api(tags = "实战平台-管教业务-文明个人登记明细")
@RestController
@RequestMapping("/acp/gj/civilizedPersonneDetail")
@Validated
public class CivilizedPersonneDetailController {

    @Resource
    private CivilizedPersonneDetailService civilizedPersonneDetailService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-文明个人登记明细")
    public CommonResult<String> createCivilizedPersonneDetail(@Valid @RequestBody CivilizedPersonneDetailSaveReqVO createReqVO) {
        return success(civilizedPersonneDetailService.createCivilizedPersonneDetail(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-文明个人登记明细")
    public CommonResult<Boolean> updateCivilizedPersonneDetail(@Valid @RequestBody CivilizedPersonneDetailSaveReqVO updateReqVO) {
        civilizedPersonneDetailService.updateCivilizedPersonneDetail(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-文明个人登记明细")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCivilizedPersonneDetail(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           civilizedPersonneDetailService.deleteCivilizedPersonneDetail(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-文明个人登记明细")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CivilizedPersonneDetailRespVO> getCivilizedPersonneDetail(@RequestParam("id") String id) {
        CivilizedPersonneDetailDO civilizedPersonneDetail = civilizedPersonneDetailService.getCivilizedPersonneDetail(id);
        return success(BeanUtils.toBean(civilizedPersonneDetail, CivilizedPersonneDetailRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-文明个人登记明细分页")
    public CommonResult<PageResult<CivilizedPersonneDetailRespVO>> getCivilizedPersonneDetailPage(@Valid @RequestBody CivilizedPersonneDetailPageReqVO pageReqVO) {
        PageResult<CivilizedPersonneDetailDO> pageResult = civilizedPersonneDetailService.getCivilizedPersonneDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CivilizedPersonneDetailRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-文明个人登记明细列表")
    public CommonResult<List<CivilizedPersonneDetailRespVO>> getCivilizedPersonneDetailList(@Valid @RequestBody CivilizedPersonneDetailListReqVO listReqVO) {
        List<CivilizedPersonneDetailDO> list = civilizedPersonneDetailService.getCivilizedPersonneDetailList(listReqVO);
        return success(BeanUtils.toBean(list, CivilizedPersonneDetailRespVO.class));
    }
}
