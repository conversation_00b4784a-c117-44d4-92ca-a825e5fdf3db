package com.rs.module.acp.controller.admin.config;

import com.rs.framework.common.pojo.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

import static com.rs.framework.common.pojo.CommonResult.success;

/**
 *
 * <AUTHOR>
 * @Date 2025/5/9 18:58
 */
@Api(tags = "实战平台-系统配置")
@RestController
@RequestMapping("/base/systemConfig")
public class SystemConfigController {


    @GetMapping("/getNow")
    @ApiOperation(value = "获取系统时间", notes = "获取系统时间")
    public CommonResult<Date> getNow(){
        return success(new Date());
    }

}
