package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 综合管理-绩效考核截止日期设置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorConfigSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("截止日期类型，01：本月，02：下月 字典：ZD_JXKH_JZRQLX ")
    @NotEmpty(message = "截止日期类型，01：本月，02：下月不能为空")
    private String expiryDateType;

    @ApiModelProperty("间隔天数")
    @NotNull(message = "间隔天数不能为空")
    private Integer intervalDays;

}
