package com.rs.module.acp.controller.admin.gj.vo.performancekss;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-人员表现鉴定表-看守所 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceKssRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("接收单位")
    private String jsdw;
    @ApiModelProperty("移送因由")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PERFORMANCE_YSYY")
    private String ysyy;
    @ApiModelProperty("其他情况")
    private String qtqk;
    @ApiModelProperty("身体状况")
    private String healthStzk;
    @ApiModelProperty("外伤情况")
    private String healthWsqk;
    @ApiModelProperty("重大疾病")
    private String healthZdjb;
    @ApiModelProperty("精神状况")
    private String healthJszk;
    @ApiModelProperty("重大疾病及出所住院病因")
    private String healthZdjbjcszyyy;
    @ApiModelProperty("所规所纪制度")
    private String performanceSgsjzd;
    @ApiModelProperty("一日生活管理")
    private String performanceYrshgl;
    @ApiModelProperty("自伤自残行为或倾向")
    private String performanceZszcxwhqx;
    @ApiModelProperty("殴打他人行为")
    private String performanceOdtrxw;
    @ApiModelProperty("曾列为重大安全风险情况-一般")
    private String performanceZdaqfxqk1;
    @ApiModelProperty("曾列为重大安全风险情况-三级")
    private String performanceZdaqfxqk2;
    @ApiModelProperty("曾列为重大安全风险情况-二级")
    private String performanceZdaqfxqk3;
    @ApiModelProperty("曾列为重大安全风险情况-一级")
    private String performanceZdaqfxqk4;
    @ApiModelProperty("家属姓名及电话")
    private String familyXmjdh;
    @ApiModelProperty("羁押期间联系家属情况")
    private String familyJyqjlxjsqk;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PERFORMANCE_STATUS")
    private String status;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
