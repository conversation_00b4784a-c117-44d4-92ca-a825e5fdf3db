package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所警情管理-报警联动设置(所情来源)新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventSettingSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所情来源 字典：ZD_JJKS_SQLY")
    @NotEmpty(message = "所情来不能为空")
    private String eventSrc;

    @ApiModelProperty("所情来源-中文")
    private String name;

    @ApiModelProperty("所情等级 字典：ZD_JJKS_SQDJ")
    @NotNull(message = "所情等级不能为空")
    private Short eventLevel;

    @ApiModelProperty("是否启用 1启用 0禁用")
    @NotNull(message = "是否启用 1启用 0禁用不能为空")
    private Short enabled;

    @ApiModelProperty("处理时效（分钟）")
    private Short processingDuration;

    @ApiModelProperty("提示音 字典：ZD_JJKS_SQYJTSY")
    private String tone;

    @ApiModelProperty("可选择的设置。逗号分隔，字典：ZD_LDPZ")
    private String optionalSettings;

    @ApiModelProperty("联动配置。逗号分隔。optional_settings")
    private String settings;

    @ApiModelProperty("扩展字段1")
    private String extendOne;
}
