package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentRespVO;
import com.rs.module.acp.controller.admin.gj.vo.printdocument.PrintDocumentSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.PrintDocumentDO;
import com.rs.module.acp.service.gj.printdocument.PrintDocumentService;

@Api(tags = "实战平台-管教业务-文书打印预览")
@RestController
@RequestMapping("/acp/gj/printDocument")
@Validated
public class PrintDocumentController {

    @Resource
    private PrintDocumentService printDocumentService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-文书打印预览")
    public CommonResult<String> createPrintDocument(@Valid @RequestBody PrintDocumentSaveReqVO createReqVO) {
        return success(printDocumentService.createPrintDocument(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-文书打印预览")
    public CommonResult<Boolean> updatePrintDocument(@Valid @RequestBody PrintDocumentSaveReqVO updateReqVO) {
        printDocumentService.updatePrintDocument(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-文书打印预览")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePrintDocument(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           printDocumentService.deletePrintDocument(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-文书打印预览")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PrintDocumentRespVO> getPrintDocument(@RequestParam("id") String id) {
        PrintDocumentDO printDocument = printDocumentService.getPrintDocument(id);
        return success(BeanUtils.toBean(printDocument, PrintDocumentRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-文书打印预览分页")
    public CommonResult<PageResult<PrintDocumentRespVO>> getPrintDocumentPage(@Valid @RequestBody PrintDocumentPageReqVO pageReqVO) {
        PageResult<PrintDocumentDO> pageResult = printDocumentService.getPrintDocumentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrintDocumentRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-文书打印预览列表")
    public CommonResult<List<PrintDocumentRespVO>> getPrintDocumentList(@Valid @RequestBody PrintDocumentListReqVO listReqVO) {
        List<PrintDocumentDO> list = printDocumentService.getPrintDocumentList(listReqVO);
        return success(BeanUtils.toBean(list, PrintDocumentRespVO.class));
    }
}
