package com.rs.module.acp.controller.admin.gj.vo.performancejls;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-人员表现鉴定表-看守所新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceJssApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("状态 字典：ZD_PERFORMANCE_STATUS  02：通过  03：不通过")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("审核意见")
    private String approvalComments;



}
