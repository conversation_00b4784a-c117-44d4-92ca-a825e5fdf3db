package com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-诊断评估(戒毒所) Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DiagnosisAssmtJdsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("待评估类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PGZD_PGBLX")
    private String assmtType;
    @ApiModelProperty("评估周期")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PGZD_PGZQ")
    private String assmtPeriod;
    @ApiModelProperty("评估周期开始时间")
    private Date startTime;
    @ApiModelProperty("评估周期结束时间")
    private Date endTime;
    @ApiModelProperty("推送时间")
    private Date pushTime;
    @ApiModelProperty("推送岗位，多个逗号分割")
    private String pushJobPositions;
    @ApiModelProperty("评估内容值1")
    private String assmtContentValue1;
    @ApiModelProperty("评估内容值2")
    private String assmtContentValue2;
    @ApiModelProperty("评估内容值3")
    private String assmtContentValue3;
    @ApiModelProperty("评估内容值4")
    private String assmtContentValue4;
    @ApiModelProperty("评估内容值5")
    private String assmtContentValue5;
    @ApiModelProperty("评估内容值6")
    private String assmtContentValue6;
    @ApiModelProperty("评估内容值7")
    private String assmtContentValue7;
    @ApiModelProperty("评估内容值8")
    private String assmtContentValue8;
    @ApiModelProperty("评估内容值9")
    private String assmtContentValue9;
    @ApiModelProperty("评估内容值10")
    private String assmtContentValue10;
    @ApiModelProperty("评估内容值10")
    private String assmtContentTotalScore;

    @ApiModelProperty("评估结果")
    private String assmtResultValue;

    @ApiModelProperty("评估结果1")
    private String assmtResultValue1;
    @ApiModelProperty("评估结果2")
    private String assmtResultValue2;
    @ApiModelProperty("评估结果3")
    private String assmtResultValue3;
    @ApiModelProperty("评估结果4")
    private String assmtResultValue4;
    @ApiModelProperty("评估结果5")
    private String assmtResultValue5;
    @ApiModelProperty("评估内容JSON")
    private String asstmContentJson;
    @ApiModelProperty("评估结果JSON")
    private String assmtResultJson;
    @ApiModelProperty("评估人身份证号")
    private String assmtUserSfzh;
    @ApiModelProperty("评估人姓名")
    private String assmtUser;
    @ApiModelProperty("评估时间")
    private String assmtTime;
    @ApiModelProperty("办理状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PGZD_PGZT")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
}
