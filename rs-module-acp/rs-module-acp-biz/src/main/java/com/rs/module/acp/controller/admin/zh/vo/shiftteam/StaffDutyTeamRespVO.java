package com.rs.module.acp.controller.admin.zh.vo.shiftteam;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 值班模板班组信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class StaffDutyTeamRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("关联的班次ID")
    private String shiftId;
    @ApiModelProperty("班组名称")
    private String teamName;
    @ApiModelProperty("班组顺序")
    private Integer teamOrder;
}
