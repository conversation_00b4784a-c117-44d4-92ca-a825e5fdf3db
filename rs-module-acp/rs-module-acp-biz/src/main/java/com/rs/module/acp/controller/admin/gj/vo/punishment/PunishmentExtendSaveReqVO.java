package com.rs.module.acp.controller.admin.gj.vo.punishment;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 实战平台-管教业务延长处罚呈批新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentExtendSaveReqVO extends BaseVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @NotEmpty(message = "处罚呈批ID不能为空")
    private String id;

    @ApiModelProperty("延长理由")
    @NotEmpty(message = "延长理由不能为空")
    private String reason;

    @ApiModelProperty("延长天数")
    @NotNull(message = "延长天数不能为空")
    private Integer extendDay;


}
