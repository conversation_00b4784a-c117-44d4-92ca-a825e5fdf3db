package com.rs.module.acp.controller.admin.wb;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.cache.DicUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.entity.wb.ConsularMeetingPersonDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.ConsularMeetingPersonService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import java.util.stream.Collectors;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.ConsularMeetingDO;
import com.rs.module.acp.service.wb.ConsularMeetingService;

@Api(tags = "实战平台-窗口业务-领事会见登记")
@RestController
@RequestMapping("/acp/wb/consularMeeting")
@Validated
public class ConsularMeetingController {

    @Resource
    private ConsularMeetingService consularMeetingService;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private ConsularMeetingPersonService consularMeetingPersonService;

    @GetMapping("/verificationPersonnel")
    @ApiOperation(value = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:verificationPersonnel", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中",
            success = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中成功",
            fail = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<JSONObject> verificationPersonnel(@RequestParam("jgrybm") String jgrybm) {
        return success(wbCommonService.verificationPersonnel(jgrybm,WbConstants.BUSINESS_TYPE_CONSULAR_MEETING));
    }


    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建领事会见登记")
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建领事会见登记",
            success = "实战平台-窗口业务-创建领事会见登记成功", fail = "实战平台-窗口业务-创建领事会见登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createConsularMeeting(@Valid @RequestBody ConsularMeetingSaveReqVO createReqVO) {
        return success(consularMeetingService.createConsularMeeting(createReqVO));
    }

    @GetMapping("/signIn")
    @ApiOperation(value = "实战平台-窗口业务-领事会见签到")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "checkInTime", value = "签到时间", required = true, dataType = "String"),
    })
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:signIn", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-领事会见签到",
            success = "实战平台-窗口业务-领事会见签到成功", fail = "实战平台-窗口业务-领事会见签到失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> signIn(@RequestParam(value = "id", required = true) String id,
                                        @RequestParam(value = "checkInTime", required = true) String checkInTime) {
        if (consularMeetingService.signIn(id, checkInTime)) {
            return success();
        }
        return error("签到失败");
    }

    @GetMapping("/getIdleLawyerMeetingRoom")
    @ApiOperation(value = "实战平台-窗口业务-获取空闲会见室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:getIdleLawyerMeetingRoom", operateType = LogOperateType.UPDATE,
            title = "实战平台-窗口业务-获取空闲会见室",success = "实战平台-窗口业务-获取空闲会见室成功",
            fail = "实战平台-窗口业务-获取空闲会见室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<List<JSONObject>> getIdleInterrogationRoom(@RequestParam(value = "id", required = true) String id) {
        return success(wbCommonService.getIdleLawyerMeetingRoom(id, WbConstants.BUSINESS_TYPE_CONSULAR_MEETING));
    }

    @GetMapping("/allocationRoom")
    @ApiOperation(value = "实战平台-窗口业务-分配会见室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "roomId", value = "会见室ID", required = true, dataType = "String"),
    })
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:allocationRoom", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-分配会见室",
            success = "实战平台-窗口业务-分配会见室成功", fail = "实战平台-窗口业务-分配会见室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> allocationRoom(@RequestParam(value = "id", required = true) String id,
                                                @RequestParam(value = "roomId", required = true) String roomId) {
        if (consularMeetingService.allocationRoom(id, roomId)) {
            return success();
        }
        return error("分配会见室失败");
    }

    @PostMapping("/escortingInspect")
    @ApiOperation(value = "实战平台-窗口业务-带出安检")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "escortingPolice", value = "带出民警姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPoliceSfzh", value = "带出民警身份证号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingTime", value = "带出监室时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectionResult", value = "检查结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "prohibitedItems", value = "违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "physicalExam", value = "体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "abnormalSituations", value = "异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "inspectionTime", value = "检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectorSfzh", value = "检查人证件号码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspector", value = "检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingStartTime", value = "会见开始时间", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:escortingInspect", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-带出安检",
            success = "实战平台-窗口业务-带出安检成功", fail = "实战平台-窗口业务-带出安检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> escortingInspect(@RequestBody ConsularMeetingSaveReqVO updateReqVO) {
        if (consularMeetingService.escortingInspect(updateReqVO)) {
            return success();
        }
        return error("保存带出安检失败");
    }

    @PostMapping("/returnInspection")
    @ApiOperation(value = "实战平台-窗口业务-会毕安检")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "meetingEndTime", value = "会见结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectorSfzh", value = "会毕检查人身份证", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspector", value = "会毕检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionTime", value = "会毕检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionResult", value = "会毕检查结果", dataType = "String"),
            @ApiImplicitParam(name = "returnProhibitedItems", value = "带回违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "returnPhysicalExam", value = "带回体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "returnAbnormalSituations", value = "带回异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "returnTime", value = "带回监室时间", dataType = "String"),
            @ApiImplicitParam(name = "returnPolice", value = "带回民警姓名", dataType = "String"),
            @ApiImplicitParam(name = "returnPoliceSfzh", value = "带回民警身份证号", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:returnInspection", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-会毕安检",
            success = "实战平台-窗口业务-会毕安检成功", fail = "实战平台-窗口业务-会毕安检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_LINGSHI,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"经办时间\":\"{{#updateReqVO.addTime}}\"," +
            "\"会见单位\":\"{{#updateReqVO.workUnit}}\",\"会见开始时间\":\"{{#fDateTime(#updateReqVO.meetingStartTime)}}\"," +
            "\"会见结束时间\":\"{{#fDateTime(#updateReqVO.meetingEndTime)}}\"}")
    public CommonResult<Boolean> returnInspection(@RequestBody ConsularMeetingSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        ConsularMeetingDO consularMeetingDO = consularMeetingService.getById(updateReqVO.getId());
        LambdaQueryWrapper<ConsularMeetingPersonDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(ConsularMeetingPersonDO::getWorkUnit).eq(ConsularMeetingPersonDO::getConsularMeetingId,updateReqVO.getId());
        List<ConsularMeetingPersonDO> meetingPersonDOList = consularMeetingPersonService.list(lambdaQueryWrapper);
        List<String> workUnitList = meetingPersonDOList.stream().map(ConsularMeetingPersonDO::getWorkUnit).collect(Collectors.toList());
        updateReqVO.setAddTime(DateUtil.format(consularMeetingDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        updateReqVO.setWorkUnit(String.join("、",workUnitList));
        updateReqVO.setJgrybm(consularMeetingDO.getJgrybm());
        updateReqVO.setMeetingStartTime(consularMeetingDO.getMeetingStartTime());
        /****记录轨迹 end****/

        if (consularMeetingService.returnInspection(updateReqVO)) {
            return success();
        }
        return error("保存会毕安检失败");
    }

    @PostMapping("/additionalRecording")
    @ApiOperation(value = "实战平台-窗口业务-补录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "checkInTime", value = "签到时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "room_id", value = "审讯室ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPolice", value = "带出民警姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPoliceSfzh", value = "带出民警身份证号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingTime", value = "带出监室时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectionResult", value = "检查结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "prohibitedItems", value = "违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "physicalExam", value = "体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "abnormalSituations", value = "异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "inspectionTime", value = "检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectorSfzh", value = "检查人证件号码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspector", value = "检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingStartTime", value = "提讯开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingEndTime", value = "提讯结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectorSfzh", value = "会毕检查人身份证", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspector", value = "会毕检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionTime", value = "会毕检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionResult", value = "会毕检查结果", dataType = "String"),
            @ApiImplicitParam(name = "returnProhibitedItems", value = "带回违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "returnPhysicalExam", value = "带回体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "returnAbnormalSituations", value = "带回异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "returnTime", value = "带回监室时间", dataType = "String"),
            @ApiImplicitParam(name = "returnPolice", value = "带回民警姓名", dataType = "String"),
            @ApiImplicitParam(name = "returnPoliceSfzh", value = "带回民警身份证号", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:additionalRecording", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-补录",
            success = "实战平台-窗口业务-补录成功", fail = "实战平台-窗口业务-补录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_LINGSHI,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"经办时间\":\"{{#updateReqVO.addTime}}\"," +
            "\"会见单位\":\"{{#updateReqVO.workUnit}}\",\"会见开始时间\":\"{{#fDateTime(#updateReqVO.meetingStartTime)}}\"," +
            "\"会见结束时间\":\"{{#fDateTime(#updateReqVO.meetingEndTime)}}\"}")
    public CommonResult<Boolean> additionalRecording(@RequestBody ConsularMeetingSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        ConsularMeetingDO consularMeetingDO = consularMeetingService.getById(updateReqVO.getId());
        LambdaQueryWrapper<ConsularMeetingPersonDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(ConsularMeetingPersonDO::getWorkUnit).eq(ConsularMeetingPersonDO::getConsularMeetingId,updateReqVO.getId());
        List<ConsularMeetingPersonDO> meetingPersonDOList = consularMeetingPersonService.list(lambdaQueryWrapper);
        List<String> workUnitList = meetingPersonDOList.stream().map(ConsularMeetingPersonDO::getWorkUnit).collect(Collectors.toList());
        updateReqVO.setAddTime(DateUtil.format(consularMeetingDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        updateReqVO.setWorkUnit(String.join("、",workUnitList));
        updateReqVO.setJgrybm(consularMeetingDO.getJgrybm());
        /****记录轨迹 end****/
        if (consularMeetingService.additionalRecording(updateReqVO)) {
            return success();
        }
        return error("保存补录失败");
    }

    @GetMapping("/getHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录",
            success = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录成功", fail = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<ConsularMeetingRespVO>> getHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                                   @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                                   @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(consularMeetingService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize));
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得领事会见登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得领事会见登记",
            success = "实战平台-窗口业务-获得领事会见登记成功", fail = "实战平台-窗口业务-获得领事会见登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<ConsularMeetingRespVO> getConsularMeeting(@RequestParam("id") String id) {
        return success(consularMeetingService.getConsularMeetingById(id));
    }

    @GetMapping("/getTrajectory")
    @ApiOperation(value = "实战平台-窗口业务-获得领事会见轨迹")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:getTrajectory", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得领事会见轨迹",
            success = "实战平台-窗口业务-获得领事会见轨迹成功", fail = "实战平台-窗口业务-获得领事会见轨迹失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<List<JSONObject>> getTrajectory(@RequestParam("id") String id) {
        return success(wbCommonService.getTrajectory(id,WbConstants.BUSINESS_TYPE_CONSULAR_MEETING));
    }

    @GetMapping("/getNewHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:consularMeeting:getNewHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录",
            success = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录成功",
            fail = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<JSONObject>> getNewHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                             @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(wbCommonService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize, WbConstants.BUSINESS_TYPE_CONSULAR_MEETING));
    }
}
