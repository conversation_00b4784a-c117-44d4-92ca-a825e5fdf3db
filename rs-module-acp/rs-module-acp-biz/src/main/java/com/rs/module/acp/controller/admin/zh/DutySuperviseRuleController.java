package com.rs.module.acp.controller.admin.zh;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.zh.vo.*;
import com.rs.module.acp.entity.zh.DutySuperviseRuleDO;
import com.rs.module.acp.service.zh.DutySuperviseRuleService;

@Api(tags = "综合管理-值班管理-值班督导规则配置")
@RestController
@RequestMapping("/acp/zh/dutySuperviseRule")
@Validated
public class DutySuperviseRuleController {

    @Resource
    private DutySuperviseRuleService dutySuperviseRuleService;

//    @PostMapping("/create")
//    @ApiOperation(value = "创建综合管理-值班管理-值班督导规则配置")
//    public CommonResult<String> createDutySuperviseRule(@Valid @RequestBody DutySuperviseRuleSaveReqVO createReqVO) {
//        return success(dutySuperviseRuleService.createDutySuperviseRule(createReqVO));
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "更新综合管理-值班管理-值班督导规则配置")
//    public CommonResult<Boolean> updateDutySuperviseRule(@Valid @RequestBody DutySuperviseRuleSaveReqVO updateReqVO) {
//        dutySuperviseRuleService.updateDutySuperviseRule(updateReqVO);
//        return success(true);
//    }

    /**
     * 新增或者更新接口，该接口会判断是否存在，如果存在就进行更新，如果不存在就进行创建，根据排班模板ID进行判断
     * @param createReqVO
     * @return
     */
    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "新增或者更新综合管理-值班管理-值班督导规则配置")
    public CommonResult<String> createOrUpdateDutySuperviseRule(@Valid @RequestBody DutySuperviseRuleSaveReqVO createReqVO) {
        return success(dutySuperviseRuleService.createOrUpdateDutySuperviseRule(createReqVO));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-值班管理-值班督导规则配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDutySuperviseRule(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           dutySuperviseRuleService.deleteDutySuperviseRule(id);
        }
        return success(true);
    }

//    @GetMapping("/get")
//    @ApiOperation(value = "获得综合管理-值班管理-值班督导规则配置")
//    @ApiImplicitParam(name = "id", value = "编号")
//    public CommonResult<DutySuperviseRuleRespVO> getDutySuperviseRule(@RequestParam("id") String id) {
//        DutySuperviseRuleDO dutySuperviseRule = dutySuperviseRuleService.getDutySuperviseRule(id);
//        return success(BeanUtils.toBean(dutySuperviseRule, DutySuperviseRuleRespVO.class));
//    }
//
//
//    @PostMapping("/page")
//    @ApiOperation(value = "获得综合管理-值班管理-值班督导规则配置分页")
//    public CommonResult<PageResult<DutySuperviseRuleRespVO>> getDutySuperviseRulePage(@Valid @RequestBody DutySuperviseRulePageReqVO pageReqVO) {
//        PageResult<DutySuperviseRuleDO> pageResult = dutySuperviseRuleService.getDutySuperviseRulePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, DutySuperviseRuleRespVO.class));
//    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-值班管理-值班督导规则配置列表")
    public CommonResult<List<DutySuperviseRuleRespVO>> getDutySuperviseRuleList(@Valid @RequestBody DutySuperviseRuleListReqVO listReqVO) {
        List<DutySuperviseRuleDO> list = dutySuperviseRuleService.getDutySuperviseRuleList(listReqVO);
        List<DutySuperviseRuleRespVO> listRespVOList = new ArrayList<>();
        for (DutySuperviseRuleDO dutySuperviseRuleDO : list) {
            DutySuperviseRuleRespVO dutySuperviseRuleRespVO = BeanUtils.toBean(dutySuperviseRuleDO, DutySuperviseRuleRespVO.class);
            // 使用格式化方式将LocalTime转换为字符串，确保格式统一为"HH:mm"
            if (dutySuperviseRuleDO.getSigninStartTime() != null) {
                dutySuperviseRuleRespVO.setSigninStartTime(dutySuperviseRuleDO.getSigninStartTime().toString());
            }
            if (dutySuperviseRuleDO.getSigninEndTime() != null) {
                dutySuperviseRuleRespVO.setSigninEndTime(dutySuperviseRuleDO.getSigninEndTime().toString());
            }
            listRespVOList.add(dutySuperviseRuleRespVO);
        }
        return success(listRespVOList);
    }
}
