package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@ApiModel(description = "管理后台 - 实战平台-巡视管控-人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventDisposePostSaveVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("岗位编码")
    private String postCode;

    @ApiModelProperty("岗位名称")
    private String postName;

    @ApiModelProperty("推送类型(0:推送值班人员，1:岗位全部人员推送，2：自定义)")
    private String postType;

    @ApiModelProperty("是否内置(0：否，1：是)")
    private String isBuiltIn;

    @ApiModelProperty("其他人员列表")
    private List<UserRespDTO> otherUserList;
}
