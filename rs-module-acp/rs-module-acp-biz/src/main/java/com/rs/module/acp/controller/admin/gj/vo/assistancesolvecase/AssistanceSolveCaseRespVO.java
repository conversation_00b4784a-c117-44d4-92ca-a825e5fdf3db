package com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-协助破案 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssistanceSolveCaseRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("线索类型")
    private String clueType;
    @ApiModelProperty("线索获取日期")
    private Date clueGetTime;
    @ApiModelProperty("是否重大案件")
    private String clueSfzdaj;
    @ApiModelProperty("检举案件性质")
    private String clueJjajxz;
    @ApiModelProperty("涉案区域")
    private String clueSaqy;
    @ApiModelProperty("线索来源")
    private String clueXsly;
    @ApiModelProperty("涉案人")
    private String clueSar;
    @ApiModelProperty("涉案人数")
    private Integer clueSars;
    @ApiModelProperty("线索内容")
    private String clueContext;
    @ApiModelProperty("线索附件")
    private String clueAttachmentUrl;
    @ApiModelProperty("线索登记人")
    private String clueRegistUser;
    @ApiModelProperty("线索登记人身份证号")
    private String clueRegistUserSfzh;
    @ApiModelProperty("线索新增时间")
    private Date clueRegistTime;
    @ApiModelProperty("转递部门类型")
    private String forwardType;
    @ApiModelProperty("转递部门名称")
    private String forwardName;
    @ApiModelProperty("转递日期")
    private Date forwardTime;
    @ApiModelProperty("转递材料附件")
    private String forwardUrl;
    @ApiModelProperty("转递人")
    private String forwardUser;
    @ApiModelProperty("转递人身份证号")
    private String forwardUserSfzh;
    @ApiModelProperty("转递新增时间")
    private Date forwardUserTime;
    @ApiModelProperty("签收部门类型")
    private String feedbackReceiptType;
    @ApiModelProperty("签收部门名称")
    private String feedbackReceiptName;
    @ApiModelProperty("签收日期")
    private Date feedbackReceiptTime;
    @ApiModelProperty("反馈日期")
    private Date feedbackTime;
    @ApiModelProperty("查证情况")
    private String feedbackCzqk;
    @ApiModelProperty("破获刑事/行政案件数量")
    private Long feedbackPhajsl;
    @ApiModelProperty("抓获违法犯罪嫌疑人数量")
    private Long feedbackZhwffzxyrsl;
    @ApiModelProperty("发现在逃人员数量")
    private Long feedbackFxztrysl;
    @ApiModelProperty("缴获赃款赃物数量")
    private Long feedbackJhzkzwsl;
    @ApiModelProperty("反馈人")
    private String feedbackUser;
    @ApiModelProperty("反馈人身份证号")
    private String feedbackUserSfzh;
    @ApiModelProperty("反馈新增时间")
    private Date feedbackUserTime;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XZPALZZT")
    private String status;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
}
