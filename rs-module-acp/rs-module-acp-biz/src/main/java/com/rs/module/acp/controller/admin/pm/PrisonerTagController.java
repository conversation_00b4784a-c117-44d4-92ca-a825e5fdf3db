package com.rs.module.acp.controller.admin.pm;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagListReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerTagSaveReqVO;
import com.rs.module.base.entity.pm.PrisonerTagDO;
import com.rs.module.base.service.pm.PrisonerTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-监管人员标签")
@RestController
@RequestMapping("/acp/pm/prisonerTag")
@Validated
public class PrisonerTagController {

    @Resource
    private PrisonerTagService prisonerTagService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-监管人员标签")
    @LogRecordAnnotation(bizModule = "acp:prisonerTag:create", operateType = LogOperateType.CREATE, title = "创建实战平台-监管管理-监管人员标签",
    success = "创建实战平台-监管管理-监管人员标签成功", fail = "创建实战平台-监管管理-监管人员标签失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonerTag(@Valid @RequestBody PrisonerTagSaveReqVO createReqVO) {
        return success(prisonerTagService.createPrisonerTag(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-监管人员标签")
    @LogRecordAnnotation(bizModule = "acp:prisonerTag:update", operateType = LogOperateType.UPDATE, title = "更新实战平台-监管管理-监管人员标签",
    success = "更新实战平台-监管管理-监管人员标签成功", fail = "更新实战平台-监管管理-监管人员标签失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrisonerTag(@Valid @RequestBody PrisonerTagSaveReqVO updateReqVO) {
        prisonerTagService.updatePrisonerTag(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-监管人员标签")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonerTag:delete", operateType = LogOperateType.DELETE, title = "删除实战平台-监管管理-监管人员标签",
    success = "删除实战平台-监管管理-监管人员标签成功", fail = "删除实战平台-监管管理-监管人员标签失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrisonerTag(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonerTagService.deletePrisonerTag(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-监管人员标签")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PrisonerTagRespVO> getPrisonerTag(@RequestParam("id") String id) {
        PrisonerTagDO prisonerTag = prisonerTagService.getPrisonerTag(id);
        return success(BeanUtils.toBean(prisonerTag, PrisonerTagRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-监管人员标签分页")
    public CommonResult<PageResult<PrisonerTagRespVO>> getPrisonerTagPage(@Valid @RequestBody PrisonerTagPageReqVO pageReqVO) {
        PageResult<PrisonerTagDO> pageResult = prisonerTagService.getPrisonerTagPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonerTagRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-监管人员标签列表")
    public CommonResult<List<PrisonerTagRespVO>> getPrisonerTagList(@Valid @RequestBody PrisonerTagListReqVO listReqVO) {
        List<PrisonerTagDO> list = prisonerTagService.getPrisonerTagList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonerTagRespVO.class));
    }
}
