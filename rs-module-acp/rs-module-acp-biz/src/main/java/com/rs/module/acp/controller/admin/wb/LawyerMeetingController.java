package com.rs.module.acp.controller.admin.wb;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.cache.DicUtil;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.module.acp.entity.wb.LawyerMeetingDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.service.wb.LawyerMeetingService;

@Api(tags = "实战平台-窗口业务-律师会见登记")
@RestController
@RequestMapping("/acp/wb/lawyerMeeting")
@Validated
public class LawyerMeetingController {

    @Resource
    private LawyerMeetingService lawyerMeetingService;

    @Autowired
    private WbCommonService wbCommonService;

    @GetMapping("/sameCaseJudgment")
    @ApiOperation(value = "实战平台-窗口业务-创建律师会见登记前置接口1-同案判断")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码",required = true),
            @ApiImplicitParam(name = "lawyerIds", value = "律师ID，多个律师时，使用英文逗号隔开",required = true)
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:sameCaseJudgment", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-创建律师会见登记前置接口1-同案判断",
            success = "实战平台-窗口业务-创建律师会见登记前置接口1-同案判断成功", fail = "实战平台-窗口业务-创建律师会见登记前置接口1-同案判断失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<JSONObject> sameCaseJudgment(@RequestParam(value = "jgrybm",required = true) String jgrybm,
                                                 @RequestParam(value = "lawyerIds",required = true) String lawyerIds) {
        return success(lawyerMeetingService.sameCaseJudgment(jgrybm,lawyerIds));
    }

    @GetMapping("/timeOverlapJudgment")
    @ApiOperation(value = "实战平台-窗口业务-创建律师会见登记前置接口2-时间段重叠判断")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码",required = true),
            @ApiImplicitParam(name = "meetingMethod", value = "会见方式",required = true),
            @ApiImplicitParam(name = "appointmentTime", value = "预约会见日期（选择现场会见传入这个）",required = false),
            @ApiImplicitParam(name = "appointmentTimeSlot", value = "预约会见时间段（选择现场会见传入这个）",required = false),
            @ApiImplicitParam(name = "applyMeetingStartTime", value = "预约会见开始时间（选择快速会见、远程会见传入这个）",required = false),
            @ApiImplicitParam(name = "applyMeetingEndTime", value = "预约会见结束时间（选择快速会见、远程会见传入这个）",required = false),
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:timeOverlapJudgment", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-创建律师会见登记前置接口2-时间段重叠判断",
            success = "实战平台-窗口业务-创建律师会见登记前置接口2-时间段重叠判断成功", fail = "实战平台-窗口业务-创建律师会见登记前置接口2-时间段重叠判断失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<JSONObject> timeOverlapJudgment(@RequestParam(value = "jgrybm",required = true) String jgrybm,
                                                     @RequestParam(value = "meetingMethod",required = true) String meetingMethod,
                                                     @RequestParam(value = "appointmentTime",required = false) String appointmentTime,
                                                     @RequestParam(value = "appointmentTimeSlot",required = false) String appointmentTimeSlot,
                                                     @RequestParam(value = "applyMeetingStartTime",required = false) String applyMeetingStartTime,
                                                     @RequestParam(value = "applyMeetingEndTime",required = false) String applyMeetingEndTime) {
        return success(lawyerMeetingService.timeOverlapJudgment(jgrybm,meetingMethod,appointmentTime,appointmentTimeSlot,applyMeetingStartTime,applyMeetingEndTime));
    }

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建律师会见登记")
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建律师会见登记",
            success = "实战平台-窗口业务-创建律师会见登记成功", fail = "实战平台-窗口业务-创建律师会见登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createLawyerMeeting(@Valid @RequestBody LawyerMeetingSaveReqVO createReqVO) {
        return success(lawyerMeetingService.createLawyerMeeting(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得律师会见登记")
    @ApiImplicitParam(name = "id", value = "业务ID")
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:create", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得律师会见登记",
            success = "实战平台-窗口业务-获得律师会见登记成功", fail = "实战平台-窗口业务-获得律师会见登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<LawyerMeetingRespVO> getLawyerMeeting(@RequestParam("id") String id) {
        return success(lawyerMeetingService.getLawyerMeetingById(id));
    }

    @GetMapping("/getMeetingConfig")
    @ApiOperation(value = "实战平台-窗口业务-根据申请日期获取律师会见配置")
    @ApiImplicitParam(name = "applData", value = "申请日期（年月日 yyyy-MM-dd）",required = true)
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getMeetingConfig", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获取律师会见配置",
            success = "实战平台-窗口业务-获取律师会见配置成功", fail = "实战平台-窗口业务-获取律师会见配置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<JSONObject> getMeetingConfig(@RequestParam(value = "applData",required = true) String applData) {
        return success(lawyerMeetingService.getMeetingConfig(applData));
    }

    @GetMapping("/signIn")
    @ApiOperation(value = "实战平台-窗口业务-律师签到")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "checkInTime", value = "签到时间", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:signIn", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-律师签到",
            success = "实战平台-窗口业务-律师签到成功", fail = "实战平台-窗口业务-律师签到失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> signIn(@RequestParam(value = "id", required = true) String id,
                                        @RequestParam(value = "checkInTime", required = true) String checkInTime) {
        if (lawyerMeetingService.signIn(id, checkInTime)) {
            return success();
        }
        return error("签到失败");
    }

    @GetMapping("/getIdleMeetingRoom")
    @ApiOperation(value = "实战平台-窗口业务-获取空闲律师会见室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getIdleMeetingRoom", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-获取空闲律师会见室",
            success = "实战平台-窗口业务-获取空闲律师会见室成功", fail = "实战平台-窗口业务-获取空闲律师会见室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<List<JSONObject>> getIdleMeetingRoom(@RequestParam(value = "id", required = true) String id) {
        return success(wbCommonService.getIdleLawyerMeetingRoom(id, WbConstants.BUSINESS_TYPE_LAWYER_MEETING));
    }

    @GetMapping("/allocationRoom")
    @ApiOperation(value = "实战平台-窗口业务-分配律师会见室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "roomId", value = "会见室ID", required = true, dataType = "String"),
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:allocationRoom", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-分配律师会见室",
            success = "实战平台-窗口业务-分配律师会见室成功", fail = "实战平台-窗口业务-分配律师会见室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> allocationRoom(@RequestParam(value = "id", required = true) String id,
                                                @RequestParam(value = "roomId", required = true) String roomId) {
        if (lawyerMeetingService.allocationRoom(id, roomId)) {
            return success();
        }
        return error("分配律师会见室失败");
    }

    @PostMapping("/escortingInspect")
    @ApiOperation(value = "实战平台-窗口业务-带出安检")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "escortingPolice", value = "带出民警姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPoliceSfzh", value = "带出民警身份证号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingTime", value = "带出监室时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectionResult", value = "检查结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "prohibitedItems", value = "违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "physicalExam", value = "体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "abnormalSituations", value = "异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "inspectionTime", value = "检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectorSfzh", value = "检查人证件号码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspector", value = "检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingStartTime", value = "会见开始时间", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:escortingInspect", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-带出安检",
            success = "实战平台-窗口业务-带出安检成功", fail = "实战平台-窗口业务-带出安检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")

    public CommonResult<Boolean> escortingInspect(@RequestBody LawyerMeetingSaveReqVO updateReqVO) {
        if (lawyerMeetingService.escortingInspect(updateReqVO)) {
            return success();
        }
        return error("保存带出安检失败");
    }

    @PostMapping("/returnInspection")
    @ApiOperation(value = "实战平台-窗口业务-会毕安检")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "meetingEndTime", value = "会见结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectorSfzh", value = "会毕检查人身份证", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspector", value = "会毕检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionTime", value = "会毕检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionResult", value = "会毕检查结果", dataType = "String"),
            @ApiImplicitParam(name = "returnProhibitedItems", value = "带回违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "returnPhysicalExam", value = "带回体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "returnAbnormalSituations", value = "带回异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "returnTime", value = "带回监室时间", dataType = "String"),
            @ApiImplicitParam(name = "returnPolice", value = "带回民警姓名", dataType = "String"),
            @ApiImplicitParam(name = "returnPoliceSfzh", value = "带回民警身份证号", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:returnInspection", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-会毕安检",
            success = "实战平台-窗口业务-会毕安检成功", fail = "实战平台-窗口业务-会毕安检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_LSHJ,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"经办时间\":\"{{#updateReqVO.addTime}}\"," +
            "\"会见方式\":\"{{#updateReqVO.meetingMethod}}\",\"会见开始时间\":\"{{#fDateTime(#updateReqVO.meetingStartTime)}}\"," +
            "\"会见结束时间\":\"{{#fDateTime(#updateReqVO.meetingEndTime)}}\"}")
    public CommonResult<Boolean> returnInspection(@RequestBody LawyerMeetingSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        LawyerMeetingDO lawyerMeetingDO = lawyerMeetingService.getById(updateReqVO.getId());
        updateReqVO.setAddTime(DateUtil.format(lawyerMeetingDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        String appCode = HttpUtils.getAppCode();
        updateReqVO.setMeetingMethod(DicUtil.translate(appCode,"ZD_WB_LSHJFS",lawyerMeetingDO.getMeetingMethod()));
        updateReqVO.setMeetingStartTime(lawyerMeetingDO.getMeetingStartTime());
        updateReqVO.setJgrybm(lawyerMeetingDO.getJgrybm());
        /****记录轨迹 end****/
        if (lawyerMeetingService.returnInspection(updateReqVO)) {
            return success();
        }
        return error("保存会毕安检失败");
    }

    @GetMapping("/getHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录",
            success = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录成功", fail = "实战平台-窗口业务-根据监管人员编码获取监管人员历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<LawyerMeetingRespVO>> getHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                  @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                  @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(lawyerMeetingService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize));
    }

    @GetMapping("/getHistoryMeetingByLwayerId")
    @ApiOperation(value = "实战平台-窗口业务-根据律师ID获取律师历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getHistoryMeetingByLwayerId", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据律师ID获取律师历史会见记录",
            success = "实战平台-窗口业务-根据律师ID获取律师历史会见记录成功", fail = "实战平台-窗口业务-根据律师ID获取律师历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<LawyerMeetingRespVO>> getHistoryMeeting(@RequestParam("lawyerId") String lawyerId,
                                                                      @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                      @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(lawyerMeetingService.getHistoryMeetingByLwayerId(lawyerId,pageNo,pageSize));
    }

    @PostMapping("/additionalRecording")
    @ApiOperation(value = "实战平台-窗口业务-补录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "checkInTime", value = "签到时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "room_id", value = "审讯室ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPolice", value = "带出民警姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPoliceSfzh", value = "带出民警身份证号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingTime", value = "带出监室时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectionResult", value = "检查结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "prohibitedItems", value = "违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "physicalExam", value = "体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "abnormalSituations", value = "异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "inspectionTime", value = "检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectorSfzh", value = "检查人证件号码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspector", value = "检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingStartTime", value = "提讯开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "meetingEndTime", value = "提讯结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectorSfzh", value = "会毕检查人身份证", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspector", value = "会毕检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionTime", value = "会毕检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionResult", value = "会毕检查结果", dataType = "String"),
            @ApiImplicitParam(name = "returnProhibitedItems", value = "带回违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "returnPhysicalExam", value = "带回体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "returnAbnormalSituations", value = "带回异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "returnTime", value = "带回监室时间", dataType = "String"),
            @ApiImplicitParam(name = "returnPolice", value = "带回民警姓名", dataType = "String"),
            @ApiImplicitParam(name = "returnPoliceSfzh", value = "带回民警身份证号", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:additionalRecording", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-补录",
            success = "实战平台-窗口业务-补录成功", fail = "实战平台-窗口业务-补录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_LSHJ,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"经办时间\":\"{{#updateReqVO.addTime}}\"," +
            "\"会见方式\":\"{{#updateReqVO.meetingMethod}}\",\"会见开始时间\":\"{{#fDateTime(#updateReqVO.meetingStartTime)}}\"," +
            "\"会见结束时间\":\"{{#fDateTime(#updateReqVO.meetingEndTime)}}\"}")
    public CommonResult<Boolean> additionalRecording(@RequestBody LawyerMeetingSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        LawyerMeetingDO lawyerMeetingDO = lawyerMeetingService.getById(updateReqVO.getId());
        updateReqVO.setAddTime(DateUtil.format(lawyerMeetingDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        String appCode = HttpUtils.getAppCode();
        updateReqVO.setMeetingMethod(DicUtil.translate(appCode,"ZD_WB_LSHJFS",lawyerMeetingDO.getMeetingMethod()));
        updateReqVO.setJgrybm(lawyerMeetingDO.getJgrybm());
        /****记录轨迹 end****/
        if (lawyerMeetingService.additionalRecording(updateReqVO)) {
            return success();
        }
        return error("保存补录失败");
    }

    @GetMapping("/getOnSiteNumbering")
    @ApiOperation(value = "实战平台-窗口业务-律师现场会见排号信息")
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getOnSiteNumbering", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-律师现场会见排号信息",success = "实战平台-窗口业务-律师现场会见排号信息成功",
            fail = "实战平台-窗口业务-律师现场会见排号信息失败，错误信息：{{#_ret[msg]}}")
    public CommonResult<List<JSONObject>> getIdleMeetingRoom() {
        return success(lawyerMeetingService.getOnSiteNumbering());
    }

    @GetMapping("/getremoteNumbering")
    @ApiOperation(value = "实战平台-窗口业务-律师远程会见排号信息")
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getOnSiteNumbering", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-律师远程会见排号信息", success = "实战平台-窗口业务-律师远程会见排号信息成功",
            fail = "实战平台-窗口业务-律师远程会见排号信息失败，错误信息：{{#_ret[msg]}}")
    public CommonResult<List<JSONObject>> getremoteNumbering() {
        return success(lawyerMeetingService.getremoteNumbering());
    }

    @GetMapping("/getTrajectory")
    @ApiOperation(value = "实战平台-窗口业务-获得律师会见轨迹")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getTrajectory", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得律师会见轨迹",
            success = "实战平台-窗口业务-获得律师会见轨迹成功", fail = "实战平台-窗口业务-获得律师会见轨迹失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<List<JSONObject>> getTrajectory(@RequestParam("id") String id) {
        return success(wbCommonService.getTrajectory(id,WbConstants.BUSINESS_TYPE_LAWYER_MEETING));
    }

    @GetMapping("/getNewHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerMeeting:getNewHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录",
            success = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录成功",
            fail = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<JSONObject>> getNewHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                             @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(wbCommonService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize, WbConstants.BUSINESS_TYPE_LAWYER_MEETING));
    }


}
