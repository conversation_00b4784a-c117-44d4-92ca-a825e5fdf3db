package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-家属会见登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FamilyMeetingPageReqVO extends PageParam  {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("第一位家属姓名")
    private String familyMember1Name;

    @ApiModelProperty("第一位家属性别")
    private String familyMember1Gender;

    @ApiModelProperty("第一位家属证件类型")
    private String familyMember1IdType;

    @ApiModelProperty("第一位家属证件号码")
    private String familyMember1IdNumber;

    @ApiModelProperty("第一位家属与被会见人社会关系")
    private String familyMember1Relationship;

    @ApiModelProperty("第一位家属联系方式")
    private String familyMember1Contact;

    @ApiModelProperty("第一位家属工作单位")
    private String familyMember1WorkUnit;

    @ApiModelProperty("第一位家属居住地址")
    private String familyMember1Address;

    @ApiModelProperty("第二位家属姓名")
    private String familyMember2Name;

    @ApiModelProperty("第二位家属性别")
    private String familyMember2Gender;

    @ApiModelProperty("第二位家属证件类型")
    private String familyMember2IdType;

    @ApiModelProperty("第二位家属的证件号码")
    private String familyMember2IdNumber;

    @ApiModelProperty("第二位家属与被会见人社会关系")
    private String familyMember2Relationship;

    @ApiModelProperty("第二位家属联系方式")
    private String familyMember2Contact;

    @ApiModelProperty("第二位家属工作单位")
    private String familyMember2WorkUnit;

    @ApiModelProperty("第二位家属居住地址")
    private String familyMember2Address;

    @ApiModelProperty("第三位家属姓名")
    private String familyMember3Name;

    @ApiModelProperty("第三位家属性别")
    private String familyMember3Gender;

    @ApiModelProperty("第三位家属证件类型")
    private String familyMember3IdType;

    @ApiModelProperty("第三位家属的证件号码")
    private String familyMember3IdNumber;

    @ApiModelProperty("第三位家属与被会见人社会关系")
    private String familyMember3Relationship;

    @ApiModelProperty("第三位家属联系方式")
    private String familyMember3Contact;

    @ApiModelProperty("第三位家属工作单位")
    private String familyMember3WorkUnit;

    @ApiModelProperty("第三位家属居住地址")
    private String familyMember3Address;

    @ApiModelProperty("办案机关批准会见文书号")
    private String approvalDocumentNumber;

    @ApiModelProperty("会见批准材料附件上传后存储路径")
    private String approvalAttachmentPath;

    @ApiModelProperty("关于此次会见的备注信息")
    private String remarks;

    @ApiModelProperty("办理状态")
    private String status;

    @ApiModelProperty("会见所在房间编号")
    private String roomId;

    @ApiModelProperty("分配房间时间")
    private Date[] assignmentRoomTime;

    @ApiModelProperty("分配民警身份证号")
    private String assignmentPoliceSfzh;

    @ApiModelProperty("assignment_police")
    private String assignmentPolice;

    @ApiModelProperty("签到时间")
    private Date[] checkInTime;

    @ApiModelProperty("签到用户身份证号")
    private String checkInPoliceSfzh;

    @ApiModelProperty("签到用户")
    private String checkInPolice;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date[] escortingTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("检查时间")
    private Date[] inspectionTime;

    @ApiModelProperty("inspection_situation")
    private String inspectionSituation;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("会见开始时间")
    private Date[] meetingStartTime;

    @ApiModelProperty("会见结束时间")
    private Date[] meetingEndTime;

    @ApiModelProperty("会毕检查人")
    private String returnInspectorSfzh;

    @ApiModelProperty("会毕检查人")
    private String returnInspector;

    @ApiModelProperty("会毕检查时间")
    private Date[] returnInspectionTime;

    @ApiModelProperty("会毕检查结果")
    private String returnInspectionResult;

    @ApiModelProperty("带回违禁物品登记")
    private String returnProhibitedItems;

    @ApiModelProperty("带回体表检查登记")
    private String returnPhysicalExam;

    @ApiModelProperty("带回异常情况登记")
    private String returnAbnormalSituations;

    @ApiModelProperty("带回监室时间")
    private Date[] returnTime;

    @ApiModelProperty("return_police_sfzh")
    private String returnPoliceSfzh;

    @ApiModelProperty("带回民警姓名")
    private String returnPolice;

    @ApiModelProperty("带出操作人身份证号")
    private String escortingOperatorSfzh;

    @ApiModelProperty("带出操作人")
    private String escortingOperator;

    @ApiModelProperty("带出操作时间")
    private Date[] escortingOperatorTime;

    @ApiModelProperty("带回操作人身份证号")
    private String returnOperatorSfzh;

    @ApiModelProperty("带回操作人")
    private String returnOperator;

    @ApiModelProperty("带回操作时间")
    private Date[] returnOperatorTime;

    @ApiModelProperty("快速会见/远程会见-预约会见开始时间")
    private Date[] applyMeetingStartTime;

    @ApiModelProperty("快速会见/远程会见-预约会见结束时间")
    private Date[] applyMeetingEndTime;

    @ApiModelProperty("第一位家属职业")
    private String familyMember1Occupation;

    @ApiModelProperty("第二位家属职业")
    private String familyMember2Occupation;

    @ApiModelProperty("第三位家属职业")
    private String familyMember3Occupation;

    @ApiModelProperty("第一位家属ID")
    private String familyMember1Id;

    @ApiModelProperty("第二位家属ID")
    private String familyMember2Id;

    @ApiModelProperty("第三位家属ID")
    private String familyMember3Id;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
