package com.rs.module.acp.controller.admin.gj.vo.groupinout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-集体出入新增/修改 Request VO")
@Data
public class GroupInOutSaveReqVO implements java.io.Serializable {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("业务类型")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @ApiModelProperty("是否按监室(0:否，1:是)")
    private String isRoom;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("详情")
    private List<GroupInOutDetail> details;

}
