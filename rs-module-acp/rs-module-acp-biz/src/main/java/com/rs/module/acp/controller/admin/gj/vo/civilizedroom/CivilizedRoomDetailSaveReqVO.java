package com.rs.module.acp.controller.admin.gj.vo.civilizedroom;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明监室登记明细新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedRoomDetailSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("文明监室登记ID")
    private String civilizedRoomId;

    @ApiModelProperty("room_id")
    @NotEmpty(message = "room_id不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("违规数")
    @NotNull(message = "违规数不能为空")
    private Integer numberOfViolations;

    @ApiModelProperty("评选理由")
    @NotEmpty(message = "评选理由不能为空")
    private String selectionReason;

    @ApiModelProperty("附件地址")
    private String attUrl;

}
