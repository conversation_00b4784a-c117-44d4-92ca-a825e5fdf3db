package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("courses_code")
    private String coursesCode;

    @ApiModelProperty("courses_name")
    @NotEmpty(message = "courses_name不能为空")
    private String coursesName;

    @ApiModelProperty("分类颜色")
    private String coursesColor;

    @ApiModelProperty("是否启用 0 否  1 是")
    @NotNull(message = "是否启用不能为空")
    private Short isEnabled;

}
