package com.rs.module.acp.controller.admin.gj.vo.punishment;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 惩罚管理-外部插入
 * <AUTHOR>
 * @date 2025/6/13 21:02
 */
@Data
public class PunishmentSaveExternalInfoVO {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("备注")
    private String remark;

    @TableField(value = "申请人")
    private String addUser;

    @TableField(value = "申请人姓名")
    private String addUserName;

    @TableField(value = "申请时间")
    private Date addTime;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）1 实战平台 2 仓外屏 3 仓内屏")
    @NotEmpty(message = "数据来源（字典：ZD_DATA_SOURCES）不能为空")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("处罚原因-中文")
    private String reasonName;

    @ApiModelProperty("呈批处罚周期开始日期")
    @NotNull(message = "呈批处罚周期开始日期不能为空")
    private Date startDate;

    @ApiModelProperty("呈批处罚周期结束日期")
    @NotNull(message = "呈批处罚周期结束日期不能为空")
    private Date endDate;

    @ApiModelProperty("处罚措施")
    private String measures;

    @ApiModelProperty("处罚措施-中文")
    private String measuresName;


}
