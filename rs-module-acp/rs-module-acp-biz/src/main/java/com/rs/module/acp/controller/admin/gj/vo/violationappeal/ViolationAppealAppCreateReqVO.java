package com.rs.module.acp.controller.admin.gj.vo.violationappeal;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-违规申诉新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ViolationAppealAppCreateReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("违规登记id")
    @NotEmpty(message = "违规登记id不能为空")
    private String violationRecordId;

    @ApiModelProperty("申诉理由")
    @NotEmpty(message = "申诉理由不能为空")
    private String appealReason;

    @ApiModelProperty("申诉要求")
    @NotEmpty(message = "申诉要求不能为空")
    private String appealRequirement;

}
