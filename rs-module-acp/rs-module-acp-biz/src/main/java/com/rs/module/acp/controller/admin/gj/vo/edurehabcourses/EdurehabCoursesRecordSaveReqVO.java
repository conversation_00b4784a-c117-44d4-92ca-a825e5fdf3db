package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.time.LocalTime;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("教育康复课程计划ID")
    private String edurehabCoursesPlanId;

    @ApiModelProperty("课程日期")
    @NotNull(message = "课程日期不能为空")
    private Date coursesDate;

    @ApiModelProperty("监区ID")
    @NotEmpty(message = "监区ID不能为空")
    private String areaId;

    @ApiModelProperty("监区名称")
    @NotEmpty(message = "监区名称不能为空")
    private String areaName;

    @ApiModelProperty("课程时段编码，如“9:00-10:00”，唯一标识时段")
    @NotEmpty(message = "课程时段编码，如“9:00-10:00”，唯一标识时段不能为空")
    private String timeSlotCode;

    @ApiModelProperty("课程时段-开始")
    @NotNull(message = "课程时段-开始不能为空")
    private String timeSlotStartTime;

    @ApiModelProperty("课程时段-结束")
    @NotNull(message = "课程时段-结束不能为空")
    private String timeSlotEndTime;

    @ApiModelProperty("courses_code")
    private String coursesCode;

    @ApiModelProperty("courses_name")
    private String coursesName;

    @ApiModelProperty("coursesColor")
    private String coursesColor;

}
