package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.punishment.*;
import com.rs.module.acp.service.gj.punishment.PunishmentService;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-处罚呈批")
@RestController
@RequestMapping("/acp/gj/punishment")
@Validated
public class PunishmentController {

    @Resource
    private PunishmentService punishmentService;

    @PostMapping("/create")
    @ApiOperation(value = "管教业务-处罚呈批")
    @LogRecordAnnotation(bizModule = "acp:punishment:create", operateType = LogOperateType.CREATE, title = "管教业务-处罚呈批-登记",
            success = "管教业务-处罚呈批-创建登记成功", fail = "管教业务-处罚呈批-创建请登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPunishment(@Valid @RequestBody PunishmentSaveReqVO createReqVO) {
        return success(punishmentService.createPunishment(createReqVO));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "管教业务-处罚呈批-领导审批")
    @LogRecordAnnotation(bizModule = "acp:punishment:approve", operateType = LogOperateType.UPDATE, title = "管教业务-处罚呈批-领导审批",
            success = "管教业务-处罚呈批-领导审批成功", fail = "管教业务-处罚呈批-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_CF, jgrybm="{{#approveReqVO.jgrybm}}",condition = "'5'.equals(#approveReqVO.approvalResult)",
            content = "{\"在押人员\":\"{{#approveReqVO.jgryxm}}\",\"惩罚原因\":\"\",\"惩罚内容\":\"\"}")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody GjApproveReqVO approveReqVO) {
         punishmentService.leaderApprove(approveReqVO);
        return success(true);
    }


    @PostMapping("/regInfo")
    @ApiOperation(value = "管教业务-处罚呈批-处罚登记")
    @LogRecordAnnotation(bizModule = "acp:punishment:regInfo", operateType = LogOperateType.UPDATE, title = "管教业务-监室调整-领导审批",
            success = "管教业务-处罚呈批-处罚登记成功", fail = "管教业务-处罚呈批-处罚登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> regInfo(@Valid @RequestBody PunishmentSaveRegInfoVO regInfoVO) {
        punishmentService.regInfo(regInfoVO);
        return success(true);
    }

    @PostMapping("/createExtend")
    @ApiOperation(value = "管教业务-处罚呈批-延长呈批申请")
    @LogRecordAnnotation(bizModule = "acp:punishment:createExtend", operateType = LogOperateType.CREATE, title = "管教业务-处罚呈批-登记",
            success = "管教业务-处罚呈批-创建登记成功", fail = "管教业务-处罚呈批-创建请登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> createExtend(@Valid @RequestBody PunishmentExtendSaveReqVO createReqVO) {
        punishmentService.createExtend(createReqVO);
        return success(true);
    }

    @PostMapping("/approveExtend")
    @ApiOperation(value = "管教业务-处罚呈批-延长审批")
    @LogRecordAnnotation(bizModule = "acp:punishment:approveExtend", operateType = LogOperateType.CREATE, title = "管教业务-处罚呈批-领导审批",
            success = "管教业务-处罚呈批-领导审批成功", fail = "管教业务-处罚呈批-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> approveExtend(@Valid @RequestBody GjApproveReqVO createReqVO) {
        punishmentService.approveExtend(createReqVO);
        return success(true);
    }


    /**
     * 提前解除审批
     * <AUTHOR>
     * @date 2025/6/11 10:39
     * @param [createReqVO]
     * @return com.rs.framework.common.pojo.CommonResult<java.lang.Boolean>
     */
    @PostMapping("/createRemove")
    @ApiOperation(value = "管教业务-处罚呈批-提前解除申请")
    @LogRecordAnnotation(bizModule = "acp:punishment:createRemove", operateType = LogOperateType.CREATE, title = "管教业务-提前解除处罚呈批",
            success = "管教业务-提前解除处罚呈批-登记", fail = "管教业务-提前解除处罚呈批-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> createRemove(@Valid @RequestBody PunishmentRemoveSaveReqVO createReqVO) {
        punishmentService.createRemove(createReqVO);
        return success(true);
    }


    /**
     * 提前解除审批
     * <AUTHOR>
     * @date 2025/6/11 10:39
     * @param [createReqVO]
     * @return com.rs.framework.common.pojo.CommonResult<java.lang.Boolean>
     */
    @PostMapping("/apprRemove")
    @ApiOperation(value = "管教业务-处罚呈批-提前解除审批")
    @LogRecordAnnotation(bizModule = "acp:punishment:apprRemove", operateType = LogOperateType.CREATE, title = "管教业务-提前解除处罚呈批",
            success = "管教业务-提前解除处罚呈批-登记", fail = "管教业务-提前解除处罚呈批-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> apprRemove(@Valid @RequestBody GjApproveReqVO createReqVO) {
        punishmentService.apprRemove(createReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "管教业务-处罚信息-获取详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:punishment:get", operateType = LogOperateType.QUERY, title = "管教业务-处罚信息-详情获取",
            bizNo = "{{#id}}", success = "管教业务-管教业务-处罚呈批获取详情成功", fail = "管教业务-处罚呈批-处罚呈批获取详情失败", extraInfo = "{{#id}}")
    public CommonResult<PunishmentRespVO> getPunishment(@RequestParam("id") String id) {
        PunishmentRespVO punishment = punishmentService.getPunishment(id);
        return success(punishment );
    }

    @GetMapping("/getPunishmentHistoryByJgrybm")
    @ApiOperation(value = "获取处罚历史记录信息")
    @LogRecordAnnotation(bizModule = "acp:punishment:history", operateType = LogOperateType.QUERY, title = "管教业务-处罚信息-处罚历史记录",
            bizNo = "{{#jgrybm}}", success = "获取历史处罚记录-成功", fail = "获取历史处罚记录详情失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<PageResult<PunishmentRespVO>> getPunishmentHistoryByJgrybm(@RequestParam("jgrybm") String jgrybm,@RequestParam("pageNo") Long pageNo,@RequestParam("pageSize")  Long pageSize) {
        PageResult<PunishmentRespVO> pageResult = punishmentService.getPunishmentHistoryByJgrybm(jgrybm, pageNo, pageSize);
        return success(BeanUtils.toBean(pageResult, PunishmentRespVO.class));
    }

    @PostMapping("/removeRegInfo")
    @ApiOperation(value = "管教业务-处罚信息-解除处罚登记")
    @LogRecordAnnotation(bizModule = "acp:punishment:removeRegInfo", operateType = LogOperateType.CREATE, title = "管教业务-提前解除处罚呈批",
            success = "管教业务-解除处罚登记", fail = "管教业务-解除处罚登记-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> removeRegInfo(@RequestBody PunishmentSaveRemoveReqVO createReqVO) {
        punishmentService.removeRegInfo(createReqVO);
        return success(true);
    }

    @GetMapping("/getApproveTrack")
    @ApiOperation(value = "管教业务-监室调整-获取轨迹记录")
    public CommonResult<List<GjApprovalTraceVO>> getApproveTrack(@RequestParam("id")String id) {
        List<GjApprovalTraceVO> list = punishmentService.getApproveTrack(id);
        return success(list);
    }


}
