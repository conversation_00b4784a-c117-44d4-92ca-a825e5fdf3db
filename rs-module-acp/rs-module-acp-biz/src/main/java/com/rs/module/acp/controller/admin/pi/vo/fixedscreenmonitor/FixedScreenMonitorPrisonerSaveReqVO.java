package com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-定屏监控与被监管人员关联新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FixedScreenMonitorPrisonerSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("定屏监控ID")
    private String fixedScreenMonitorId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("上墙人员信息")
    private String onScreenPersonInfo;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

}
