package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-来访人员信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VisitorInfoSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("对外开放登记ID")
    @NotEmpty(message = "对外开放登记ID不能为空")
    private String visitorRegId;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String name;

    @ApiModelProperty("证件类型")
    @NotEmpty(message = "证件类型不能为空")
    private String idType;

    @ApiModelProperty("证件号码")
    @NotEmpty(message = "证件号码不能为空")
    private String idNumber;

}
