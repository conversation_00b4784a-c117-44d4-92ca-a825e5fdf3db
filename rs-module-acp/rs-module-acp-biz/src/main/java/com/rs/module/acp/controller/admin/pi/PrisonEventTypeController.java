package com.rs.module.acp.controller.admin.pi;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypePageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventTypeSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.service.pi.PrisonEventTypeService;

@Api(tags = "实战平台-巡视管控-所情事件类型")
@RestController
@RequestMapping("/acp/pi/prisonEventType")
@Validated
public class PrisonEventTypeController {

    @Resource
    private PrisonEventTypeService prisonEventTypeService;

    @PostMapping("/create")
    @ApiOperation(value = "创建所情事件类型")
    @LogRecordAnnotation(bizModule = "acp:prisonEventType:create", operateType = LogOperateType.CREATE, title = "实战平台-巡视管控-创建所情事件类型",
            success = "实战平台-巡视管控-创建所情事件类型成功", fail = "实战平台-巡视管控-创建所情事件类型失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createPrisonEventType(@Valid @RequestBody PrisonEventTypeSaveReqVO createReqVO) {
        return success(prisonEventTypeService.createPrisonEventType(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新所情事件类型")
    @LogRecordAnnotation(bizModule = "acp:prisonEventType:update", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-更新所情事件类型",
            success = "实战平台-巡视管控-更新所情事件类型成功", fail = "实战平台-巡视管控-更新所情事件类型失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> updatePrisonEventType(@Valid @RequestBody PrisonEventTypeSaveReqVO updateReqVO) {
        prisonEventTypeService.updatePrisonEventType(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除所情事件类型")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonEventType:delete", operateType = LogOperateType.DELETE, title = "实战平台-巡视管控-删除所情事件类型",
            success = "实战平台-巡视管控-删除所情事件类型成功", fail = "实战平台-巡视管控-删除所情事件类型失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deletePrisonEventType(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           prisonEventTypeService.deletePrisonEventType(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得所情事件类型详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonEventType:get", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获得所情事件类型详情",
            success = "实战平台-巡视管控-获得所情事件类型详情成功",
            fail = "实战平台-巡视管控-获得所情事件类型详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<PrisonEventTypeRespVO> getPrisonEventType(@RequestParam("id") String id) {
        return success(prisonEventTypeService.getPrisonEventTypeById(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得所情事件类型分页")
    @LogRecordAnnotation(bizModule = "acp:prisonEventType:page", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获得所情事件类型分页",
            success = "实战平台-巡视管控-获得所情事件类型分页成功",
            fail = "实战平台-巡视管控-获得所情事件类型分页失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrisonEventTypeRespVO>> getPrisonEventTypePage(@Valid @RequestBody PrisonEventTypePageReqVO pageReqVO) {
        return success(prisonEventTypeService.getPrisonEventTypePage(pageReqVO));
    }

    @PostMapping("/list")
    @ApiOperation(value = "所情事件类型列表")
    @LogRecordAnnotation(bizModule = "acp:prisonEventType:list", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-所情事件类型列表",
            success = "实战平台-巡视管控-所情事件类型列表成功",
            fail = "实战平台-巡视管控-所情事件类型列表失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrisonEventTypeRespVO>> getPrisonEventTypeList(@Valid @RequestBody PrisonEventTypeListReqVO listReqVO) {
        return success(prisonEventTypeService.getPrisonEventTypeList(listReqVO));
    }

    @GetMapping("/changeStatus")
    @ApiOperation(value = "启用或禁用所情类型")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonEventType:changeStatus", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-value",
            success = "实战平台-巡视管控-value成功", fail = "实战平台-巡视管控-value失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> changeStatus(@RequestParam("id")String id) {
        return success(prisonEventTypeService.changeStatus(id));
    }
}
