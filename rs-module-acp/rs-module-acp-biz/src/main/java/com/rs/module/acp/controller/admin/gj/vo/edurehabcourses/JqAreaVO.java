package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "戒毒所有戒员的戒区信息")
@Data
public class JqAreaVO {

    private String areaId;

    private String areaName;

    @ApiModelProperty("时间段信息")
    private List<EdurehabCoursesTimeSlotRespVO> timeSlotList;

}
