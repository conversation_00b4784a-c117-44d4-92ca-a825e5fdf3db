package com.rs.module.acp.controller.admin.gj.vo.civilizedroom;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明监室登记明细列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedRoomDetailListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("文明监室登记ID")
    private String civilizedRoomId;

    @ApiModelProperty("room_id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("违规数")
    private Integer numberOfViolations;

    @ApiModelProperty("评选理由")
    private String selectionReason;

    @ApiModelProperty("附件地址")
    private String attUrl;

}
