package com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-协助破案新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssistanceSolveCaseFeedbackReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("签收部门类型")
    private String feedbackReceiptType;

    @ApiModelProperty("签收部门名称")
    private String feedbackReceiptName;

    @ApiModelProperty("签收日期")
    private Date feedbackReceiptTime;

    @ApiModelProperty("反馈日期")
    private Date feedbackTime;

    @ApiModelProperty("查证情况")
    private String feedbackCzqk;

    @ApiModelProperty("破获刑事/行政案件数量")
    private Long feedbackPhajsl;

    @ApiModelProperty("抓获违法犯罪嫌疑人数量")
    private Long feedbackZhwffzxyrsl;

    @ApiModelProperty("发现在逃人员数量")
    private Long feedbackFxztrysl;

    @ApiModelProperty("缴获赃款赃物数量")
    private Long feedbackJhzkzwsl;

    @ApiModelProperty("反馈人")
    private String feedbackUser;

    @ApiModelProperty("反馈人身份证号")
    private String feedbackUserSfzh;

    @ApiModelProperty("反馈新增时间")
    private Date feedbackUserTime;
}
