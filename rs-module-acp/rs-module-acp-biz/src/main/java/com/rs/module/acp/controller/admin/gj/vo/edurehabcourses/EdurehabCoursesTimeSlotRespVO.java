package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "时间段信息")
@Data
public class EdurehabCoursesTimeSlotRespVO {

    @ApiModelProperty("课程时段编码，如“9:00-10:00”，唯一标识时段")
    private String timeSlotCode;
    @ApiModelProperty("课程时段-开始")
    private String startTime;
    @ApiModelProperty("课程时段-结束")
    private String endTime;

    @ApiModelProperty("课程信息")
    private List<EdurehabCoursesRecordRespVO> listRecord;
}
