package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.alibaba.fastjson.JSONObject;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所属预警")
    private String warningId;
    @ApiModelProperty("警情地点")
    private String areaId;
    @ApiModelProperty("警情地点名称")
    private String areaName;
    @ApiModelProperty("发生时间")
    private Date happenTime;
    @ApiModelProperty("警情事件")
    private String eventType;
    @ApiModelProperty("警情级别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQDJ")
    private String eventLevel;
    @ApiModelProperty("设备类型ID")
    private String deviceTypeId;
    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;
    @ApiModelProperty("设备ID")
    private String deviceId;
    @ApiModelProperty("设备名称")
    private String deviceName;
    @ApiModelProperty("状态（3待核实、0待处置、5待办结（所领导审批）、1已办结）")
    private String status;
    @ApiModelProperty("警情描述")
    private String eventDetails;
    @ApiModelProperty("警情编码")
    private String eventCode;
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQLY")
    @ApiModelProperty("事件来源")
    private String eventSrc;
    @ApiModelProperty("处置情况")
    private String handleInfo;
    @ApiModelProperty("警情事件id")
    private String eventTypeId;
    @ApiModelProperty("事件开始时间（精确时间-开始）")
    private Date eventStartTime;
    @ApiModelProperty("事件结束时间（精确时间-结束）")
    private Date eventEndTime;
    @ApiModelProperty("巡控登记人身份证号")
    private String handleUserSfzh;
    @ApiModelProperty("巡控登记人")
    private String handleUserName;
    @ApiModelProperty("巡控登记时间")
    private Date handleTime;
    @ApiModelProperty("所领导审批人身份证号")
    private String auditUserSfzh;
    @ApiModelProperty("所领导审批人")
    private String auditUserName;
    @ApiModelProperty("所领导审批时间")
    private Date auditTime;
    @ApiModelProperty("所领导审批意见")
    private String auditOpinion;
    @ApiModelProperty("关联报警录像文件")
    private String eventVideo;
    @ApiModelProperty("所情事件类型大类ID")
    private String eventRootTypeId;
    @ApiModelProperty("所情事件类型大类名称")
    private String eventRootTypeName;
    @ApiModelProperty("附件地址")
    private String attUrl;
    @ApiModelProperty("截图照片")
    private String screenshotUrl;
    @ApiModelProperty("报警人姓名")
    private String reportPrisonerName;

    @ApiModelProperty("报警人列表")
    private List<PrisonEventPersonRespVO> reportPrisonerList;

    @ApiModelProperty("关联人员-在押人员列表")
    private List<PrisonEventPrisonerRespVO> inPrisonerList;

    @ApiModelProperty("关联人员-工作人员列表")
    private List<PrisonEventPersonRespVO> workPrisonerList;

    @ApiModelProperty("关联人员-外来人员列表")
    private List<PrisonEventPersonRespVO> outsiderPrisonerList;

    @ApiModelProperty("处置阶段")
    private List<JSONObject> handerStageList;

    @ApiModelProperty("处置阶段")
    private List<JSONObject> handerTrajectory;
}
