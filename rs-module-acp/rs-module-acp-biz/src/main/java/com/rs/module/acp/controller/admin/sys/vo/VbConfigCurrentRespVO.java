package com.rs.module.acp.controller.admin.sys.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-语音播报-即时配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VbConfigCurrentRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("播报名称")
    private String vbName;
    @ApiModelProperty("播报内容")
    private String content;
    @ApiModelProperty("播报次数")
    private Short vbNum;
    @ApiModelProperty("优先级")
    private Short priority;
    @ApiModelProperty("播报开始时间")
    private Date startTime;
    @ApiModelProperty("播报结束时间")
    private Date endTime;
    @ApiModelProperty("是否启用(0否1是)")
    private Short isEnabled;
    @ApiModelProperty("播报区域")
    private String vbArea;
}
