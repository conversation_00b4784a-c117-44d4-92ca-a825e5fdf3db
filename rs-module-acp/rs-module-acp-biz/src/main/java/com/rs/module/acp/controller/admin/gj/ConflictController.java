package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictFollowupSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediateHistoryRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictMediationSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegRespVO;
import com.rs.module.acp.controller.admin.gj.vo.conflict.ConflictRegSaveReqVO;
import com.rs.module.acp.service.gj.conflict.ConflictRegService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-社会矛盾化解登记")
@RestController
@RequestMapping("/acp/conflict")
@Validated
public class ConflictController {

    @Resource
    private ConflictRegService conflictRegService;

    @PostMapping("/create")
    @ApiOperation(value = "管教民警-社会矛盾化解登记")
    @LogRecordAnnotation(bizModule = "acp:conflict:create", operateType = LogOperateType.CREATE, title = "管教民警-社会矛盾化解登记",
            success = "管教业务-社会矛盾化解登记成功", fail = "管教业务-社会矛盾化解登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createConflict(@Valid @RequestBody ConflictRegSaveReqVO createReqVO) {
        return success(conflictRegService.createConflict(createReqVO));
    }

    @GetMapping("/getByEventCode")
    @ApiOperation(value = "查询矛盾详情通过事件编号或者事件ID")
    @ApiImplicitParam(name = "eventCode", value = "事件编号", required = true, paramType = "query")
    public CommonResult<ConflictRegRespVO> getConflictRegByEventCodeVO(@RequestParam("eventCode") String eventCode) {
        ConflictRegRespVO respVO = conflictRegService.getConflictRegByEventCodeVO(eventCode);
        return success(respVO);
    }

    @PostMapping("/deleteConflict")
    @ApiOperation(value = "管教民警-社会矛盾删除登记")
    @LogRecordAnnotation(bizModule = "acp:conflict:delete", operateType = LogOperateType.CREATE,
            title = "管教民警-社会矛盾删除登记",
            success = "管教业务-社会矛盾删除登记成功",
            fail = "管教业务-社会矛盾删除登记失败，错误信息：{{#_ret[msg]}}")
    @ApiImplicitParam(name = "ids", value = "事件ID，多个用逗号分隔", required = true)
    public CommonResult deleteConflict(@RequestParam("ids") String ids) {
        conflictRegService.deleteConflict(ids);
        return success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "管教民警-社会矛盾化解登记编辑")
    @LogRecordAnnotation(bizModule = "acp:conflict:update", operateType = LogOperateType.UPDATE, title = "管教民警-社会矛盾化解登记编辑",
            success = "管教业务-社会矛盾化解登记编辑成功", fail = "管教业务-社会矛盾化解登记编辑失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<String> updateConflict(@Valid @RequestBody ConflictRegSaveReqVO updateReqVO) {
        return success(conflictRegService.updateConflict(updateReqVO));
    }

    @PostMapping("/updateProcessStatus")
    @ApiOperation(value = "社会矛盾化解登记状态和流程修改")
    @LogRecordAnnotation(bizModule = "acp:conflict:updateProcessStatus", operateType = LogOperateType.UPDATE, title = "社会矛盾化解登记状态和流程修改",
            success = "社会矛盾化解登记状态和流程修改成功", fail = "社会矛盾化解登记状态和流程修改失败，错误信息：{{#_ret[msg]}}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "eventCode", value = "事件编号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "regStatus", value = "登记状态", required = true, paramType = "query"),
            @ApiImplicitParam(name = "actInstId", value = "流程实例ID", required = false, paramType = "query")
    })
    public CommonResult<Boolean> updateProcessStatus(@NotBlank(message = "事件编号不能为空") @RequestParam(value = "eventCode") String eventCode,
                                                     @NotBlank(message = "登记状态不能为空") @RequestParam(value = "regStatus") String regStatus,
                                                     @RequestParam(value = "actInstId", required = false) String actInstId) {
        return success(conflictRegService.updateProcessStatus(eventCode, regStatus, actInstId));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "所领导审批-社会矛盾化解登记")
    @LogRecordAnnotation(bizModule = "acp:conflict:leaderApprove", operateType = LogOperateType.UPDATE, title = "所领导审批-社会矛盾化解登记",
            success = "所领导审批-社会矛盾化解登记成功", fail = "所领导审批-社会矛盾化解登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody ConflictRegApproveReqVO approveReqVO) {
        conflictRegService.leaderApprove(approveReqVO);
        return success(true);
    }

    @PostMapping("/addMediateInfo")
    @ApiOperation(value = "管教民警-社会矛盾调解情况录入")
    @LogRecordAnnotation(bizModule = "acp:conflict:addMediateInfo", operateType = LogOperateType.CREATE, title = "管教民警-社会矛盾化解情况录入",
            success = "管教业务-社会矛盾化解情况录入成功", fail = "管教业务-社会矛盾化解情况录入失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#mediateInfoReqVO}}")
    public CommonResult addMediateInfo(@Valid @RequestBody ConflictMediationSaveReqVO mediateInfoReqVO) {
        conflictRegService.addMediateInfo(mediateInfoReqVO);
        return success(true);
    }

    @GetMapping("/getMediateHistory")
    @ApiOperation(value = "管教民警-社会矛盾查询调解历史")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "eventCode", value = "事件编号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "记录数", required = true, paramType = "query")
    })
    public CommonResult<List<ConflictMediateHistoryRespVO>> getMediateHistory(
            @NotBlank(message = "事件编号不能为空")
            @RequestParam(value = "eventCode") String eventCode,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        List<ConflictMediateHistoryRespVO> historyList = conflictRegService.getMediateHistory(eventCode, pageSize);
        return success(historyList);
    }

    @PostMapping("/policeConfirm")
    @ApiOperation(value = "管教民警-民警确认")
    @ApiImplicitParam(name = "id", value = "社会矛盾ID", required = true, paramType = "query")
    @LogRecordAnnotation(bizModule = "acp:conflict:policeConfirm", operateType = LogOperateType.UPDATE,
            title = "管教民警-民警确认", bizNo = "{{#id}}",
            success = "管教业务-民警确认成功", fail = "管教业务-民警确认失败，错误信息：{{#_ret[msg]}}")
    public CommonResult<Boolean> policeConfirm(@RequestParam("id") String id) {
        conflictRegService.policeConfirm(id);
        return success(true);
    }

    @PostMapping("/returnAndResign")
    @ApiOperation(value = "管教民警-民警退回在押人员签名")
    @ApiImplicitParam(name = "id", value = "社会矛盾关联人员ID", required = true, paramType = "query")
    @LogRecordAnnotation(bizModule = "acp:conflict:returnAndResign", operateType = LogOperateType.UPDATE,
            title = "管教民警-民警退回在押人员签名", bizNo = "{{#id}}",
            success = "管教业务-民警退回在押人员签名成功", fail = "管教业务-民警退回在押人员签名失败，错误信息：{{#_ret[msg]}}")
    public CommonResult<Boolean> returnAndResign(@RequestParam("id") String id) {
        conflictRegService.returnAndResign(id);
        return success(true);
    }

    @PostMapping("/createFollowUp")
    @ApiOperation(value = "管教民警-社会矛盾化解回访登记")
    @LogRecordAnnotation(bizModule = "acp:conflict:createFollowUp", operateType = LogOperateType.CREATE,
            title = "管教民警-社会矛盾化解回访登记",
            success = "管教业务-社会矛盾化解回访登记成功",
            fail = "管教业务-社会矛盾化解回访登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createFollowUp(@Valid @RequestBody ConflictFollowupSaveReqVO createReqVO) {
        return success(conflictRegService.createFollowUp(createReqVO));
    }

    @PostMapping("/back")
    @ApiOperation(value = "管教民警-社会矛盾化退回在押人员申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "eventCode", value = "事件编号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pcId", value = "消息ID", required = true, paramType = "query"),
            @ApiImplicitParam(name = "jgrybm", value = "监管人编号", required = true, paramType = "query")
    })
    public CommonResult back(@RequestParam("pcId") String pcId, @RequestParam("eventCode") String eventCode,
                             @RequestParam("jgrybm") String jgrybm) {
        conflictRegService.back(pcId, eventCode, jgrybm);
        return success();
    }

}
