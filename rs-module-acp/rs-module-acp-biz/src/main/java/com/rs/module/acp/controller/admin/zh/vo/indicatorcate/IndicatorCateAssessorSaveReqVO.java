package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类与考评人关联新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorCateAssessorSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("指标分类ID")
    //@NotEmpty(message = "指标分类ID不能为空")
    private String indicatorCateId;

    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;

    @ApiModelProperty("考核人姓名")
    private String assessorName;

}
