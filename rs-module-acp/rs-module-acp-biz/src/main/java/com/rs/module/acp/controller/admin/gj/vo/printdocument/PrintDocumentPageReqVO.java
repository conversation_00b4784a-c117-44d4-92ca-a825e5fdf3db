package com.rs.module.acp.controller.admin.gj.vo.printdocument;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文书打印预览分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrintDocumentPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("关联id")
    private String glId;

    @ApiModelProperty("关联类型")
    private String glType;

    @ApiModelProperty("文书打印其他信息")
    private String wsdata;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
