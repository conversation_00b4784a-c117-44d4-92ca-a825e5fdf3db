package com.rs.module.acp.controller.admin.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckRespVO;
import com.rs.module.acp.controller.admin.gj.vo.EquipmentUseCheckSaveReqVO;
import com.rs.module.acp.entity.gj.EquipmentUseCheckDO;
import com.rs.module.acp.service.gj.EquipmentUseCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.Date;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "戒具检查")
@RestController
@RequestMapping("/acp/gj/equipmentUse/check")
@Validated
public class EquipmentUseCheckController {

    @Resource
    private EquipmentUseCheckService equipmentUseCheckService;


    @PostMapping("/update")
    @ApiOperation(value = "更新戒具检查信息")
    public CommonResult<Boolean> updateEquipmentUseCheck(@Valid @RequestBody EquipmentUseCheckSaveReqVO updateReqVO) {
        equipmentUseCheckService.updateEquipmentUseCheck(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得戒具检查信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EquipmentUseCheckRespVO> getEquipmentUseCheck(@RequestParam("id") String id) {
        EquipmentUseCheckDO equipmentUseCheck = equipmentUseCheckService.getEquipmentUseCheck(id);
        return success(BeanUtils.toBean(equipmentUseCheck, EquipmentUseCheckRespVO.class));
    }

    @GetMapping("/getCheckList")
    @ApiOperation(value = "获得待戒具检查列表")
    @ApiImplicitParam(name = "useId", value = "戒具使用id")
    public CommonResult<List<EquipmentUseCheckRespVO>> getCheckList(@RequestParam("useId") String useId) {
        return success(equipmentUseCheckService.getCheckList(useId));
    }


    @GetMapping("/getCheckRecordList")
    @ApiOperation(value = "获得戒具检查记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roomCode", value = "监室号"),
            @ApiImplicitParam(name = "startTime", value = "开始时间"),
            @ApiImplicitParam(name = "endTime", value = "结束时间")
    })
    public CommonResult<List<EquipmentUseCheckRespVO>> getCheckRecordList(@RequestParam("roomCode") String roomCode,
                                                                          @RequestParam(name = "startTime", required = false) Date startTime,
                                                                          @RequestParam(name = "endTime", required = false) Date endTime) {
        return success(equipmentUseCheckService.getCheckRecordList(roomCode, startTime, endTime));
    }
}
