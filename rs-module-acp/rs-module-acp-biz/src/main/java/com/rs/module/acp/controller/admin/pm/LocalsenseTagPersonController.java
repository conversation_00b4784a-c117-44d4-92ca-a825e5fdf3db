package com.rs.module.acp.controller.admin.pm;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.util.QingyanLocalsenseHttpUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.LocalsenseTagPersonDO;
import com.rs.module.acp.service.pm.LocalsenseTagPersonService;

@Api(tags = "实战平台-监管管理-定位标签与人员绑定")
@RestController
@RequestMapping("/acp/pm/localsenseTagPerson")
@Validated
public class LocalsenseTagPersonController {

    @Resource
    private LocalsenseTagPersonService localsenseTagPersonService;
    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-定位标签与人员绑定")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LocalsenseTagPersonRespVO> getLocalsenseTagPerson(@RequestParam("id") String id) {
        LocalsenseTagPersonDO localsenseTagPerson = localsenseTagPersonService.getLocalsenseTagPerson(id);
        return success(BeanUtils.toBean(localsenseTagPerson, LocalsenseTagPersonRespVO.class));
    }
    @PostMapping("/rsCreate")
    @ApiOperation("入所被监管人员绑定标签(手环)")
    public CommonResult<String> rsCreateLocalsenseTagPerson(@Valid @RequestBody LocalsenseTagPersonRsSaveReqVO reqVO) {
        try{
            return success(localsenseTagPersonService.rsCreateLocalsenseTagPerson(reqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @PostMapping("/znwdCreate")
    @ApiOperation("智能腕带模块绑定被监管人员")
    public CommonResult<String> znwdCreateLocalsenseTagPerson(@Valid @RequestBody LocalsenseTagPersonZnwdSaveReqVO reqVO) {
        try{
            return success(localsenseTagPersonService.znwdCreateLocalsenseTagPerson(reqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @PostMapping("/mjCreate")
    @ApiOperation("民警绑定工牌")
    public CommonResult<String> mjCreateLocalsenseTagPerson(@Valid @RequestBody LocalsenseTagPersonMjSaveReqVO reqVO) {
        try{
            return success(localsenseTagPersonService.mjCreateLocalsenseTagPerson(reqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @GetMapping("/getByPersonId")
    @ApiOperation("根据人员ID查询绑定信息")
    @ApiImplicitParam(name = "bindPersonId", value = "人员ID", required = true)
    public CommonResult<List<LocalsenseTagPersonRespVO>> getByPersonId(@RequestParam String bindPersonIds) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonByBindPersonIds(bindPersonIds);
        return success(BeanUtils.toBean(list, LocalsenseTagPersonRespVO.class));
    }

    @GetMapping("/getByTagId")
    @ApiOperation("根据标签ID查询绑定信息")
    @ApiImplicitParam(name = "tagId", value = "标签ID", required = true)
    public CommonResult<List<LocalsenseTagPersonRespVO>> getByTagId(@RequestParam String tagIds) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonByTagIds(tagIds);
        return success(BeanUtils.toBean(list, LocalsenseTagPersonRespVO.class));
    }

    @PostMapping("/unbind")
    @ApiOperation("解绑标签")
    public CommonResult<String> unbind(@Valid @RequestBody LocalsenseTagPersonUnbindSaveReqVO reqVO) {
        try{
            return success(localsenseTagPersonService.unbind(reqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @GetMapping("/historyByPerson")
    @ApiOperation("查询人员所有历史绑定记录")
    @ApiImplicitParam(name = "bindPersonId", value = "人员ID", required = true)
    public CommonResult<List<LocalsenseTagPersonRespVO>> historyByPerson(@RequestParam String bindPersonId) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonHistoryList(bindPersonId);
        return success(BeanUtils.toBean(list, LocalsenseTagPersonRespVO.class));
    }

    @GetMapping("/historyByTag")
    @ApiOperation("查询标签所有绑定记录")
    @ApiImplicitParam(name = "tagId", value = "标签ID", required = true)
    public CommonResult<List<LocalsenseTagPersonRespVO>> historyByTag(@RequestParam String tagId) {
        List<LocalsenseTagPersonDO> list = localsenseTagPersonService.getLocalsenseTagPersonHistoryListByTagId(tagId);
        return success(BeanUtils.toBean(list, LocalsenseTagPersonRespVO.class));
    }
    //查询手环告警信息
    @GetMapping("/getAlarmByParam")
    @ApiOperation("查询手环告警信息")
    public CommonResult<JSONObject> getAlarmByParam(@RequestParam(required = false) String alarmType,
                                             @RequestParam(required = false) String areaId,
                                             @RequestParam(required = false) String beginTime,
                                             @RequestParam(required = false) String endTime,
                                             @RequestParam(required = false) Integer num,
                                             @RequestParam(required = false) Integer page,
                                             @RequestParam(required = false) String state) {
        try {
            return success(new QingyanLocalsenseHttpUtil().getAlarmByParam(alarmType,areaId,beginTime,endTime,num,page,state));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @GetMapping("/getBatteryByPersonId")
    @ApiOperation("查询标签电量,体征信息")
    public CommonResult<List<JSONObject>> getBatteryByPersonId(@RequestParam String tagIds) {
        try {
            return success(localsenseTagPersonService.getLocalSenseInfo(tagIds));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
}
