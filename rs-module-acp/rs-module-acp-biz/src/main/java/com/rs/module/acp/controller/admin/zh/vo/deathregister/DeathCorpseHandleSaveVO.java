package com.rs.module.acp.controller.admin.zh.vo.deathregister;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * 死亡登记-尸体处理
 */
@Data
public class DeathCorpseHandleSaveVO {

    @NotEmpty(message = "ID不能为空")
    @ApiModelProperty(value = "id", required = true)
    private String id;
    @ApiModelProperty(value = "保存类型 0 保存,1 提交",hidden = true)
    private String saveType;
    /** 尸体处理经办时间 */
    //private Date corpseHandleCreateTime ;
    /** 尸体处理经办人，对应permission_user.userid */
    //private Integer corpseHandleCreateUserId ;
    /** 尸体处理经办人名称 */
    //private String corpseHandleCreateUserName ;

    @ApiModelProperty("尸体处理情况")
    private String corpseHandleSituation ;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "尸体处理时间", required = true)
    private Date corpseHandleTime;
}
