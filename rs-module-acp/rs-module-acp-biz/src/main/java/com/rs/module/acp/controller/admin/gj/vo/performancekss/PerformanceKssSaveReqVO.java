package com.rs.module.acp.controller.admin.gj.vo.performancekss;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-人员表现鉴定表-看守所新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceKssSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("接收单位")
    private String jsdw;

    @ApiModelProperty("移送因由")
    private String ysyy;

    @ApiModelProperty("其他情况")
    private String qtqk;

    @ApiModelProperty("身体状况，健康：1  不健康：2")
    private String healthStzk;

    @ApiModelProperty("外伤情况，无：1  有：2")
    private String healthWsqk;

    @ApiModelProperty("重大疾病，无：1  有：2")
    private String healthZdjb;

    @ApiModelProperty("精神状况，良好：1  不好：2")
    private String healthJszk;

    @ApiModelProperty("重大疾病及出所住院病因")
    private String healthZdjbjcszyyy;

    @ApiModelProperty("所规所纪制度，遵守：1  不遵守：2")
    private String performanceSgsjzd;

    @ApiModelProperty("一日生活管理，服从：1  不服从：2")
    private String performanceYrshgl;

    @ApiModelProperty("自伤自残行为或倾向，无：1  有：2")
    private String performanceZszcxwhqx;

    @ApiModelProperty("殴打他人行为，无：1  有：2")
    private String performanceOdtrxw;

    @ApiModelProperty("曾列为重大安全风险情况-一般， 被勾选：1 取消勾选2")
    private String performanceZdaqfxqk1;

    @ApiModelProperty("曾列为重大安全风险情况-三级， 被勾选：1 取消勾选2")
    private String performanceZdaqfxqk2;

    @ApiModelProperty("曾列为重大安全风险情况-二级， 被勾选：1 取消勾选2")
    private String performanceZdaqfxqk3;

    @ApiModelProperty("曾列为重大安全风险情况-一级， 被勾选：1 取消勾选2")
    private String performanceZdaqfxqk4;

    @ApiModelProperty("家属姓名及电话")
    private String familyXmjdh;

    @ApiModelProperty("羁押期间联系家属情况， 联系过：1 未联系：2")
    private String familyJyqjlxjsqk;

}
