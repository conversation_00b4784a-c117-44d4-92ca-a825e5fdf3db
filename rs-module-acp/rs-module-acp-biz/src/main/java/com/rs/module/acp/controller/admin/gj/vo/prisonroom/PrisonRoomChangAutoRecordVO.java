package com.rs.module.acp.controller.admin.gj.vo.prisonroom;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 禁闭-单独关押插入-自动生成
 * <AUTHOR>
 * @date 2025/6/15 14:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonRoomChangAutoRecordVO extends BaseVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("旧监室id")
    @NotEmpty(message = "旧监室id不能为空")
    private String oldRoomId;

    @ApiModelProperty("新监室ID")
    @NotEmpty(message = "新监室ID不能为空")
    private String newRoomId;

    @ApiModelProperty("旧监室名称")
    @NotEmpty(message = "旧监室名称不能为空")
    private String oldRoomName;

    @ApiModelProperty("新监室名称")
    @NotEmpty(message = "新监室名称不能为空")
    private String newRoomName;

    @ApiModelProperty("调整原因")
    @NotEmpty(message = "调整原因不能为空")
    private String changeReason;

    @ApiModelProperty("监室调整时间")
    @NotNull(message = "监室调整时间不能为空")
    private Date roomChangeTime;

    //@ApiModelProperty("是否调整（1：已调整，0：未调整）")
    //private Short isChange;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("来源业务ID")
    private String sourceBusinessId;
    @ApiModelProperty("类型 0802:单独关押,0801:禁闭")
    private String businessType;
}
