package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("指标名称")
    private String indicatorName;
    @ApiModelProperty("指标类型ID")
    private String indicatorCateId;
    @ApiModelProperty("指标类型名称")
    private String indicatorCateName;
    @ApiModelProperty("指标类型，01：主观分指标，02：加减分指标")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_ZBLX")
    private String indicatorType;
    @ApiModelProperty("排序序号")
    private Integer sortOrder;
    @ApiModelProperty("是否启用")
    private Short isEnabled;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("子指标")
    private List<IndicatorSubRespVO> subList;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
