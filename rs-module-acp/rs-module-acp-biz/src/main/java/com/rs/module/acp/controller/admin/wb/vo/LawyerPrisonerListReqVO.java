package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师关联被监管人员列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerPrisonerListReqVO extends BaseVO  {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("律师表ID")
    private String lawyerId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("委托类型")
    private String entrustType;

    @ApiModelProperty("委托阶段")
    private String entrustStage;

    @ApiModelProperty("委托人")
    private String principal;

    @ApiModelProperty("委托人证件号")
    private String principalId;

    @ApiModelProperty("委托书类型")
    private String powerOfAttorneyType;

    @ApiModelProperty("委托书存储路径")
    private String powerOfAttorneyUrl;

    @ApiModelProperty("介绍信编号")
    private String letterNumber;

    @ApiModelProperty("会见批准机关")
    private String meetingApprovalAuthority;

    @ApiModelProperty("委托状态")
    private String status;

}
