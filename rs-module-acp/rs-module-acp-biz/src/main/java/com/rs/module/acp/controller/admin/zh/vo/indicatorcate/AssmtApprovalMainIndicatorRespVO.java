package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmtApprovalMainIndicatorRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所属主指标ID")
    private String mainIndicatorId;
    @ApiModelProperty("指标名称")
    private String indicatorName;

    @ApiModelProperty("子指标名称")
    private List<AssmttApprovalRecordRespVO> subIndicatorList;
}
