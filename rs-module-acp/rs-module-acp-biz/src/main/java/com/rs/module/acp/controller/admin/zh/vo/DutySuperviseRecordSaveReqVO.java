package com.rs.module.acp.controller.admin.zh.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-值班管理-值班督导记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutySuperviseRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("排班模板ID")
//    @NotEmpty(message = "排班模板ID不能为空")
    private String staffDutyTemplateId;

    @ApiModelProperty("值班岗位ID")
//    @NotEmpty(message = "值班岗位ID不能为空")
    private String staffDutyPostId;

    @ApiModelProperty("值班模板关联角色ID")
//    @NotEmpty(message = "值班模板关联角色ID不能为空")
    private String staffDutyRoleId;

    @ApiModelProperty("值班日期")
//    @NotNull(message = "值班日期不能为空")
    private Date dutyDate;

    @ApiModelProperty("签到验证模式")
//    @NotEmpty(message = "签到验证模式不能为空")
    private String signinValidMode;

    @ApiModelProperty("签到状态")
//    @NotEmpty(message = "签到状态不能为空")
    private String signinStatus;

    @ApiModelProperty("签到时间")
    private Date signinTime;

}
