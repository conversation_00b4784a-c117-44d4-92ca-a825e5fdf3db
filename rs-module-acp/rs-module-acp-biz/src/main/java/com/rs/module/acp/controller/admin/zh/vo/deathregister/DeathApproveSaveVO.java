package com.rs.module.acp.controller.admin.zh.vo.deathregister;

import com.rs.module.acp.controller.admin.db.vo.ApprovalRespVO;
import com.rs.module.base.vo.ApproveReqVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 死亡登记-审批
 */
@Data
public class DeathApproveSaveVO extends ApproveReqVO {
}
