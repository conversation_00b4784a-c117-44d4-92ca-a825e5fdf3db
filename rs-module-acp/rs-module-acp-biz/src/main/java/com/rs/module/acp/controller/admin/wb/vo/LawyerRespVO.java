package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_XB")
    private String xb;
    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_ZJZL")
    private String zjlx;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("联系方式")
    private String lxfs;
    @ApiModelProperty("律师类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_LSLX")
    private String lslx;
    @ApiModelProperty("执业证号码")
    private String zyzhm;
    @ApiModelProperty("执业证有效期")
    private Date ksZyzyxq;
    @ApiModelProperty("执业证有效期-结束")
    private Date jsZyzyxq;
    @ApiModelProperty("律师单位")
    private String lsdw;
    @ApiModelProperty("照片存储url")
    private String zpUrl;
    @ApiModelProperty("执业证书url")
    private String zyzsUrl;
}
