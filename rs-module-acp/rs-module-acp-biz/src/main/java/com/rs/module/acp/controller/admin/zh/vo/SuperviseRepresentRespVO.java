package com.rs.module.acp.controller.admin.zh.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-督导申诉 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SuperviseRepresentRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("申诉单位编号")
    private String orgCode;
    @ApiModelProperty("申诉单位名称")
    private String orgName;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("申诉人身份证号")
    private String userIdCard;
    @ApiModelProperty("申诉人姓名")
    private String userName;
    @ApiModelProperty("申诉时间")
    private Date time;
    @ApiModelProperty("申诉理由")
    private String reason;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("督导id")
    private String superviseId;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("附件信息")
    private String attachment;
    @ApiModelProperty("是否具有审批权限")
    private Boolean isApprove;
    @ApiModelProperty("当前审批人身份证号")
    private String approvalUserSfzh;
    @ApiModelProperty("当前审批人姓名")
    private String approvalUserName;
}
