package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-领事外事会见同行登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsularMeetingCompanionRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("领事会见ID")
    private String consularMeetingId;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_XB")
    private String gender;
    @ApiModelProperty("证件号码")
    private String idCard;
    @ApiModelProperty("同行人类别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_TXRLB")
    private String companionType;
    @ApiModelProperty("附件URL")
    private String attachmentUrl;
}
