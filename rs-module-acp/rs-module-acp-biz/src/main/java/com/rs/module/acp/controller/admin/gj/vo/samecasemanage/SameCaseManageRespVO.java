package com.rs.module.acp.controller.admin.gj.vo.samecasemanage;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-同案人员管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SameCaseManageRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("案件编号")
    private String ajbh;
    @ApiModelProperty("同案人监管人员编码")
    private String sameCaseJgrybm;
}
