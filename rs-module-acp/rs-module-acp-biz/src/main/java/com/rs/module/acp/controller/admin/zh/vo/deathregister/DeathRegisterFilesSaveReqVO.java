package com.rs.module.acp.controller.admin.zh.vo.deathregister;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "实战平台-综合管理-死亡登记文件新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeathRegisterFilesSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("对应id")
    private String deathRegisterId;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件地址")
    private String fileUrl;

    @ApiModelProperty("文件后缀")
    private String fileSuffix;

    @ApiModelProperty("死亡鉴定文件类型 字典 ZD_ZHGL_SWWJLX")
    private String deathAppraiseFileType;

}
