package com.rs.module.acp.controller.admin.gj.vo.biometriccomp;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-生物特征比对新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricCompFkReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键不能为空")
    private String id;

    @ApiModelProperty("是否比中 字典：ZD_SWTZBD_BDZT")
    @NotEmpty(message = "是否比中不能为空")
    private String isComp;

    @ApiModelProperty("比中案件编号")
    private String compCaseNo;

    @ApiModelProperty("比中案件类型 字典：ZD_GABBZ_XZAJAY")
    private String compCaseType;

    @ApiModelProperty("比中处置情况")
    private String disposalSituation;


}
