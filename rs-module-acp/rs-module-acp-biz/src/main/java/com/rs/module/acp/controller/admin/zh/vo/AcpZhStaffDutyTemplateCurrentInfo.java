package com.rs.module.acp.controller.admin.zh.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;
import java.util.Date;

@Data
@ApiModel("当前值班信息")
public class AcpZhStaffDutyTemplateCurrentInfo {

    @ApiModelProperty("模板ID")
    private String staffDutyTemplateId;

    @ApiModelProperty("组织机构代码")
    private String orgCode;

    @ApiModelProperty("排班日期")
    private Date dutyDate;

    @ApiModelProperty("排班记录ID")
    private String recordId;

    @ApiModelProperty("岗位名称")
    private String postName;

    @ApiModelProperty("角色ID")
    private String postId;

    @ApiModelProperty("值班角色名称")
    private String dutyRoleName;

    @ApiModelProperty("班次")
    private String dutyShift;

    @ApiModelProperty("开始时间")
    private LocalTime dutyTimeBegin;

    @ApiModelProperty("结束时间")
    private LocalTime dutyTimeEnd;

    @ApiModelProperty("开始时间类型")
    private String dutyTimeTypeBegin;

    @ApiModelProperty("结束时间类型")
    private String dutyTimeTypeEnd;

    @ApiModelProperty("值班角色ID")
    private String dutyRoleId;

    @ApiModelProperty("岗位ID（扩展字段）")
    private String staffDutyPostId;

    @ApiModelProperty("值班角色明细ID")
    private String staffDutyRoleId;
}
