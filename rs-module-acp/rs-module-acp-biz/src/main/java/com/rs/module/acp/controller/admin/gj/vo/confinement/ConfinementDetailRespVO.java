package com.rs.module.acp.controller.admin.gj.vo.confinement;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.controller.admin.gj.vo.inoutrecords.InOutRecordsRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-禁闭登记 Response VO")
@Data
public class ConfinementDetailRespVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("呈批信息")
    private ConfinementRegRespVO regRespVO;

    @ApiModelProperty("延长信息")
    private List<ConfinementExtendRespVO> extendRespVOList;
    @ApiModelProperty("解除信息")
    private ConfinementRemoveRespVO  removeRespVO;
    @ApiModelProperty("业务轨迹信息")
    List<ConfinementFlowApproveTrackVO> trackList;
    @ApiModelProperty("呈批登记信息")
    private List<InOutRecordsRespVO>  regInOutRecordsRespVOList;
    @ApiModelProperty("解除登记信息带入记录")
    private InOutRecordsRespVO  removeInOutRecordsRespVO;
}
