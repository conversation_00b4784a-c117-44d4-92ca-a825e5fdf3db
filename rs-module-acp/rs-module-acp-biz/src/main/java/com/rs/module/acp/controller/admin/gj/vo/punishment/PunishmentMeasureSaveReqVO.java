package com.rs.module.acp.controller.admin.gj.vo.punishment;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-处罚呈批关联措施新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentMeasureSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("处罚呈批ID")
    private String punishmentId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("处罚措施 字典：ZD_GJCFNR")
    @NotEmpty(message = "处罚措施不能为空")
    private String measures;

    @ApiModelProperty("处罚周期开始日期")
    @NotNull(message = "处罚周期开始日期不能为空")
    private Date startDate;

    @ApiModelProperty("处罚周期结束日期")
    @NotNull(message = "处罚周期结束日期不能为空")
    private Date endDate;

    @ApiModelProperty("处罚时长，自动生成（天-时-分钟）")
    @NotNull(message = "处罚时长，自动生成（天-时-分钟）不能为空")
    private Integer duration;

    @ApiModelProperty("处罚业务ID")
    @NotEmpty(message = "处罚业务ID不能为空")
    private String businessId;

}
