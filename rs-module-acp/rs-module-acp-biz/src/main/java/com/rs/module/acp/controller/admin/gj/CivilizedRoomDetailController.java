package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailRespVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomDetailSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.CivilizedRoomDetailDO;
import com.rs.module.acp.service.gj.civilizedroom.CivilizedRoomDetailService;

@Api(tags = "实战平台-管教业务-文明监室登记明细")
@RestController
@RequestMapping("/acp/gj/civilizedRoomDetail")
@Validated
public class CivilizedRoomDetailController {

    @Resource
    private CivilizedRoomDetailService civilizedRoomDetailService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-文明监室登记明细")
    public CommonResult<String> createCivilizedRoomDetail(@Valid @RequestBody CivilizedRoomDetailSaveReqVO createReqVO) {
        return success(civilizedRoomDetailService.createCivilizedRoomDetail(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-文明监室登记明细")
    public CommonResult<Boolean> updateCivilizedRoomDetail(@Valid @RequestBody CivilizedRoomDetailSaveReqVO updateReqVO) {
        civilizedRoomDetailService.updateCivilizedRoomDetail(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-文明监室登记明细")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCivilizedRoomDetail(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           civilizedRoomDetailService.deleteCivilizedRoomDetail(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-文明监室登记明细")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CivilizedRoomDetailRespVO> getCivilizedRoomDetail(@RequestParam("id") String id) {
        CivilizedRoomDetailDO civilizedRoomDetail = civilizedRoomDetailService.getCivilizedRoomDetail(id);
        return success(BeanUtils.toBean(civilizedRoomDetail, CivilizedRoomDetailRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-文明监室登记明细分页")
    public CommonResult<PageResult<CivilizedRoomDetailRespVO>> getCivilizedRoomDetailPage(@Valid @RequestBody CivilizedRoomDetailPageReqVO pageReqVO) {
        PageResult<CivilizedRoomDetailDO> pageResult = civilizedRoomDetailService.getCivilizedRoomDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CivilizedRoomDetailRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-文明监室登记明细列表")
    public CommonResult<List<CivilizedRoomDetailRespVO>> getCivilizedRoomDetailList(@Valid @RequestBody CivilizedRoomDetailListReqVO listReqVO) {
        List<CivilizedRoomDetailDO> list = civilizedRoomDetailService.getCivilizedRoomDetailList(listReqVO);
        return success(BeanUtils.toBean(list, CivilizedRoomDetailRespVO.class));
    }
}
