package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomEvalSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomExtendSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomRespVO;
import com.rs.module.acp.controller.admin.gj.vo.transitionroom.TransitionRoomSaveReqVO;
import com.rs.module.acp.service.gj.transitionroom.TransitionRoomEvalService;
import com.rs.module.acp.service.gj.transitionroom.TransitionRoomExtendService;
import com.rs.module.acp.service.gj.transitionroom.TransitionRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-过渡监室管理")
@RestController
@RequestMapping("/acp/gj/transitionRoom")
@Validated
public class TransitionRoomController {

    @Resource
    private TransitionRoomService transitionRoomService;

    @Resource
    private TransitionRoomExtendService transitionRoomExtendService;

    @Resource
    private TransitionRoomEvalService transitionRoomEvalService;


    @PostMapping("/create")
    @ApiOperation(value = "管教业务-收押岗新增过渡监室记录")
    public CommonResult<String> createTransitionRoom(@Valid @RequestBody TransitionRoomSaveReqVO createReqVO) {
        return success(transitionRoomService.createTransitionRoom(createReqVO));
    }

    @PostMapping("/createExtend")
    @ApiOperation(value = "管教业务-过渡监室延长呈批")
    @LogRecordAnnotation(bizModule = "acp:createTransitionRoomExtend:update", operateType = LogOperateType.CREATE, title = "管教业务-过渡监室管理-延长呈批",
            success = "管教业务-过渡监室管理-延长呈批-成功", fail = "管教业务-信息员管理-信息编辑失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createTransitionRoomExtend(@Valid @RequestBody TransitionRoomExtendSaveReqVO createReqVO) {
        return success(transitionRoomExtendService.createTransitionRoomExtend(createReqVO));
    }

    @PostMapping("/apprExtend")
    @ApiOperation(value = "管教业务-过渡监室延长领导审批")
    @LogRecordAnnotation(bizModule = "acp:apprTransitionRoomExtend:update", operateType = LogOperateType.UPDATE, title = "管教业务-过渡监室管理-延长呈批",
            success = "管教业务-过渡监室管理-延长呈批-成功", fail = "管教业务-过渡监室管理-延长审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> apprTransitionRoomExtend(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        boolean flag = transitionRoomExtendService.apprTransitionRoomExtend(approveReqVO);
        return success(flag);
    }

    @PostMapping("/regAssessmentInfo")
    @ApiOperation(value = "管教业务-过渡监室过渡考核登记")
    @LogRecordAnnotation(bizModule = "acp:apprTransitionRoomExtend:regAssessmentInfo", operateType = LogOperateType.CREATE, title = "管教业务-过渡监室管理-延长呈批",
            success = "管教业务-过渡监室管理-延长呈批-成功", fail = "管教业务-过渡监室管理-延长审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> regAssessmentInfo(@Valid @RequestBody TransitionRoomEvalSaveReqVO createReqVO) {
        boolean flag = transitionRoomEvalService.regAssessmentInfo(createReqVO);
        return success(flag);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-过渡监室管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<TransitionRoomRespVO> getTransitionRoom(@RequestParam("id") String id) {
        TransitionRoomRespVO transitionRoom = transitionRoomService.getTransitionRoom(id);
        return success(transitionRoom);
    }


}
