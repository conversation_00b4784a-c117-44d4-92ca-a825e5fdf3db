package com.rs.module.acp.controller.admin.gj.vo.doublecheck;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.common.annotation.Format;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-双重检查登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DoubleCheckRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监室id")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("检查结果")
    @Trans(type = TransType.DICTIONARY, key = "ZD_PASS_TYPE")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;
    @ApiModelProperty("体表检查登记")
    private String physicalExam;
    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;
    @ApiModelProperty("经办民警")
    private String operatePolice;
    @ApiModelProperty("经办时间")
    private Date operateTime;

    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String xb;
    @ApiModelProperty("年龄")
    private String nl;

}
