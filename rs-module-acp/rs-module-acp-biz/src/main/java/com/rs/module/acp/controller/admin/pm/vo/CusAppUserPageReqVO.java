package com.rs.module.acp.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-我的应用配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CusAppUserPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("系统ID")
    private String systemId;

    @ApiModelProperty("用户证件号码")
    private String userIdCard;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
