package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorCateListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("指标类型名称")
    private String typeName;

    @ApiModelProperty("初始化分值")
    private Integer initScore;

    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;

    @ApiModelProperty("考核人姓名")
    private String assessorName;

    @ApiModelProperty("被考核对象类型，01:岗位、02：角色、03：用户")
    private String assessedObjectType;

    @ApiModelProperty("被考核对象ID")
    private String assessedObjectId;

    @ApiModelProperty("被考核对象名称")
    private String assessedObjectName;

    @ApiModelProperty("排序序号")
    private Integer sortOrder;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("机构编码")
    private String orgCode;

}
