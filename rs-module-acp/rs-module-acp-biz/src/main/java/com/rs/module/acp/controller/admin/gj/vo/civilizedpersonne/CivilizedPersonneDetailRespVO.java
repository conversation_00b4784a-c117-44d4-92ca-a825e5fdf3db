package com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明个人登记明细 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedPersonneDetailRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("文明个人登记ID")
    private String civilizedPersonneId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("room_id")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("奖励次数")
    private Integer numberOfReward;
    @ApiModelProperty("违规次数")
    private Integer numberOfViolations;
    @ApiModelProperty("惩罚次数")
    private Integer numberOfPunishment;
    @ApiModelProperty("评选理由")
    private String selectionReason;
    @ApiModelProperty("附件地址")
    private String attUrl;
    @ApiModelProperty("人员信息")
    public PrisonerVwRespVO prisonerVwRespVO;

}
