package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类与考评人关联列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorCateAssessorListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("指标分类ID")
    private String indicatorCateId;

    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;

    @ApiModelProperty("考核人姓名")
    private String assessorName;

}
