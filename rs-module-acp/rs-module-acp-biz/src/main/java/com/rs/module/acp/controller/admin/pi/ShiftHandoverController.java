package com.rs.module.acp.controller.admin.pi;

import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.PatrolRecordRespVO;
import com.rs.module.acp.controller.admin.pi.vo.shifthandover.*;
import com.rs.module.acp.entity.pi.PatrolRecordDO;
import com.rs.module.acp.service.pi.PatrolRecordService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.pi.ShiftHandoverDO;
import com.rs.module.acp.service.pi.ShiftHandoverService;

@Api(tags = "实战平台-巡视管控-巡控交接班登记")
@RestController
@RequestMapping("/acp/pi/shiftHandover")
@Validated
public class ShiftHandoverController {

    @Resource
    private ShiftHandoverService shiftHandoverService;

    @Resource
    private PatrolRecordService patrolRecordService;
    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-巡视管控-巡控交接班登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteShiftHandover(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           shiftHandoverService.deleteShiftHandover(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-巡视管控-巡控交接班登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ShiftHandoverRespVO> getShiftHandover(@RequestParam("id") String id) {
        ShiftHandoverDO shiftHandover = shiftHandoverService.getShiftHandover(id);
        ShiftHandoverRespVO shiftHandoverRespVO = BeanUtils.toBean(shiftHandover, ShiftHandoverRespVO.class);
        List<PatrolRecordDO> listPatrolRecordDO = patrolRecordService.getXkdjList(shiftHandover.getXkdj());
        shiftHandoverRespVO.setXkdjList(BeanUtils.toBean(listPatrolRecordDO, PatrolRecordRespVO.class));
        return success(shiftHandoverRespVO);
    }
    //交班登记 shift
    @PostMapping("/shiftReg")
    @ApiOperation(value = "实战平台-巡视管控-交班登记")
    public CommonResult<String> shiftReg(@Valid @RequestBody ShiftRegReqVO regReqVO) {
        return success(shiftHandoverService.shiftReg(regReqVO));
    }
    @PostMapping("/successionReg")
    @ApiOperation(value = "实战平台-巡视管控-接班登记")
    public CommonResult<Boolean> successionReg(@Valid @RequestBody SuccessionRegReqVO regReqVO) {
        return success(shiftHandoverService.successionReg(regReqVO));
    }
    @GetMapping("/getPersonDistribution")
    @ApiOperation(value = "实战平台-巡视管控-人员分布情况查询")
    public CommonResult<ShiftHandoverStatisticsNumVO> getPersonDistribution() {
        return success(shiftHandoverService.getPersonDistribution("",""));
    }
}
