package com.rs.module.acp.controller.admin.gj.vo.civilizedroom;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.gj.CivilizedRoomDetailDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明监室登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedRoomRegistEvaluationReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "主键 不能为空")
    private String id;

    @ApiModelProperty("登记信息")
    @NotEmpty(message = "登记信息 不能为空")
    private List<CivilizedRoomDetailDO> list;

}
