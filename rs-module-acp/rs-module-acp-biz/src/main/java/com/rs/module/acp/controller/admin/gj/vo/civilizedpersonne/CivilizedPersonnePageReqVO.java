package com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明个人登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CivilizedPersonnePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("评比月份")
    private String evalMonth;

    @ApiModelProperty("文明个人")
    private String civilizedPersonne;

    @ApiModelProperty("申请时间")
    private Date[] applyTime;

    @ApiModelProperty("申请人身份证号")
    private String applyUserSfzh;

    @ApiModelProperty("申请人")
    private String applyUser;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
