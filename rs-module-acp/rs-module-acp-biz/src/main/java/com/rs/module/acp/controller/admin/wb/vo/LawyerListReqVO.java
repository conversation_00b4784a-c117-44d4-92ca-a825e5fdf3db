package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerListReqVO extends BaseVO  {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("性别")
    private String xb;

    @ApiModelProperty("证件类型")
    private String zjlx;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("联系方式")
    private String lxfs;

    @ApiModelProperty("律师类型")
    private String lslx;

    @ApiModelProperty("执业证号码")
    private String zyzhm;

    @ApiModelProperty("执业证有效期")
    private Date ksZyzyxq;

    @ApiModelProperty("执业证有效期-结束")
    private Date jsZyzyxq;

    @ApiModelProperty("律师单位")
    private String lsdw;

    @ApiModelProperty("照片存储url")
    private String zpUrl;

    @ApiModelProperty("执业证书url")
    private String zyzsUrl;

}
