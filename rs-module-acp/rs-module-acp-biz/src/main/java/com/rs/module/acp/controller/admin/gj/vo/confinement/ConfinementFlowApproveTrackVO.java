package com.rs.module.acp.controller.admin.gj.vo.confinement;

import com.alibaba.fastjson.JSONObject;
import com.rs.module.base.vo.ApproveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@ApiModel(description = "管理后台 - 实战平台-管教业务-禁闭审批轨迹 VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class ConfinementFlowApproveTrackVO{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("节点标识")
    String nodeKey;
    @ApiModelProperty("节点名称")
    String nodeName;
    @ApiModelProperty("是否审批完成 -1 未完成 ，1 完成")
    Integer nodeStatus;
    @ApiModelProperty("节点时间")
    String nodeCreateTime;
    @ApiModelProperty("字段信息")
    List<JSONObject> nodeInfo;
}
