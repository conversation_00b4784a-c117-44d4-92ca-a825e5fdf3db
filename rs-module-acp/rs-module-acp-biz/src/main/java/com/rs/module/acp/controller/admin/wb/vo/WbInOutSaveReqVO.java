package com.rs.module.acp.controller.admin.wb.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-出入登记 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WbInOutSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("窗口业务ID")
    private String id;

    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("操作人身份证号")
    private String operatorSfzh;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("带出带入民警身份证号")
    private String inoutPoliceSfzh;

    @ApiModelProperty("带出带入民警")
    private String inoutPolice;

    @ApiModelProperty("带出带入时间")
    private Date inoutTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;

    @ApiModelProperty("检查时间")
    private Date inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("会见开始时间（年月日时分秒）---带出登记时传入")
    private Date meetingStartTime;

    @ApiModelProperty("会见结束时间（年月日时分秒）---带入登记时传入")
    private Date meetingEndTime;

    @ApiModelProperty("业务类型子类型(业务类型为：01或03时，必须传)")
    private String businessSubType;
}
