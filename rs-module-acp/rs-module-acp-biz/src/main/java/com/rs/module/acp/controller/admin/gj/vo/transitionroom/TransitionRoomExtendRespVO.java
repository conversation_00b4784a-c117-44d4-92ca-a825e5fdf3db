package com.rs.module.acp.controller.admin.gj.vo.transitionroom;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡监室延长呈批 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransitionRoomExtendRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("过渡监室ID")
    private String transitionRoomId;

    @ApiModelProperty("延长理由")
    private String reason;

    @ApiModelProperty("延长天数")
    private Integer extendDay;

    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_TRANSITION_ROOM_STATUS")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("呈批人")
    private String addUserName;

    @ApiModelProperty("呈批时间")
    private Date addTime;

}
