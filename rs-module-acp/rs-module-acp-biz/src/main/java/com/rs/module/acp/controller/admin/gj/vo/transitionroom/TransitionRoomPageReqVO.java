package com.rs.module.acp.controller.admin.gj.vo.transitionroom;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡监室管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransitionRoomPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("过渡开始日期")
    private Date[] startDate;

    @ApiModelProperty("过渡结束日期")
    private Date[] endDate;

    @ApiModelProperty("实际开始日期")
    private Date[] actualStartDate;

    @ApiModelProperty("实际结束日期")
    private Date[] actualEndDate;

    @ApiModelProperty("过渡监室id")
    private String roomId;

    @ApiModelProperty("过渡监室名称")
    private String roomName;

    @ApiModelProperty("是否延长")
    private Short isExtend;

    @ApiModelProperty("状态")
    private String status;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
