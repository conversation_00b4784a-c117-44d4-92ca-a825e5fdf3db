package com.rs.module.acp.controller.admin.zh;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessedSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.zh.IndicatorCateAssessedDO;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorCateAssessedService;

@Api(tags = "综合管理-绩效考核指标分类与被考评人关联")
@RestController
@RequestMapping("/acp/zh/indicatorCateAssessed")
@Validated
public class IndicatorCateAssessedController {

    @Resource
    private IndicatorCateAssessedService indicatorCateAssessedService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核指标分类与被考评人关联")
    public CommonResult<String> createIndicatorCateAssessed(@Valid @RequestBody IndicatorCateAssessedSaveReqVO createReqVO) {
        return success(indicatorCateAssessedService.createIndicatorCateAssessed(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核指标分类与被考评人关联")
    public CommonResult<Boolean> updateIndicatorCateAssessed(@Valid @RequestBody IndicatorCateAssessedSaveReqVO updateReqVO) {
        indicatorCateAssessedService.updateIndicatorCateAssessed(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核指标分类与被考评人关联")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteIndicatorCateAssessed(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           indicatorCateAssessedService.deleteIndicatorCateAssessed(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类与被考评人关联")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<IndicatorCateAssessedRespVO> getIndicatorCateAssessed(@RequestParam("id") String id) {
        IndicatorCateAssessedDO indicatorCateAssessed = indicatorCateAssessedService.getIndicatorCateAssessed(id);
        return success(BeanUtils.toBean(indicatorCateAssessed, IndicatorCateAssessedRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类与被考评人关联分页")
    public CommonResult<PageResult<IndicatorCateAssessedRespVO>> getIndicatorCateAssessedPage(@Valid @RequestBody IndicatorCateAssessedPageReqVO pageReqVO) {
        PageResult<IndicatorCateAssessedDO> pageResult = indicatorCateAssessedService.getIndicatorCateAssessedPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndicatorCateAssessedRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类与被考评人关联列表")
    public CommonResult<List<IndicatorCateAssessedRespVO>> getIndicatorCateAssessedList(@Valid @RequestBody IndicatorCateAssessedListReqVO listReqVO) {
        List<IndicatorCateAssessedDO> list = indicatorCateAssessedService.getIndicatorCateAssessedList(listReqVO);
        return success(BeanUtils.toBean(list, IndicatorCateAssessedRespVO.class));
    }
}
