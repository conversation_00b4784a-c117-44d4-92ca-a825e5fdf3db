package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("courses_code")
    private String coursesCode;

    @ApiModelProperty("courses_name")
    private String coursesName;

    @ApiModelProperty("分类颜色")
    private String coursesColor;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("机构码")
    private String orgCode;

}
