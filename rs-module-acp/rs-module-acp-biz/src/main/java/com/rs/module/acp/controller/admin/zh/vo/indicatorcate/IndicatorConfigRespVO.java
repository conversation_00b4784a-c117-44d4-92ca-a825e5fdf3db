package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 综合管理-绩效考核截止日期设置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorConfigRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("截止日期类型，01：本月，02：下月")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_JZRQLX")
    private String expiryDateType;
    @ApiModelProperty("间隔天数")
    private Integer intervalDays;
}
