package com.rs.module.acp.controller.admin.pi.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-保护性约束新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ProtRestraintSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监室ID")
    @NotEmpty(message = "监室ID不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("申请人身份证号")
    private String applicantSfzh;

    @ApiModelProperty("申请人")
    private String applicant;

    @ApiModelProperty("申请事由 字典：ZD_BHXYSSQSY")
    @NotEmpty(message = "申请事由不能为空")
    private String reason;

    @ApiModelProperty("约束带类别 字典：ZD_YSDLB")
    @NotEmpty(message = "约束带类别不能为空")
    private String restraintType;

    @ApiModelProperty("执行人身份证号")
    @NotEmpty(message = "执行人身份证号不能为空")
    private String executorSfzh;

    @ApiModelProperty("执行人身份证号")
    @NotEmpty(message = "执行人身份证号不能为空")
    private String executor;

    @ApiModelProperty("约束开始时间")
    @NotNull(message = "约束开始时间不能为空")
    private Date startTime;

    @ApiModelProperty("约束结束时间")
    private Date endTime;

}
