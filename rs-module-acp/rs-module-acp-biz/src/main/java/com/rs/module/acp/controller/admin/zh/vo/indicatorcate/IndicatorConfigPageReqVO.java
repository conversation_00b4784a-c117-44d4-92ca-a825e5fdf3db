package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-绩效考核截止日期设置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IndicatorConfigPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("截止日期类型，01：本月，02：下月")
    private String expiryDateType;

    @ApiModelProperty("间隔天数")
    private Integer intervalDays;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
