package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情处置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventHandleSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotEmpty(message = "处置ID不能为空")
    private String id;

    @ApiModelProperty("所属警情")
    @NotEmpty(message = "所属警情不能为空")
    private String eventId;

    @ApiModelProperty("处置反馈")
    private String feedbackInfo;

    @ApiModelProperty("处置状态 0未办结 1已办结 2不通过 3通过")
    private String status;

    @ApiModelProperty("处置时所处岗位")
    private String handlePost;

    @ApiModelProperty("审批内容")
    private String handleInfo;

    @ApiModelProperty("变动内容")
    private String changeContent;

    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("业务处置列表")
    private List<PrisonEventBusinessSaveReqVO> businessList;

    @ApiModelProperty("保存类型（0：保存--可以理解为临时保存，1：提交--所领导审批节点只有提交）")
    @NotEmpty(message = "保存类型不能为空")
    private String saveType;

    @ApiModelProperty("报告本级公安机关时间")
    private Date reportLevelPolice;

    @ApiModelProperty("报告上级监管业务指导部门时间")
    private Date reportSupervise;

    @ApiModelProperty("报告上级监管业务指导部门时间")
    private Date reportProcuratorialOrgans;
}
