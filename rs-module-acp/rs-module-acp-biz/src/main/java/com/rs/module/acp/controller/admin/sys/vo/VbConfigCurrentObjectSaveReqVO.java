package com.rs.module.acp.controller.admin.sys.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-语音播报-即时播报对象新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VbConfigCurrentObjectSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("配置Id")
    private String configId;

    @ApiModelProperty("播报监所Id")
    private String vbPrisonId;

    @ApiModelProperty("播报监区Id")
    private String vbAreaId;

    @ApiModelProperty("播报监室Id")
    private String vbRoomId;

}
