package com.rs.module.acp.controller.admin.gj.vo.undercover;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-耳目撤销新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UndercoverCancelSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("撤销申请ID")
    private String id;

    @ApiModelProperty("撤销理由  字典： ZD_GJXXYBJCX")
    @NotEmpty(message = "撤销理由不能为空")
    private String cancelReason;

    @ApiModelProperty("备注")
    private String remark;


    //@ApiModelProperty("审批人身份证号")
    //private String approverSfzh;
    //
    //@ApiModelProperty("审批人姓名")
    //private String approverXm;
    //
    //@ApiModelProperty("审批时间")
    //private Date approverTime;
    //
    //@ApiModelProperty("审批结果")
    //private String approvalResult;
    //
    //@ApiModelProperty("审核意见")
    //private String approvalComments;
    //
    //@ApiModelProperty("ACT流程实例Id")
    //private String actInstId;
    //
    //@ApiModelProperty("任务ID")
    //private String taskId;

}
