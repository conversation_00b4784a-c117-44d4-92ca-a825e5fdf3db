package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.GjRiskAssmtRespVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.GjRiskAssmtSaveRegVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.GjRiskAssmtSaveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.riskassmt.RiskAssmtTodoRespVO;
import com.rs.module.acp.entity.gj.RiskAssmtTodoDO;
import com.rs.module.acp.service.gj.riskassmt.GjRiskAssmtService;
import com.rs.module.acp.service.gj.riskassmt.RiskAssmtTodoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-风险评估")
@RestController
@RequestMapping("/acp/gj/gjRiskAssmt")
@Validated
public class GjRiskAssmtController {

    @Resource
    private GjRiskAssmtService gjRiskAssmtService;

    @Resource
    private RiskAssmtTodoService riskAssmtTodoService;

    @PostMapping("/create")
    @ApiOperation(value = "管教业务-风险评估-新增")
    @LogRecordAnnotation(bizModule = "acp:conflict:create", operateType = LogOperateType.CREATE, title = "管教民警-风险评估登记",
            success = "管教业务-风险评估解登记成功", fail = "管教业务-风险评估登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    //@BusTrace(busType = BusTypeEnum.YEWU_FXPG, jgrybm="{{#createReqVO.jgrybm}}",
    //        content = "{\"被监管人姓名\":\"{{#createReqVO.jgryxm}}\",\"评估等级\":\"{{#createReqVO.assmtReason}}\"}")
    public CommonResult<String> createGjRiskAssmt(@Valid @RequestBody GjRiskAssmtSaveRegVO createReqVO) {
        return success(gjRiskAssmtService.createGjRiskAssmt(createReqVO));
    }

    /**
     * 批量创建
     * <AUTHOR>
     * @date 2025/6/4 19:40
     * @param [createReqVOList]
     * @return com.rs.framework.common.pojo.CommonResult<java.lang.Boolean>
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "管教业务-风险评估-批量新增)")
    @LogRecordAnnotation(bizModule = "acp:conflict:createReqVOList", operateType = LogOperateType.CREATE, title = "管教民警-风险评估登记",
            success = "管教业务-风险评估解登记成功", fail = "管教业务-风险评估登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVOList}}")
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<GjRiskAssmtSaveRegVO> createReqVOList) {
        return success(gjRiskAssmtService.createReqVOList(createReqVOList));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-风险评估")
    @LogRecordAnnotation(bizModule = "acp:gjrisk:update", operateType = LogOperateType.UPDATE, title = "管教民警-风险评估-编辑",
            success = "管教业务-风险评估-登记成功", fail = "管教业务-风险评估登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> updateGjRiskAssmt(@Valid @RequestBody GjRiskAssmtSaveReqVO updateReqVO) {
        gjRiskAssmtService.updateGjRiskAssmt(updateReqVO);
        return success(true);
    }

    @PostMapping("/updateProcessStatus")
    @ApiOperation(value = "管教业务-风险评估-流程修改")
    @LogRecordAnnotation(bizModule = "acp:gjrisk:updateProcessStatus", operateType = LogOperateType.UPDATE, title = "管教业务-风险评估-流程状态修改",
            success = "管教业务-风险评估-流程状态修改成功", fail = "管教业务-风险评估-流程状态修改失败，错误信息：{{#_ret[msg]}}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "登记状态", required = true, paramType = "query"),
            @ApiImplicitParam(name = "actInstId", value = "流程实例ID", required = true, paramType = "query"),
            @ApiImplicitParam(name = "taskId", value = "任务ID", required = true, paramType = "query")
    })
    public CommonResult<Boolean> updateProcessStatus(@NotBlank(message = "ID不能为空") @RequestParam(value = "id") String id,
                                                     @NotBlank(message = "状态不能为空") @RequestParam(value = "status") String status,
                                                     @NotBlank(message = "流程实例ID") @RequestParam(value = "actInstId") String actInstId,
                                                     @NotBlank(message = "任务ID") @RequestParam(value = "taskId") String taskId)
    {
        return success(gjRiskAssmtService.updateProcessStatus(id, status, actInstId,taskId));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "领导审批-管教业务-风险评估")
    @LogRecordAnnotation(bizModule = "acp:gjrisk:leaderApprove", operateType = LogOperateType.UPDATE, title = "管教业务-信息员管理-领导审批",
            success = "管教业务-风险评估-领导审批成功", fail = "管教业务-风险评估-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        gjRiskAssmtService.leaderApprove(approveReqVO);
        return success(true);
    }

    @PostMapping("/batchApprove")
    @ApiOperation(value = "领导审批-管教业务-风险评估")
    @LogRecordAnnotation(bizModule = "acp:gjrisk:batchApprove", operateType = LogOperateType.UPDATE, title = "管教业务-信息员管理-领导审批",
            success = "管教业务-风险评估-领导审批成功", fail = "管教业务-风险评估-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVOList}}")
    public CommonResult<Boolean> batchApprove(@Valid @RequestBody List<GjApproveReqVO> approveReqVOList) {
        gjRiskAssmtService.batchApprove(approveReqVOList);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-风险评估")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:gjrisk:delete", operateType = LogOperateType.DELETE, title = "管教业务-风险评估-删除",
            success = "管教业务-风险评估-删除成功", fail = "管教业务-风险评估-删除失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteGjRiskAssmt(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           gjRiskAssmtService.deleteGjRiskAssmt(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "管教业务-风险评估-获取详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:gjrisk:get", operateType = LogOperateType.QUERY, title = "管教业务-风险评估-获取详情",
            bizNo = "{{#id}}", success = "获取管教业务-风险评估详情成功", fail = "获取管教业务-风险评估详情失败", extraInfo = "{{#id}}")
    public CommonResult<GjRiskAssmtRespVO> getGjRiskAssmt(@RequestParam("id") String id) {
        GjRiskAssmtRespVO gjRiskAssmt = gjRiskAssmtService.getGjRiskAssmt(id);
        return success(gjRiskAssmt);
    }

    @PostMapping("/getGjRiskAssmtRespVOByJgrybm")
    @ApiOperation(value = "管教业务-风险评估-获取信息员记录列表信息")
    @LogRecordAnnotation(bizModule = "acp:gjriskassmt:jgrybm", operateType = LogOperateType.QUERY, title = "管教业务-风险评估-获取记录列表",
            bizNo = "{{#jgrybm}}", success = "获取风险评估记录列表信息成功", fail = "获取风险评估列表失败", extraInfo = "{{#jgrybm}}")
    public CommonResult<List<GjRiskAssmtRespVO>> getGjRiskAssmtRespVOByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        List<GjRiskAssmtRespVO> list = gjRiskAssmtService.getGjRiskAssmtRespVOByJgrybm(jgrybm);
        return success(list);
    }


    @GetMapping("/getTodoDetailById")
    @ApiOperation(value = "管教业务-风险评估-获取待评估详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:gjrisk:getTodoDetailById", operateType = LogOperateType.QUERY, title = "管教业务-风险评估-获取待评估详情",
            bizNo = "{{#id}}", success = "获取管教业务-获取待评估详情成功", fail = "获取待评估详情成功", extraInfo = "{{#id}}")
    public CommonResult<RiskAssmtTodoRespVO> getTodoDetailById(@RequestParam("id") String id) {
        RiskAssmtTodoDO gjRiskAssmt = riskAssmtTodoService.getRiskAssmtTodo(id);
        return success(BeanUtils.toBean(gjRiskAssmt, RiskAssmtTodoRespVO.class));
    }

    //@GetMapping("/test")
    //@ApiOperation(value = "测试定时任务job")
    //public CommonResult<Boolean> test() {
    //    riskAssmtTodoService.attentionPersonnel();
    //    return success(true);
    //}


}
