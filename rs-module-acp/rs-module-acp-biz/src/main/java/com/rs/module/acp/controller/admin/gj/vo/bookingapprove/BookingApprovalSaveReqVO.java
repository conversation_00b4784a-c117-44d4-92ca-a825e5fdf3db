package com.rs.module.acp.controller.admin.gj.vo.bookingapprove;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-预约审核管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BookingApprovalSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;


    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("预约类别 字典编码：ZD_GJ_YYSHGL_YYLX")
    @NotEmpty(message = "预约类别不能为空")
    private String bookingCategory;

    @ApiModelProperty("服务类别 字典编码：ZD_GJ_YYSHGL_FWLX")
    @NotEmpty(message = "服务类别不能为空")
    private String serviceCategory;

    //@ApiModelProperty("申请时间")
    //@NotNull(message = "申请时间不能为空")
    //private Date applicationTime;

    //@ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    //private String status;
    //
    //@ApiModelProperty("审批人身份证号")
    //private String approverSfzh;
    //
    //@ApiModelProperty("审批人姓名")
    //private String approverXm;
    //
    //@ApiModelProperty("审批时间")
    //private Date approverTime;
    //
    //@ApiModelProperty("审批结果")
    //private String approvalResult;
    //
    //@ApiModelProperty("审核意见")
    //private String approvalComments;
    //
    //@ApiModelProperty("ACT流程实例Id")
    //private String actInstId;
    //
    //@ApiModelProperty("任务ID")
    //private String taskId;

}
