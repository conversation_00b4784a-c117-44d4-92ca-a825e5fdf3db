package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 综合管理-绩效考核子指标新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorSubSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所属主指标ID")
    //@NotEmpty(message = "所属主指标ID不能为空")
    private String mainIndicatorId;

    @ApiModelProperty("是否必填 字典：ZD_BOOLEAN_TYPE")
    @NotNull(message = "是否必填不能为空")
    private Short isRequired;

    @ApiModelProperty("指标描述")
    @NotEmpty(message = "指标描述不能为空")
    private String description;

    @ApiModelProperty("加减分，字典：ADD：加分、SUBTRACT：扣分  字典：ZD_JXKH_JJF")
    @NotEmpty(message = "加减分，字典：ADD：加分、SUBTRACT：扣分不能为空")
    private String addSubtract;

    @ApiModelProperty("分值类型，字典：FIXED：固定分，RANGE：范围分 字典：ZD_JXKH_FZLX")
    @NotEmpty(message = "分值类型，字典：FIXED：固定分，RANGE：范围分不能为空")
    private String scoreType;

    @ApiModelProperty("基础分值")
    private BigDecimal baseScore;

    @ApiModelProperty("最小分值")
    private BigDecimal minScore;

    @ApiModelProperty("最大分值（仅范围分有效）")
    private BigDecimal maxScore;

    @ApiModelProperty("排序序号")
    @NotNull(message = "排序序号不能为空")
    private Integer sortOrder;

}
