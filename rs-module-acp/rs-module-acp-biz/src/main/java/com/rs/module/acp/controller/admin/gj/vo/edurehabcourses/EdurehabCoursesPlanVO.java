package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程计划 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesPlanVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("课程计划名称")
    private String planName;
    @ApiModelProperty("plan_code")
    private String planCode;
    @ApiModelProperty("课程时段-开始")
    private Date startDate;
    @ApiModelProperty("课程时段-结束")
    private Date endDate;

    private List<JqAreaVO> jqList;

    private List<EdurehabTimeSlotRespVO> timeList;

}
