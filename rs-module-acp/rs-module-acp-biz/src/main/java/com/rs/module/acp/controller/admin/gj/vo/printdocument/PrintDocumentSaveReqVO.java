package com.rs.module.acp.controller.admin.gj.vo.printdocument;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文书打印预览新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrintDocumentSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("关联id")
    private String glId;

    @ApiModelProperty("关联类型")
    private String glType;

    @ApiModelProperty("文书打印其他信息")
    private String wsdata;

}
