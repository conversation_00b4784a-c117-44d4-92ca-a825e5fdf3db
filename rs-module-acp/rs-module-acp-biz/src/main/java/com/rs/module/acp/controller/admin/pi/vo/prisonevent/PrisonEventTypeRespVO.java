package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件类型 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventTypeRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("事件类型名称")
    private String typeName;
    @ApiModelProperty("图标")
    private String icon;
    @ApiModelProperty("是否启用")
    private Short isEnabled;
    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("是否内置（0：否，1：是），内置的不允许删除")
    private String isBuiltin;

    @ApiModelProperty("添加人")
    private String addUserName;

    @ApiModelProperty("添加人")
    private Date addTime;

    @ApiModelProperty("事件明细树形List")
    private List<PrisonEventTypeItemRespVO> eventItemList;

    @ApiModelProperty("处置模板List")
    private List<PrisonEventDisposeTemplateRespVO> disposeTemplateList;

    @ApiModelProperty("推送岗位List")
    private List<PrisonEventPushSettingRespVO> pushSettingList;
}
