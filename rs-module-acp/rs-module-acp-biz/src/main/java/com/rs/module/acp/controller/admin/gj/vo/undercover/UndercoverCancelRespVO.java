package com.rs.module.acp.controller.admin.gj.vo.undercover;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-耳目撤销 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class UndercoverCancelRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("撤销理由")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJXXYBJCX")
    private String cancelReason;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("撤销申请人")
    private String addUserName;

    @ApiModelProperty("撤销申请时间")
    private Date addTime;
}
