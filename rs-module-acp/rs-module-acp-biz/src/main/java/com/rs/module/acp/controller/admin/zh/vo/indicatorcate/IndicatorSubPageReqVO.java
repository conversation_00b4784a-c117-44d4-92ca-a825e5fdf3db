package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-绩效考核子指标分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IndicatorSubPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所属主指标ID")
    private String mainIndicatorId;

    @ApiModelProperty("是否必填")
    private Short isRequired;

    @ApiModelProperty("指标描述")
    private String description;

    @ApiModelProperty("加减分，字典：ADD：加分、SUBTRACT：扣分")
    private String addSubtract;

    @ApiModelProperty("分值类型，字典：FIXED：固定分，RANGE：范围分")
    private String scoreType;

    @ApiModelProperty("基础分值")
    private BigDecimal baseScore;

    @ApiModelProperty("最小分值")
    private BigDecimal minScore;

    @ApiModelProperty("最大分值（仅范围分有效）")
    private BigDecimal maxScore;

    @ApiModelProperty("排序序号")
    private Integer sortOrder;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
