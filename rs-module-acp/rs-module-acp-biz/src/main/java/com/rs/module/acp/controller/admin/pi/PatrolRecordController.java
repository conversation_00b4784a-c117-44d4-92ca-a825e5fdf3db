package com.rs.module.acp.controller.admin.pi;

import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.PatrolRecordRegReqVO;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.PatrolRecordRespVO;
import com.rs.module.acp.controller.admin.pi.vo.patrolrecord.PatrolRecordSaveReqVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.util.DateUtil;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.pi.PatrolRecordDO;
import com.rs.module.acp.service.pi.PatrolRecordService;

@Api(tags = "实战平台-巡视管控-巡视登记")
@RestController
@RequestMapping("/acp/pi/patrolRecord")
@Validated
public class PatrolRecordController {

    @Resource
    private PatrolRecordService patrolRecordService;

    /*@PostMapping("/create")
    @ApiOperation(value = "创建实战平台-巡视管控-巡视登记")
    public CommonResult<String> createPatrolRecord(@Valid @RequestBody PatrolRecordSaveReqVO createReqVO) {
        return success(patrolRecordService.createPatrolRecord(createReqVO));
    }*/

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-巡视管控-巡视登记")
    public CommonResult<Boolean> updatePatrolRecord(@Valid @RequestBody PatrolRecordSaveReqVO updateReqVO) {
        patrolRecordService.updatePatrolRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-巡视管控-巡视登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deletePatrolRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           patrolRecordService.deletePatrolRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-巡视管控-巡视登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<PatrolRecordRespVO> getPatrolRecord(@RequestParam("id") String id) {
        PatrolRecordDO patrolRecord = patrolRecordService.getPatrolRecord(id);
        return success(BeanUtils.toBean(patrolRecord, PatrolRecordRespVO.class));
    }
    @PostMapping("/getAreaByAreaType")
    @ApiOperation(value = "实战平台-巡视管控-分控室选择框")
    public CommonResult<List<AreaDO>> getAreaByAreaType(){
        return success(patrolRecordService.getAreaByAreaType(null,true));
    }
    @PostMapping("/manuallyCreatePatrolRecord")
    @ApiOperation(value = "实战平台-巡视管控-新增巡视登记")
    public CommonResult<String> manuallyCreatePatrolRecord(@Valid @RequestBody PatrolRecordRegReqVO regReqVO){
        return success(patrolRecordService.manuallyRegSave(regReqVO));
    }
    @PostMapping("/regSave")
    @ApiOperation(value = "实战平台-巡视管控-其他业务推送后巡视登记")
    public CommonResult<Boolean> regSave(@Valid @RequestBody PatrolRecordSaveReqVO saveReqVO){
        patrolRecordService.regSave(saveReqVO);
        return success(true);
    }
    @GetMapping("/getPatrolRecordList")
    @ApiOperation(value = "查询当前时间8小时前当前账号的巡控记录")
    public CommonResult<List<PatrolRecordRespVO>> getPatrolRecordList(){
        Date endTime = DateUtil.getCurrentDate();
        Date startTime = DateUtil.add(endTime, Calendar.HOUR_OF_DAY, -8);
        return success(BeanUtils.toBean(patrolRecordService.getPatrolRecordList(SessionUserUtil.getSessionUser().getIdCard(),startTime,endTime),PatrolRecordRespVO.class));
    }
    @GetMapping("/getUserListByCoordinationPosts")
    @ApiOperation(value = "根据岗位协同字典编码查询人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "coordinationPostsCode", value = "岗位协同字典编码 ZD_XSGKXTGW", example = "01", required = true)
    })
    public CommonResult<List<UserRespDTO>> getUserListByCoordinationPosts(@RequestParam String coordinationPostsCode){
        try {
            return success(patrolRecordService.getUserListByCoordinationPosts(coordinationPostsCode));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

}
