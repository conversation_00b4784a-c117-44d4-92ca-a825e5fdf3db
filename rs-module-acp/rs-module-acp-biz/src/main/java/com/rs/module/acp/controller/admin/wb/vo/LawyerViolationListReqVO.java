package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师违规列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerViolationListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("律师表ID")
    private String lawyerId;

    @ApiModelProperty("违规时间")
    private Date[] violationTime;

    @ApiModelProperty("违规情况")
    private String violationType;

    @ApiModelProperty("详细说明")
    private String description;

}
