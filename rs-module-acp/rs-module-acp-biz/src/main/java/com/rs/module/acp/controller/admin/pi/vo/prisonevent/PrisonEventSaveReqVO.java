package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所属预警")
    private String warningId;

    @ApiModelProperty("警情地点")
    private String areaId;

    @ApiModelProperty("警情地点名称")
    private String areaName;

    @ApiModelProperty("发生时间")
    private Date happenTime;

    @ApiModelProperty("警情事件")
    private String eventType;

    @ApiModelProperty("警情级别")
    private String eventLevel;

    @ApiModelProperty("设备类型ID")
    private String deviceTypeId;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;

    @ApiModelProperty("设备ID")
    private String deviceId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("状态（0：待核实、1待处置、2待办结（所领导审批）、3已办结、99：无需处置）-字典： ZD_JJKS_SQYWZT")
    private String status;

    @ApiModelProperty("警情详情")
    private String eventDetails;

    @ApiModelProperty("警情编码--无需传值")
    private String eventCode;

    @ApiModelProperty("事件来源")
    private String eventSrc;

    @ApiModelProperty("处置情况")
    private String handleInfo;

    @ApiModelProperty("警情事件id")
    private String eventTypeId;

    @ApiModelProperty("事件开始时间（精确时间-开始）")
    private Date eventStartTime;

    @ApiModelProperty("事件结束时间（精确时间-结束）")
    private Date eventEndTime;

    @ApiModelProperty("巡控登记人身份证号")
    private String handleUserSfzh;

    @ApiModelProperty("巡控登记人")
    private String handleUserName;

    @ApiModelProperty("巡控登记时间")
    private Date handleTime;

    @ApiModelProperty("所领导审批人身份证号")
    private String auditUserSfzh;

    @ApiModelProperty("所领导审批人")
    private String auditUserName;

    @ApiModelProperty("所领导审批时间")
    private Date auditTime;

    @ApiModelProperty("所领导审批意见")
    private String auditOpinion;

    @ApiModelProperty("关联报警录像文件")
    private String eventVideo;

    @ApiModelProperty("所情事件类型大类ID")
    private String eventRootTypeId;

    @ApiModelProperty("所情事件类型大类名称")
    private String eventRootTypeName;

    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("多个使用逗号隔开")
    private String screenshotUrl;

    @ApiModelProperty("保存类型（0：办结，1：提交）")
    @NotEmpty(message = "保存类型不能为空")
    private String saveType;

    @ApiModelProperty("报警人列表")
    private List<PrisonEventPersonSaveVO> reportPrisonerList;

    @ApiModelProperty("关联人员-在押人员列表")
    private List<PrisonEventPrisonerSaveReqVO> inPrisonerList;

    @ApiModelProperty("关联人员-工作人员列表")
    private List<PrisonEventPersonSaveVO> workPrisonerList;

    @ApiModelProperty("关联人员-外来人员列表")
    private List<PrisonEventPersonSaveVO> outsiderPrisonerList;

    @ApiModelProperty("推送对象")
    private List<PrisonEventDisposePostSaveVO> postInfoList;

}
