package com.rs.module.acp.controller.admin.gj.vo.confinement;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-禁闭登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConfinementRegSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    @NotEmpty(message = "数据来源（字典：ZD_DATA_SOURCES）不能为空")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("禁闭天数")
    @NotNull(message = "禁闭天数不能为空")
    private Integer confinementDays;

    @ApiModelProperty("禁闭监室id")
    @NotEmpty(message = "禁闭监室id不能为空")
    private String roomId;

    @ApiModelProperty("原监室ID")
    private String originalRoomId;

    @ApiModelProperty("禁闭原因 ZD_GJYW_JBYY")
    @NotEmpty(message = "禁闭原因不能为空")
    private String confinementReason;

    @ApiModelProperty("详细原因")
    private String detailedReason;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("是否关联惩罚 0 否,1 是")
    private Integer isAssociatedPunishment;

    @ApiModelProperty("惩罚措施")
    private String punishmentMeasures;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("禁闭开始日期")
    private Date confinementStartDate;

    @ApiModelProperty("禁闭结束日期")
    private Date confinementEndDate;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPoliceSfzh;

    @ApiModelProperty("带出民警身份证号")
    private String escortingPolice;

    @ApiModelProperty("带出监室时间")
    private Date escortingTime;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;

    @ApiModelProperty("检查时间")
    private Date inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("带入禁闭监室时间")
    private Date intoConfinementTime;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
