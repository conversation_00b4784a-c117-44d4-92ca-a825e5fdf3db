package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所警情管理-报警联动设置(所情来源) Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventSettingRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所请来源 字典：C_JQLY")
    private String eventSrc;
    @ApiModelProperty("所情来源-中文")
    private String name;
    @ApiModelProperty("所情等级 字典：C_JQDJ")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JJKS_SQDJ")
    private Short eventLevel;
    @ApiModelProperty("是否启用 1启用 0禁用")
    private Short enabled;
    @ApiModelProperty("处理时效（分钟）")
    private Short processingDuration;
    @ApiModelProperty("提示音 字典：PRISON_WARNING_TONE")
    private String tone;
    @ApiModelProperty("可选择的设置。逗号分隔	字典：PRISON_EVENT_SETTING_ITEM")
    private String optionalSettings;
    @ApiModelProperty("联动配置。逗号分隔。optional_settings")
    private String settings;
    @ApiModelProperty("扩展字段1")
    private String extendOne;
    @ApiModelProperty("所情事件类型ID")
    private String typeId;
}
