package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-物品顾送登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GoodsDeliveryPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("顾送编号")
    private String deliveryNo;

    @ApiModelProperty("顾送日期")
    private Date[] deliveryDate;

    @ApiModelProperty("顾送人姓名")
    private String senderName;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件号码")
    private String idNumber;

    @ApiModelProperty("与被监管人关系")
    private String relationship;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("户籍地址")
    private String householdAddress;

    @ApiModelProperty("物品照片存储路径")
    private String goodsPhotoPath;

    @ApiModelProperty("物品信息")
    private String goodsInfo;

    @ApiModelProperty("物品总数")
    private Integer goodsTotal;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("签收时间")
    private Date[] receiptTime;

    @ApiModelProperty("签名（存储签名文件路径或文本）")
    private String signature;

    @ApiModelProperty("拒签原因")
    private String rejectionReason;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
