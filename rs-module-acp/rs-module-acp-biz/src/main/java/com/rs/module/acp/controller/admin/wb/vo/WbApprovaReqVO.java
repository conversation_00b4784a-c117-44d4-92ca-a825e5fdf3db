package com.rs.module.acp.controller.admin.wb.vo;

import com.rs.module.base.vo.ApproveReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-审批")
@Data
@EqualsAndHashCode(callSuper = false)
public class WbApprovaReqVO extends ApproveReqVO  {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("业务类型(0：提讯、1：提询、2：提解、3：律师会见、4：家属会见、5：领事会见、6：家属单向视频会见)")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;
}
