package com.rs.module.acp.controller.admin.gj.vo.punishment;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务延长处罚呈批 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentExtendRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("处罚呈批ID")
    private String punishmentId;
    @ApiModelProperty("延长理由")
    private String reason;
    @ApiModelProperty("延长天数")
    private Integer extendDay;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("审批人签名")
    private String approvalAutograph;
    @ApiModelProperty("审批人签名日期")
    private Date approvalAutographTime;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
}
