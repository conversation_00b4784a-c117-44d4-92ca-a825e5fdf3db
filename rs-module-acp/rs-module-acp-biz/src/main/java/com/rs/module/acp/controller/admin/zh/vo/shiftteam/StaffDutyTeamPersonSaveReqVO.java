package com.rs.module.acp.controller.admin.zh.vo.shiftteam;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 值班模板班组人员信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class StaffDutyTeamPersonSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("acp_zh_staff_duty_team.id")
    @NotEmpty(message = "acp_zh_staff_duty_team.id不能为空")
    private String teamId;

    @ApiModelProperty("acp_zh_staff_duty_role.id")
    @NotEmpty(message = "acp_zh_staff_duty_role.id不能为空")
    private String roleId;

    @ApiModelProperty("人员id")
    private String personId;

    @ApiModelProperty("人员姓名")
    @NotEmpty(message = "人员姓名不能为空")
    private String personName;

    @ApiModelProperty("人员sfzh")
    @NotEmpty(message = "人员sfzh不能为空")
    private String personSfzh;

}
