package com.rs.module.acp.controller.admin.pi.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-夜间开启监室门 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class NightDoorRecordRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监室ID")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("开门原因")
    @Trans(type = TransType.DICTIONARY, key = "ZD_YJKM_YY")
    private String openReason;
    @ApiModelProperty("申请人身份证号")
    private String applyUserSfzh;
    @ApiModelProperty("申请人")
    private String applyUser;
    @ApiModelProperty("申请时间")
    private Date applyTime;
    @ApiModelProperty("申请开门日期")
    private Date applyOpenDoorDate;
    @ApiModelProperty("进入监室民辅警身份证号，多个用逗号分隔")
    private String entryPoliceSfzh;
    @ApiModelProperty("进入监室民辅警名称，多个用逗号分隔")
    private String entryPoliceName;
    @ApiModelProperty("监室外警戒民辅警身份证号，多个用逗号分隔")
    private String guardPoliceSfzh;
    @ApiModelProperty("监室外警戒民辅名称，多个用逗号分隔")
    private String guardPoliceName;
    @ApiModelProperty("实际开门时间")
    private Date actualOpenTime;
    @ApiModelProperty("实际关门时间")
    private Date actualCloseTime;
    @ApiModelProperty("开门时长（分钟）")
    private Integer durationMinutes;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
}
