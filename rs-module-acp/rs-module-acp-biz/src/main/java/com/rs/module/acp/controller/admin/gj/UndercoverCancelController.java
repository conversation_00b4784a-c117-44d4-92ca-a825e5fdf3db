package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelRespVO;
import com.rs.module.acp.controller.admin.gj.vo.undercover.UndercoverCancelSaveReqVO;
import com.rs.module.acp.entity.gj.UndercoverCancelDO;
import com.rs.module.acp.service.gj.undercover.UndercoverCancelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-信息员撤销")
@RestController
@RequestMapping("/acp/gj/undercoverCancel")
@Validated
public class UndercoverCancelController {

    @Resource
    private UndercoverCancelService undercoverCancelService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-信息员撤销")
    @LogRecordAnnotation(bizModule = "acp:undercoverCancel:create", operateType = LogOperateType.CREATE, title = "管教业务-信息员管理-布建申请撤销",
            success = "管教业务-信息员管理-布建申请撤销登记成功", fail = "管教业务-信息员管理-布建申请登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createUndercoverCancel(@Valid @RequestBody UndercoverCancelSaveReqVO createReqVO) {
        return success(undercoverCancelService.createUndercoverCancel(createReqVO));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "领导审批-管教业务-信息员撤销")
    @LogRecordAnnotation(bizModule = "acp:undercoverCancel:leaderApprove", operateType = LogOperateType.UPDATE, title = "管教业务-信息员管理-领导审批",
            success = "管教业务-信息员管理-领导审批成功", fail = "管教业务-信息员管理-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApprove(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        undercoverCancelService.leaderApprove(approveReqVO);
        return success(true);
    }


    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-信息员撤销")
    public CommonResult<Boolean> updateUndercoverCancel(@Valid @RequestBody UndercoverCancelSaveReqVO updateReqVO) {
        undercoverCancelService.updateUndercoverCancel(updateReqVO);
        return success(true);
    }


    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-耳目撤销")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteUndercoverCancel(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           undercoverCancelService.deleteUndercoverCancel(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-耳目撤销")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<UndercoverCancelRespVO> getUndercoverCancel(@RequestParam("id") String id) {
        UndercoverCancelDO undercoverCancel = undercoverCancelService.getUndercoverCancel(id);
        return success(BeanUtils.toBean(undercoverCancel, UndercoverCancelRespVO.class));
    }

}
