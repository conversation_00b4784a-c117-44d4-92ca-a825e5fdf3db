package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-考核审核记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmttZdldApprovalRecordReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("综合审核原因")
    private String zhApprovalReason;

    @ApiModelProperty("综合审核分数")
    private BigDecimal zhApprovalScore;

}
