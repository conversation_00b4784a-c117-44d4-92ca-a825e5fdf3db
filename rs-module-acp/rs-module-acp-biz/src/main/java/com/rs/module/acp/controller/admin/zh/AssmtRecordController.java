package com.rs.module.acp.controller.admin.zh;

import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmtRecordSaveReqVO;
import com.rs.module.acp.job.indicator.IndicatorJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.zh.AssmtRecordDO;
import com.rs.module.acp.service.zh.indicatorcate.AssmtRecordService;

@Api(tags = "综合管理-绩效考核-记录")
@RestController
@RequestMapping("/acp/zh/assmtRecord")
@Validated
public class AssmtRecordController {

    @Autowired
    private AssmtRecordService assmtRecordService;

    @Autowired
    private IndicatorJob indicatorJob;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核-记录")
    public CommonResult<String> createAssmtRecord(@Valid @RequestBody AssmtRecordSaveReqVO createReqVO) {
        return success(assmtRecordService.createAssmtRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核-记录")
    public CommonResult<Boolean> updateAssmtRecord(@Valid @RequestBody AssmtRecordSaveReqVO updateReqVO) {
        assmtRecordService.updateAssmtRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核-记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteAssmtRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           assmtRecordService.deleteAssmtRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核-记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AssmtRecordRespVO> getAssmtRecord(@RequestParam("id") String id) {
        return success(assmtRecordService.getAssmtRecord(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核-记录分页")
    public CommonResult<PageResult<AssmtRecordRespVO>> getAssmtRecordPage(@Valid @RequestBody AssmtRecordPageReqVO pageReqVO) {
        PageResult<AssmtRecordDO> pageResult = assmtRecordService.getAssmtRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssmtRecordRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核-记录列表")
    public CommonResult<List<AssmtRecordRespVO>> getAssmtRecordList(@Valid @RequestBody AssmtRecordListReqVO listReqVO) {
        List<AssmtRecordDO> list = assmtRecordService.getAssmtRecordList(listReqVO);
        return success(BeanUtils.toBean(list, AssmtRecordRespVO.class));
    }

    @GetMapping("/test-job")
    @ApiOperation(value = "定时任务测试")
    public CommonResult<Boolean> testJob() {
        indicatorJob.indicatorAssmtRecordJob();
        return success(true);
    }
}
