package com.rs.module.acp.controller.admin.pi.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-保护性约束分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProtRestraintPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("监室ID")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("申请人身份证号")
    private String applicantSfzh;

    @ApiModelProperty("申请人")
    private String applicant;

    @ApiModelProperty("申请事由")
    private String reason;

    @ApiModelProperty("约束带类别")
    private String restraintType;

    @ApiModelProperty("执行人身份证号")
    private String executorSfzh;

    @ApiModelProperty("执行人身份证号")
    private String executor;

    @ApiModelProperty("约束开始时间")
    private Date[] startTime;

    @ApiModelProperty("束结束时间")
    private Date[] endTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("排序属性")
    List<OrderItem> orderFields;
}
