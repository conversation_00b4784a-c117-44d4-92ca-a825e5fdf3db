package com.rs.module.acp.controller.admin.pm.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-定位标签与人员绑定-解绑新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LocalsenseTagPersonUnbindSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("标签id")
    private String tagId;

    @ApiModelProperty("绑定人员ID")
    private String bindPersonId;

    @ApiModelProperty("解绑时间")
    private Date unbindTime;

    @ApiModelProperty("解绑原因")
    @NotEmpty(message = "解绑原因不能为空")
    private String unbindReason;

}
