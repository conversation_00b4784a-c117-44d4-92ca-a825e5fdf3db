package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-物品顾送登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsDeliveryRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("顾送编号")
    private String deliveryNo;
    @ApiModelProperty("顾送日期")
    private Date deliveryDate;
    @ApiModelProperty("顾送人姓名")
    private String senderName;
    @ApiModelProperty("性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_XB")
    private String gender;
    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_ZJZL")
    private String idType;
    @ApiModelProperty("证件号码")
    private String idNumber;
    @ApiModelProperty("与被监管人关系")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_SHGX")
    private String relationship;
    @ApiModelProperty("联系方式")
    private String contact;
    @ApiModelProperty("户籍地址")
    private String householdAddress;
    @ApiModelProperty("物品照片存储路径")
    private String goodsPhotoPath;
    @ApiModelProperty("物品信息")
    private String goodsInfo;
    @ApiModelProperty("物品总数")
    private Integer goodsTotal;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_WPGSZT")
    private String status;
    @ApiModelProperty("签收时间")
    private Date receiptTime;
    @ApiModelProperty("签名（存储签名文件路径或文本）")
    private String signature;
    @ApiModelProperty("拒签原因")
    private String rejectionReason;

    @ApiModelProperty("办理人")
    private String addUserName;

    @ApiModelProperty("登记时间")
    private Date addTime;

    @ApiModelProperty("物品顾送列表")
    List<GoodsDeliveryDetailsRespVO> goodsList;
}
