package com.rs.module.acp.controller.admin.gj.vo.punishment;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管教业务-处罚呈批-解除处罚登记")
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentSaveRemoveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("处罚呈批ID")
    private String id;

    @ApiModelProperty("字典编码：ZD_SFCSCSPZ ")
    private String removeReason;

    @ApiModelProperty("备注")
    private String remark;


}
