package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.wb.VisitorInfoDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-对外开放登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VisitorRegSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("来访时间")
    @NotNull(message = "来访时间不能为空")
    private Date visitTime;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("来访事由")
    private String visitReason;

    @ApiModelProperty("来访人员类型")
    private String visitorType;

    @ApiModelProperty("参观区域")
    private String visitArea;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("来访问人列表")
    private List<VisitorInfoSaveReqVO> personList;
}
