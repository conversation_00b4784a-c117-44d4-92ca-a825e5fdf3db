package com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds;

import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-月度考核(戒毒所)新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MonthlyAssmtJdsCommitReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("考核结果")
    private List<AssmtCommonBO> khjg;

    @ApiModelProperty("加分或扣分情况")
    private String bonusOrPenaltyPointsSituati;

    @ApiModelProperty("依据")
    private String according;

}
