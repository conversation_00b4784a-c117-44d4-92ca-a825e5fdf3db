package com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-诊断评估(戒毒所) Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DiagnosisAssmtJdsBusinessIdRespVO extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("手册名称")
    private String name;
    @ApiModelProperty("formId")
    private String formId;
    @ApiModelProperty("businessId")
    private String businessId;
}
