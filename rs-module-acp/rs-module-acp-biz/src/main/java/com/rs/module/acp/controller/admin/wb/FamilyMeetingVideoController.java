package com.rs.module.acp.controller.admin.wb;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.entity.wb.FamilyMeetingDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.FamilyMeetingVideoDO;
import com.rs.module.acp.service.wb.FamilyMeetingVideoService;

@Api(tags = "实战平台-窗口业务-单向视频家属会见")
@RestController
@RequestMapping("/acp/wb/familyMeetingVideo")
@Validated
public class FamilyMeetingVideoController {

    @Resource
    private FamilyMeetingVideoService familyMeetingVideoService;

    @Autowired
    private WbCommonService wbCommonService;


    @GetMapping("/verificationPersonnel")
    @ApiOperation(value = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:verificationPersonnel", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中",
            success = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中成功",
            fail = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<JSONObject> verificationPersonnel(@RequestParam("jgrybm") String jgrybm) {
        return success(wbCommonService.verificationPersonnel(jgrybm,WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO));
    }

    @GetMapping("/limitNumber")
    @ApiOperation(value = "实战平台-窗口业务-校验被监管人员单向视频会见次数是否超过限制")
    @ApiImplicitParam(name = "jgrybm", value = "被监管人员编号")
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:limitNumber", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-校验被监管人员单向视频会见次数是否超过限制",
            success = "实战平台-窗口业务-校验被监管人员单向视频会见次数是否超过限制成功",
            fail = "实战平台-窗口业务-校验被监管人员单向视频会见次数是否超过限制失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<JSONObject> limitNumber(@RequestParam("jgrybm") String jgrybm) {
        return success(familyMeetingVideoService.limitNumber(jgrybm));
    }


    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建单向视频家属会见")
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建单向视频家属会见",
            success = "实战平台-窗口业务-创建单向视频家属会见成功", fail = "实战平台-窗口业务-创建单向视频家属会见失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createFamilyMeetingVideo(@Valid @RequestBody FamilyMeetingVideoSaveReqVO createReqVO) {
        return success(familyMeetingVideoService.createFamilyMeetingVideo(createReqVO));
    }


    @GetMapping("/getremoteNumbering")
    @ApiOperation(value = "实战平台-窗口业务-家属单向视频排号信息")
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:getremoteNumbering", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-家属单向视频排号信息", success = "实战平台-窗口业务-家属单向视频排号信息成功",
            fail = "实战平台-窗口业务-家属单向视频排号信息失败，错误信息：{{#_ret[msg]}}")
    public CommonResult<List<JSONObject>> getremoteNumbering() {
        return success(familyMeetingVideoService.getremoteNumbering());
    }

    @PostMapping("/notifyFamilyMembers")
    @ApiOperation(value = "实战平台-窗口业务-通知家属")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "notificationFamilyTime", value = "通知家属时间（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "notificationMeetingDate", value = "通知会见日期（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:notifyFamilyMembers", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-通知家属",
            success = "实战平台-窗口业务-通知家属成功", fail = "实战平台-窗口业务-通知家属失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")

    public CommonResult<Boolean> notifyFamilyMembers(@RequestBody FamilyMeetingVideoSaveReqVO updateReqVO) {
        if (familyMeetingVideoService.notifyFamilyMembers(updateReqVO)) {
            return success();
        }
        return error("通知家属失败");
    }

    @PostMapping("/meetingRegister")
    @ApiOperation(value = "实战平台-窗口业务-会见登记")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "meetingStartTime", value = "会见开始时间（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "meetingEndTime", value = "会见结束时间（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "remarks", value = "备注", paramType = "query", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:meetingRegister", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-会见登记",
            success = "实战平台-窗口业务-会见登记成功", fail = "实战平台-窗口业务-会见登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_JSHJ,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"经办时间\":\"{{#updateReqVO.addTime}}\"," +
            "\"会见类型\":\"{{#updateReqVO.meetingMethod}}\",\"会见开始时间\":\"{{#fDateTime(#updateReqVO.meetingStartTime)}}\"," +
            "\"会见结束时间\":\"{{#fDateTime(#updateReqVO.meetingEndTime)}}\"}")
    public CommonResult<Boolean> meetingRegister(@RequestBody FamilyMeetingVideoSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        FamilyMeetingVideoDO familyMeetingVideoDO = familyMeetingVideoService.getById(updateReqVO.getId());
        updateReqVO.setAddTime(DateUtil.format(familyMeetingVideoDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        updateReqVO.setMeetingMethod("单向视频");
        updateReqVO.setJgrybm(familyMeetingVideoDO.getJgrybm());
        /****记录轨迹 end****/
        if (familyMeetingVideoService.meetingRegister(updateReqVO)) {
            return success();
        }
        return error("通知家属失败");
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-根据ID获得单向视频家属会见")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:get", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-根据ID获得单向视频家属会见",
            success = "实战平台-窗口业务-根据ID获得单向视频家属会见成功",
            fail = "实战平台-窗口业务-根据ID获得单向视频家属会见失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<FamilyMeetingVideoRespVO> getFamilyMeetingVideo(@RequestParam("id") String id) {
        return success(familyMeetingVideoService.getFamilyMeetingVideoById(id));
    }

    @PostMapping("/getHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-根据监管人员编码获取监管人员历史单向视频会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:getHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-根据监管人员编码获取监管人员历史单向视频会见记录",
            success = "实战平台-窗口业务-根据监管人员编码获取监管人员历史单向视频会见记录成功",
            fail = "实战平台-窗口业务-根据监管人员编码获取监管人员历史单向视频会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<FamilyMeetingVideoRespVO>> getFamilyMeetingVideoPage(@RequestParam("jgrybm") String jgrybm,
                                                                                        @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                                        @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(familyMeetingVideoService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize));
    }

    @GetMapping("/getTrajectory")
    @ApiOperation(value = "实战平台-窗口业务-获得家属当面会见轨迹")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:getTrajectory", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得家属当面会见轨迹",
            success = "实战平台-窗口业务-获得家属当面会见轨迹成功", fail = "实战平台-窗口业务-获得家属当面会见轨迹失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<List<JSONObject>> getTrajectory(@RequestParam("id") String id) {
        return success(wbCommonService.getTrajectory(id,WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO));
    }

    @GetMapping("/getNewHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:familyMeetingVideo:getNewHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录",
            success = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录成功",
            fail = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<JSONObject>> getNewHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                             @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(wbCommonService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize, WbConstants.BUSINESS_TYPE_FAMILY_MEETING_VIDEO));
    }
}
