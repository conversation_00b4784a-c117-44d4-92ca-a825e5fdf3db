package com.rs.module.acp.controller.admin.wb;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.LawyerPrisonerDO;
import com.rs.module.acp.service.wb.LawyerPrisonerService;

@Api(tags = "实战平台-窗口业务-律师关联被监管人员")
@RestController
@RequestMapping("/acp/wb/lawyerPrisoner")
@Validated
public class LawyerPrisonerController {

    @Resource
    private LawyerPrisonerService lawyerPrisonerService;

    @PostMapping("/updateLawyerPrisoner")
    @ApiOperation(value = "实战平台-窗口业务-更新律师关联被监管人员（在律师详情页面使用）")
    @LogRecordAnnotation(bizModule = "acp:lawyerPrisoner:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新律师关联被监管人员",
            success = "实战平台-窗口业务-更新律师关联被监管人员成功", fail = "实战平台-窗口业务-更新律师关联被监管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateLawyerPrisoner(@Valid @RequestBody LawyerPrisonerSaveReqVO updateReqVO) {
        lawyerPrisonerService.updateLawyerPrisoner(updateReqVO);
        return success(true);
    }

    @GetMapping("/getPrisonerListByLawyerId")
    @ApiOperation(value = "实战平台-窗口业务-根据律师ID获取律师关联的被监管人员")
    @ApiImplicitParam(name = "lawyerId", value = "律师id")
    @LogRecordAnnotation(bizModule = "acp:lawyerPrisoner:getPrisonerListByLawyerId", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据律师ID获取律师关联的被监管人员",
            success = "实战平台-窗口业务-根据律师ID获取律师关联的被监管人员成功", fail = "实战平台-窗口业务-根据律师ID获取律师关联的被监管人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#lawyerId}}")
    public CommonResult<List<LawyerPrisonerRespVO>> getPrisonerListByLawyerId(@RequestParam("lawyerId") String lawyerId) {
        return success(lawyerPrisonerService.getPrisonerListByLawyerId(lawyerId));
    }

    @GetMapping("/sameCaseJudgmentByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-根据监管人员编码判断是否是同案人员")
    @ApiImplicitParam(name = "jgrybms", value = "监管人员编码，多个使用英文逗号隔开")
    @LogRecordAnnotation(bizModule = "acp:lawyerPrisoner:getPrisonerListByLawyerId", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据监管人员编码判断是否是同案人员",
            success = "实战平台-窗口业务-根据监管人员编码判断是否是同案人员成功", fail = "实战平台-窗口业务-根据监管人员编码判断是否是同案人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#lawyerId}}")
    public CommonResult<JSONObject> sameCaseJudgmentByJgrybm(@RequestParam("jgrybms") String jgrybms) {
        return success(lawyerPrisonerService.sameCaseJudgmentByJgrybm(jgrybms));
    }

}
