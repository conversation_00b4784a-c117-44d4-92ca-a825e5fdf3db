package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-来访人员信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VisitorInfoRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("对外开放登记ID")
    private String visitorRegId;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("证件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_ZJZL")
    private String idType;
    @ApiModelProperty("证件号码")
    private String idNumber;
}
