package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-社会关系 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SocialRelationsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("家属姓名")
    private String name;
    @ApiModelProperty("家属性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_XB")
    private String gender;
    @ApiModelProperty("家属证件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_ZJZL")
    private String idType;
    @ApiModelProperty("家属证件号码")
    private String idNumber;
    @ApiModelProperty("家属与被会见人社会关系")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_SHGX")
    private String relationship;
    @ApiModelProperty("家属联系方式")
    private String contact;
    @ApiModelProperty("家属工作单位")
    private String workUnit;
    @ApiModelProperty("家属居住地址")
    private String address;
    @ApiModelProperty("职业")
    private String occupation;
    @ApiModelProperty("照片")
    private String imageUrl;
    @ApiModelProperty("关系证明附件")
    private String relationsAttch;
}
