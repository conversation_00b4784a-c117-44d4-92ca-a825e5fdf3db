package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.time.LocalTime;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabCoursesRecordRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("教育康复课程计划ID")
    private String edurehabCoursesPlanId;
    @ApiModelProperty("课程日期")
    private Date coursesDate;

    @ApiModelProperty("课程日期 String类型")
    private String coursesDateStr;

    @ApiModelProperty("监区ID")
    private String areaId;
    @ApiModelProperty("监区名称")
    private String areaName;
    @ApiModelProperty("课程时段编码，如“9:00-10:00”，唯一标识时段")
    private String timeSlotCode;
    @ApiModelProperty("课程时段-开始")
    private String timeSlotStartTime;
    @ApiModelProperty("课程时段-结束")
    private String timeSlotEndTime;
    @ApiModelProperty("courses_code")
    private String coursesCode;
    @ApiModelProperty("courses_name")
    private String coursesName;
    @ApiModelProperty("coursesColor")
    private String coursesColor;
}
