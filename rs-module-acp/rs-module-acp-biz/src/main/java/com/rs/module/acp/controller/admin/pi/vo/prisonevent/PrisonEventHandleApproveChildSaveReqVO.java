package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情处置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventHandleApproveChildSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("业务处置Id")
    @NotEmpty(message = "处置ID不能为空")
    private String handleId;

    @NotEmpty(message = "岗位编码")
    private String handlePostCode;

    @NotEmpty(message = "岗位名称")
    private String handlePostName;

    @ApiModelProperty("审批结果 0 不同意,1 同意")
    @NotEmpty(message = "审批结果不能为空")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

//    @ApiModelProperty("是否需要审批（0：需要，1：不需要）")
//    private String isApprove;

}
