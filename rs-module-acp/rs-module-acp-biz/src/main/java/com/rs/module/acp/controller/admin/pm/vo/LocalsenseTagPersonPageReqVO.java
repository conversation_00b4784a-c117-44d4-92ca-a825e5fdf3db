package com.rs.module.acp.controller.admin.pm.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-定位标签与人员绑定分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LocalsenseTagPersonPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("绑定来源")
    private String bindSource;

    @ApiModelProperty("标签id")
    private String tagId;

    @ApiModelProperty("人员标签类型，01：被监管人员，02：民警")
    private String personType;

    @ApiModelProperty("绑定人员ID")
    private String bindPersonId;

    @ApiModelProperty("bind_person_name")
    private String bindPersonName;

    @ApiModelProperty("绑定时间")
    private Date[] bindTime;

    @ApiModelProperty("解绑时间")
    private Date[] unbindTime;

    @ApiModelProperty("解绑原因")
    private String unbindReason;

    @ApiModelProperty("状态")
    private String status;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
