package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师违规 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerViolationRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("律师表ID")
    private String lawyerId;
    @ApiModelProperty("违规时间")
    private Date violationTime;
    @ApiModelProperty("违规情况")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_WGQK")
    private String violationType;
    @ApiModelProperty("详细说明")
    private String description;
    @ApiModelProperty("添加人")
    private String addUserName;
    @ApiModelProperty("添加时间")
    private Date addTime;
}
