package com.rs.module.acp.controller.admin.gj;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigRespVO;
import com.rs.module.acp.controller.admin.gj.vo.face2face.FaceToFaceConfigSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.FaceToFaceConfigDO;
import com.rs.module.acp.service.gj.face2face.FaceToFaceConfigService;

@Api(tags = "实战平台-管教业务-面对面配置")
@RestController
@RequestMapping("/acp/gj/faceToFaceConfig")
@Validated
public class FaceToFaceConfigController {

    @Resource
    private FaceToFaceConfigService faceToFaceConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-面对面配置")
    public CommonResult<String> createFaceToFaceConfig(@Valid @RequestBody FaceToFaceConfigSaveReqVO createReqVO) {
        return success(faceToFaceConfigService.createFaceToFaceConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-面对面配置")
    public CommonResult<Boolean> updateFaceToFaceConfig(@Valid @RequestBody FaceToFaceConfigSaveReqVO updateReqVO) {
        faceToFaceConfigService.updateFaceToFaceConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-面对面配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteFaceToFaceConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           faceToFaceConfigService.deleteFaceToFaceConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-面对面配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<FaceToFaceConfigRespVO> getFaceToFaceConfig(@RequestParam("id") String id) {
        FaceToFaceConfigDO faceToFaceConfig = faceToFaceConfigService.getFaceToFaceConfig(id);
        return success(BeanUtils.toBean(faceToFaceConfig, FaceToFaceConfigRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-面对面配置分页")
    public CommonResult<PageResult<FaceToFaceConfigRespVO>> getFaceToFaceConfigPage(@Valid @RequestBody FaceToFaceConfigPageReqVO pageReqVO) {
        PageResult<FaceToFaceConfigDO> pageResult = faceToFaceConfigService.getFaceToFaceConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FaceToFaceConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-面对面配置列表")
    public CommonResult<List<FaceToFaceConfigRespVO>> getFaceToFaceConfigList(@Valid @RequestBody FaceToFaceConfigListReqVO listReqVO) {
        List<FaceToFaceConfigDO> list = faceToFaceConfigService.getFaceToFaceConfigList(listReqVO);
        return success(BeanUtils.toBean(list, FaceToFaceConfigRespVO.class));
    }

    @GetMapping("/listByOrgCode")
    @ApiOperation(value = "获得实战平台-管教业务-面对面配置列表-OrgCode")
    public CommonResult<List<FaceToFaceConfigRespVO>> getFaceToFaceConfigList() {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        List<FaceToFaceConfigDO> list = faceToFaceConfigService.selectListByOrgCode(sessionUser.getOrgCode());
        return success(BeanUtils.toBean(list, FaceToFaceConfigRespVO.class));
    }
}
