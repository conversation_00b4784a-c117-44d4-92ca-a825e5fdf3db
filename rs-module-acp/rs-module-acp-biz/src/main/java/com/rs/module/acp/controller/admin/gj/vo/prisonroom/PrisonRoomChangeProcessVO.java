package com.rs.module.acp.controller.admin.gj.vo.prisonroom;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonRoomChangeProcessVO extends BaseVO {
private static final long serialVersionUID = 1L;


    @ApiModelProperty("ID记录")
    @NotBlank(message = "ID不能为空")
    private String id;

    @NotBlank(message = "状态不能为空")
    @ApiModelProperty("状态")
    private String status;

    @NotBlank(message = "流程实例ID不能为空")
    @ApiModelProperty("流程实例ID")
    private String actInstId;

    @NotBlank(message = "")
    @ApiModelProperty("任务ID，不能为空！")
    private String taskId;


}
