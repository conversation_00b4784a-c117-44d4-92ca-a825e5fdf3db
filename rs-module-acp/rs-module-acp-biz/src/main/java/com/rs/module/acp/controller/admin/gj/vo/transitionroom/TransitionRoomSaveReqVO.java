package com.rs.module.acp.controller.admin.gj.vo.transitionroom;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-过渡监室管理新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class TransitionRoomSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    //@ApiModelProperty("主键")
    //private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("过渡开始日期")
    @NotNull(message = "过渡开始日期不能为空")
    private Date startDate;

    @ApiModelProperty("过渡结束日期")
    @NotNull(message = "过渡结束日期不能为空")
    private Date endDate;

    //@ApiModelProperty("实际开始日期")
    //@NotNull(message = "实际开始日期不能为空")
    //private Date actualStartDate;
    //
    //@ApiModelProperty("实际结束日期")
    //@NotNull(message = "实际结束日期不能为空")
    //private Date actualEndDate;

    @ApiModelProperty("过渡监室id")
    @NotEmpty(message = "过渡监室id不能为空")
    private String roomId;

    @ApiModelProperty("过渡监室名称")
    @NotEmpty(message = "过渡监室名称不能为空")
    private String roomName;

    //@ApiModelProperty("是否延长")
    //@NotNull(message = "是否延长不能为空")
    //private Short isExtend;
    //
    //@ApiModelProperty("状态")
    //@NotEmpty(message = "状态不能为空")
    //private String status;

}
