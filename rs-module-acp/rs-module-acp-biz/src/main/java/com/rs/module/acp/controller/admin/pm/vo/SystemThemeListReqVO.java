package com.rs.module.acp.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-主题-主题配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemThemeListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主题")
    private String theme;

}
