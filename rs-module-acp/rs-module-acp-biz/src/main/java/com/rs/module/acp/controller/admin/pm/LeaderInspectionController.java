package com.rs.module.acp.controller.admin.pm;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.pm.vo.*;
import com.rs.module.acp.entity.pm.LeaderInspectionDO;
import com.rs.module.acp.service.pm.LeaderInspectionService;

@Api(tags = "领导巡视")
@RestController
@RequestMapping("/acp/pm/leaderInspection")
@Validated
public class LeaderInspectionController {

    @Resource
    private LeaderInspectionService leaderInspectionService;

    @PostMapping("/create")
    @ApiOperation(value = "创建领导巡视")
    public CommonResult<String> createLeaderInspection(@Valid @RequestBody LeaderInspectionSaveReqVO createReqVO) {
        return success(leaderInspectionService.createLeaderInspection(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新领导巡视")
    public CommonResult<Boolean> updateLeaderInspection(@Valid @RequestBody LeaderInspectionSaveReqVO updateReqVO) {
        leaderInspectionService.updateLeaderInspection(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除领导巡视")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteLeaderInspection(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           leaderInspectionService.deleteLeaderInspection(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得领导巡视")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<LeaderInspectionRespVO> getLeaderInspection(@RequestParam("id") String id) {
        LeaderInspectionDO leaderInspection = leaderInspectionService.getLeaderInspection(id);
        return success(BeanUtils.toBean(leaderInspection, LeaderInspectionRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得领导巡视分页")
    public CommonResult<PageResult<LeaderInspectionRespVO>> getLeaderInspectionPage(@Valid @RequestBody LeaderInspectionPageReqVO pageReqVO) {
        PageResult<LeaderInspectionDO> pageResult = leaderInspectionService.getLeaderInspectionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LeaderInspectionRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得领导巡视列表")
    public CommonResult<List<LeaderInspectionRespVO>> getLeaderInspectionList(@Valid @RequestBody LeaderInspectionListReqVO listReqVO) {
        List<LeaderInspectionDO> list = leaderInspectionService.getLeaderInspectionList(listReqVO);
        return success(BeanUtils.toBean(list, LeaderInspectionRespVO.class));
    }

    /**
     * 返回一个list，该list里有个map，map里包含一个key和一个value，都为string类型
     */
    @GetMapping("/getRoomTypeList")
    @ApiOperation(value = "获得监室类型列表")
    public CommonResult<List<DictRespVO>> getList() {
        List<DictRespVO> dictList = new ArrayList<>();

        dictList.add(new DictRespVO("15", "留置"));
        dictList.add(new DictRespVO("4", "病号"));
        dictList.add(new DictRespVO("5", "严管"));
        dictList.add(new DictRespVO("14", "医疗"));
        dictList.add(new DictRespVO("8", "刑拘"));
        dictList.add(new DictRespVO("9", "逮捕"));
        dictList.add(new DictRespVO("0", "普通"));
        dictList.add(new DictRespVO("19", "单独关押"));
        dictList.add(new DictRespVO("11", "未决"));
        dictList.add(new DictRespVO("16", "出所就医"));
        dictList.add(new DictRespVO("12", "戒毒代管"));
        dictList.add(new DictRespVO("1", "留所服刑"));
        dictList.add(new DictRespVO("10", "已决"));
        dictList.add(new DictRespVO("2", "未成年"));
        dictList.add(new DictRespVO("18", "医生办公室"));
        dictList.add(new DictRespVO("13", "禁闭"));
        dictList.add(new DictRespVO("3", "重点"));
        dictList.add(new DictRespVO("17", "传染病"));
        dictList.add(new DictRespVO("6", "宽管"));
        dictList.add(new DictRespVO("7", "过渡"));
        return success(dictList);
    }
}
