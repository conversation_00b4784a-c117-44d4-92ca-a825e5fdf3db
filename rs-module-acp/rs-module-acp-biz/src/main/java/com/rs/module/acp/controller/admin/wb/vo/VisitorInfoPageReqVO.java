package com.rs.module.acp.controller.admin.wb.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-来访人员信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorInfoPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("对外开放登记ID")
    private String visitorRegId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件号码")
    private String idNumber;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
