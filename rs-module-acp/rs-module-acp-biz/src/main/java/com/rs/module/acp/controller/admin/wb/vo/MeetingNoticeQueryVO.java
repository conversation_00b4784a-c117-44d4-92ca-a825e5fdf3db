package com.rs.module.acp.controller.admin.wb.vo;

import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-会见通知 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingNoticeQueryVO extends PageParam{


    @ApiModelProperty("被监管人员编码")
    private String jgrybm;

    @ApiModelProperty("开始时间")
    private Date queryStartTime;

    @ApiModelProperty("结束时间")
    private Date queryEndTime;

    @ApiModelProperty("无需传值")
    private String orgCode;

    @ApiModelProperty("查询时间（0：全部，1：今天，2：昨天，3：近一周，4：本周，5：近一个月，6：本月，7：近一年，8：本年）")
    private String queryTimeType;
}
