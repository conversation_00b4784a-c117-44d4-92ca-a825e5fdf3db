package com.rs.module.acp.controller.admin.pi.vo.violationrecord;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-违规登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ViolationRecordListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("巡控室ID")
    private String patrolRoomId;

    @ApiModelProperty("巡控室名称")
    private String patrolRoomName;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("违规内容")
    private String violationContent;

    @ApiModelProperty("处置情况")
    private String disposalSituation;

    @ApiModelProperty("是否岗位协同：1-是/0-否")
    private Short isPostCoordination;

    @ApiModelProperty("岗位协同人员，多选项")
    private String coordinationPosts;

    @ApiModelProperty("岗位协同人员名称")
    private String coordinationPostsName;

    @ApiModelProperty("上传附件路径，存储附件文件路径")
    private String attachment;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @ApiModelProperty("登记经办时间")
    private Date[] operatorTime;

}
