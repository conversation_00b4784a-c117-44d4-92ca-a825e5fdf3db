package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.samecasemanage.*;
import com.rs.module.acp.service.gj.samecasemanage.bo.SameCaseManageBO;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.SameCaseManageDO;
import com.rs.module.acp.service.gj.samecasemanage.SameCaseManageService;

@Api(tags = "实战平台-管教业务-同案人员管理")
@RestController
@RequestMapping("/acp/gj/sameCaseManage")
@Validated
public class SameCaseManageController {

    @Resource
    private SameCaseManageService sameCaseManageService;

    @PostMapping("/create")
    @ApiOperation(value = "添加")
    public CommonResult<String> createSameCaseManage(@Valid @RequestBody SameCaseManageSaveReqVO createReqVO) {
        return success(sameCaseManageService.createSameCaseManage(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-同案人员管理")
    public CommonResult<Boolean> updateSameCaseManage(@Valid @RequestBody SameCaseManageSaveReqVO updateReqVO) {
        sameCaseManageService.updateSameCaseManage(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSameCaseManage(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           sameCaseManageService.deleteSameCaseManage(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-同案人员管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SameCaseManageRespVO> getSameCaseManage(@RequestParam("id") String id) {
        SameCaseManageDO sameCaseManage = sameCaseManageService.getSameCaseManage(id);
        return success(BeanUtils.toBean(sameCaseManage, SameCaseManageRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-同案人员管理分页")
    public CommonResult<PageResult<SameCaseManageRespVO>> getSameCaseManagePage(@Valid @RequestBody SameCaseManagePageReqVO pageReqVO) {
        PageResult<SameCaseManageDO> pageResult = sameCaseManageService.getSameCaseManagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SameCaseManageRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-同案人员管理列表")
    public CommonResult<List<SameCaseManageRespVO>> getSameCaseManageList(@Valid @RequestBody SameCaseManageListReqVO listReqVO) {
        List<SameCaseManageDO> list = sameCaseManageService.getSameCaseManageList(listReqVO);
        return success(BeanUtils.toBean(list, SameCaseManageRespVO.class));
    }

    @GetMapping("/manageListPage")
    @ApiOperation(value = "详情中-同案人员列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "每页大小"),
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    })
    public CommonResult<PageResult<SameCaseManageRyxxRespVO>> manageListPage(
            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "jgrybm") String jgrybm) {
        PageResult<SameCaseManageBO> pageResult = sameCaseManageService.manageListPage(pageNo, pageSize, jgrybm);
        return success(BeanUtils.toBean(pageResult, SameCaseManageRyxxRespVO.class));
    }


}
