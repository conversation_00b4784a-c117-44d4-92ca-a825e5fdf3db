package com.rs.module.acp.controller.admin.gj.vo.inoutrecords;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-出入登记-实战平台通用保存 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class InOutRecordsACPSaveVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("业务id用来做businessId 不是出入记录ID")
    private String id;
    @ApiModelProperty("出入监室时间")
    @NotNull(message = "出入监室时间不能为空")
    private Date inoutTime;

    @ApiModelProperty("带出带入民警身份证号")
    private String inoutPoliceSfzh;

    @ApiModelProperty("带出带入民警")
    private String inoutPolice;

    @ApiModelProperty("检查结果")
    private String inspectionResult;

    @ApiModelProperty("业务ID")
    private String businessId;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("管教民警身份证号")
    @NotEmpty(message = "管教民警身份证号不能为空")
    private String supervisorPoliceSfzh;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("管教民警姓名")
    @NotEmpty(message = "管教民警姓名不能为空")
    private String supervisorPolice;

    @ApiModelProperty("异常情况登记")
    private String abnormalSituations;

    @ApiModelProperty("管教民警人脸信息存储路径")
    private String supervisorFaceInfoPath;

    @ApiModelProperty("违禁物品登记照片存储地址")
    private String prohibitedItemsImgUrl;

    @ApiModelProperty("被监管人员人脸信息存储路径")
    private String prisonerFaceInfoPath;

    @ApiModelProperty("体表检查登记照片存储地址")
    private String physicalExamImgUrl;

    @ApiModelProperty("异常情况登记照片存储地址")
    private String abnormalSituationsImgUrl;

    @ApiModelProperty("是否查出违禁物品")
    private Short isProhibitedItems;

    @ApiModelProperty("检查时间")
    private Date inspectionTime;

    @ApiModelProperty("检查民警身份证号")
    private String inspectorSfzh;

    @ApiModelProperty("检查民警身份证号")
    private String inspector;

    @ApiModelProperty("带入监室时间")
    @NotNull(message = "带入监室时间不能为空")
    private Date inTime;

    @ApiModelProperty("带入监室id")
    private String inRoomId;
    @ApiModelProperty("业务类型子类型")
    private String businessSubType;
}
