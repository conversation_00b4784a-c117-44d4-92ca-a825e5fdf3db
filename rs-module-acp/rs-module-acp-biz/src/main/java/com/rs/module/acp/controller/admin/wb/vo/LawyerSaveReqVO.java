package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String xm;

    @ApiModelProperty("性别")
    @NotEmpty(message = "性别不能为空")
    private String xb;

    @ApiModelProperty("证件类型")
    private String zjlx;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("联系方式")
    @NotEmpty(message = "联系方式不能为空")
    private String lxfs;

    @ApiModelProperty("律师类型")
    @NotEmpty(message = "律师类型不能为空")
    private String lslx;

    @ApiModelProperty("执业证号码")
    @NotEmpty(message = "执业证号码不能为空")
    private String zyzhm;

    @ApiModelProperty("执业证有效期")
    private Date ksZyzyxq;

    @ApiModelProperty("执业证有效期-结束")
    private Date jsZyzyxq;

    @ApiModelProperty("律师单位")
    @NotEmpty(message = "律师单位不能为空")
    private String lsdw;

    @ApiModelProperty("照片存储url")
    private String zpUrl;

    @ApiModelProperty("执业证书url")
    private String zyzsUrl;

    @ApiModelProperty("关联被监管人员列表")
    List<LawyerPrisonerSaveReqVO> prisonerList;
}
