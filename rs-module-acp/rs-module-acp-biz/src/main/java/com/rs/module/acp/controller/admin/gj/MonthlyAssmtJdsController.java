package com.rs.module.acp.controller.admin.gj;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.*;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.MonthlyAssmtJdsDO;
import com.rs.module.acp.service.gj.diagnosiassmtjds.MonthlyAssmtJdsService;

@Api(tags = "实战平台-管教业务-月度考核(戒毒所)")
@RestController
@RequestMapping("/acp/gj/monthlyAssmtJds")
@Validated
public class MonthlyAssmtJdsController {

    @Resource
    private MonthlyAssmtJdsService monthlyAssmtJdsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-月度考核(戒毒所)")
    public CommonResult<String> createMonthlyAssmtJds(@Valid @RequestBody MonthlyAssmtJdsSaveReqVO createReqVO) {
        return success(monthlyAssmtJdsService.createMonthlyAssmtJds(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-月度考核(戒毒所)")
    public CommonResult<Boolean> updateMonthlyAssmtJds(@Valid @RequestBody MonthlyAssmtJdsSaveReqVO updateReqVO) {
        monthlyAssmtJdsService.updateMonthlyAssmtJds(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-月度考核(戒毒所)")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteMonthlyAssmtJds(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            monthlyAssmtJdsService.deleteMonthlyAssmtJds(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-月度考核(戒毒所)")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MonthlyAssmtJdsRespVO> getMonthlyAssmtJds(@RequestParam("id") String id) {
        MonthlyAssmtJdsDO monthlyAssmtJds = monthlyAssmtJdsService.getMonthlyAssmtJds(id);
        return success(BeanUtils.toBean(monthlyAssmtJds, MonthlyAssmtJdsRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-月度考核(戒毒所)分页")
    public CommonResult<PageResult<MonthlyAssmtJdsRespVO>> getMonthlyAssmtJdsPage(@Valid @RequestBody MonthlyAssmtJdsPageReqVO pageReqVO) {
        PageResult<MonthlyAssmtJdsDO> pageResult = monthlyAssmtJdsService.getMonthlyAssmtJdsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MonthlyAssmtJdsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-月度考核(戒毒所)列表")
    public CommonResult<List<MonthlyAssmtJdsRespVO>> getMonthlyAssmtJdsList(@Valid @RequestBody MonthlyAssmtJdsListReqVO listReqVO) {
        List<MonthlyAssmtJdsDO> list = monthlyAssmtJdsService.getMonthlyAssmtJdsList(listReqVO);
        return success(BeanUtils.toBean(list, MonthlyAssmtJdsRespVO.class));
    }

    @GetMapping("/getModel")
    @ApiOperation(value = "获得考核内容模板")
    public CommonResult<List<AssmtCommonBO>> getDiagnosisAssmtJds() {
        return success(monthlyAssmtJdsService.getModel());
    }

    @PostMapping("/commit")
    @ApiOperation(value = "登记提交")
    public CommonResult<Boolean> commit(@Valid @RequestBody MonthlyAssmtJdsCommitReqVO createReqVO) {
        Assert.notEmpty(createReqVO.getKhjg(), "考核内容不能为空");
        monthlyAssmtJdsService.commit(createReqVO);
        return success(true);
    }

    @GetMapping("/getHisRecords")
    @ApiOperation(value = "历史月度考核记录列表")
    @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码")
    public CommonResult<List<MonthlyAssmtJdsRespVO>> getRecordByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        List<MonthlyAssmtJdsDO> list = monthlyAssmtJdsService.getRecordByJgrybm(jgrybm);
        return success(BeanUtils.toBean(list, MonthlyAssmtJdsRespVO.class));
    }

}
