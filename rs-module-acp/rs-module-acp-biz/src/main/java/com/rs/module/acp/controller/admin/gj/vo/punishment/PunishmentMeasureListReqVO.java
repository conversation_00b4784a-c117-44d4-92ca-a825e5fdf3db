package com.rs.module.acp.controller.admin.gj.vo.punishment;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-处罚呈批关联措施列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentMeasureListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("处罚呈批ID")
    private String punishmentId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("处罚措施")
    private String measures;

    @ApiModelProperty("处罚周期开始日期")
    private Date[] startDate;

    @ApiModelProperty("处罚周期结束日期")
    private Date[] endDate;

    @ApiModelProperty("处罚时长，自动生成（天-时-分钟）")
    private Integer duration;

    @ApiModelProperty("处罚业务ID")
    private String businessId;

}
