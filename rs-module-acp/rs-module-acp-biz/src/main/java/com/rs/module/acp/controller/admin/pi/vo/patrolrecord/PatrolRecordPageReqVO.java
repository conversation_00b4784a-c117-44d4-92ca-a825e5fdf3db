package com.rs.module.acp.controller.admin.pi.vo.patrolrecord;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-巡视登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PatrolRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("巡控室ID")
    private String patrolRoomId;

    @ApiModelProperty("巡控室名称")
    private String patrolRoomName;

    @ApiModelProperty("监室号")
    private String roomId;

    @ApiModelProperty("登记类型")
    private String recordType;

    @ApiModelProperty("巡控人员身份证号")
    private String patrolStaffSfzh;

    @ApiModelProperty("巡控人员")
    private String patrolStaff;

    @ApiModelProperty("登记内容")
    private String recordContent;

    @ApiModelProperty("是否岗位协同：1-是/0-否")
    private Short isPostCoordination;

    @ApiModelProperty("岗位协同人员，多选项")
    private String coordinationPosts;

    @ApiModelProperty("岗位协同人员名称")
    private String coordinationPostsName;

    @ApiModelProperty("待办来源")
    private String todoSource;

    @ApiModelProperty("待办事由")
    private String todoReason;

    @ApiModelProperty("待办推送人身份证号")
    private String todoPersonSfzh;

    @ApiModelProperty("待办推送人")
    private String todoPerson;

    @ApiModelProperty("待办推送岗位")
    private String todoPost;

    @ApiModelProperty("待办推送时间")
    private Date[] todoPushTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("登记经办人")
    private String operatorSfzh;

    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;

    @ApiModelProperty("登记经办时间")
    private Date[] operatorTime;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
