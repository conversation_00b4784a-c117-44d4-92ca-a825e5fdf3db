package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordRespVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabCoursesRecordSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.EdurehabCoursesRecordDO;
import com.rs.module.acp.service.gj.edurehabcourses.EdurehabCoursesRecordService;

@Api(tags = "实战平台-管教业务-教育康复课程记录")
@RestController
@RequestMapping("/acp/gj/edurehabCoursesRecord")
@Validated
public class EdurehabCoursesRecordController {

    @Resource
    private EdurehabCoursesRecordService edurehabCoursesRecordService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-教育康复课程记录")
    public CommonResult<String> createEdurehabCoursesRecord(@Valid @RequestBody EdurehabCoursesRecordSaveReqVO createReqVO) {
        return success(edurehabCoursesRecordService.createEdurehabCoursesRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-教育康复课程记录")
    public CommonResult<Boolean> updateEdurehabCoursesRecord(@Valid @RequestBody EdurehabCoursesRecordSaveReqVO updateReqVO) {
        edurehabCoursesRecordService.updateEdurehabCoursesRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-教育康复课程记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEdurehabCoursesRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           edurehabCoursesRecordService.deleteEdurehabCoursesRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EdurehabCoursesRecordRespVO> getEdurehabCoursesRecord(@RequestParam("id") String id) {
        EdurehabCoursesRecordDO edurehabCoursesRecord = edurehabCoursesRecordService.getEdurehabCoursesRecord(id);
        return success(BeanUtils.toBean(edurehabCoursesRecord, EdurehabCoursesRecordRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程记录分页")
    public CommonResult<PageResult<EdurehabCoursesRecordRespVO>> getEdurehabCoursesRecordPage(@Valid @RequestBody EdurehabCoursesRecordPageReqVO pageReqVO) {
        PageResult<EdurehabCoursesRecordDO> pageResult = edurehabCoursesRecordService.getEdurehabCoursesRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EdurehabCoursesRecordRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复课程记录列表")
    public CommonResult<List<EdurehabCoursesRecordRespVO>> getEdurehabCoursesRecordList(@Valid @RequestBody EdurehabCoursesRecordListReqVO listReqVO) {
        List<EdurehabCoursesRecordDO> list = edurehabCoursesRecordService.getEdurehabCoursesRecordList(listReqVO);
        return success(BeanUtils.toBean(list, EdurehabCoursesRecordRespVO.class));
    }
}
