package com.rs.module.acp.controller.admin.gj.vo.doublecheck;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-双重检查登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DoubleCheckSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("检查结果 字典 ZD_PASS_TYPE 1: 通过  0：不通过")
    @NotEmpty(message = "检查结果不能为空")
    private String inspectionResult;

    @ApiModelProperty("违禁物品登记")
    private String prohibitedItems;

    @ApiModelProperty("体表检查登记")
    private String physicalExam;

    @ApiModelProperty("经办民警身份证号, 若不传，后台会从session获取")
    private String operatePoliceSfzh;

    @ApiModelProperty("经办民警, 若不传，后台会从session获取")
    private String operatePolice;

    @ApiModelProperty("经办时间, 若不传，默认后台时间")
    private Date operateTime;

}
