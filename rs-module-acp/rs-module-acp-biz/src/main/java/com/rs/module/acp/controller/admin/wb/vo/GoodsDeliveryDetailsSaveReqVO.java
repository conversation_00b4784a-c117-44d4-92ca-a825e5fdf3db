package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-物品顾送明细新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsDeliveryDetailsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("物品顾送ID")
    private String goodsDeliveryId;

    @ApiModelProperty("物品名称")
    @NotEmpty(message = "物品名称不能为空")
    private String goodsName;

    @ApiModelProperty("物品数量")
    @NotNull(message = "物品数量不能为空")
    private Integer goodsQuantity;

    @ApiModelProperty("单位")
    @NotEmpty(message = "单位不能为空")
    private String unit;

    @ApiModelProperty("物品备注")
    private String remark;

}
