package com.rs.module.acp.controller.admin.wb.vo;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-对外开放登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorRegPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("来访时间")
    private Date[] visitTime;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("来访事由")
    private String visitReason;

    @ApiModelProperty("来访人员类型")
    private String visitorType;

    @ApiModelProperty("参观区域")
    private String visitArea;

    @ApiModelProperty("备注")
    private String remark;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
