package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件类型明细项新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventTypeItemSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键--新增无需传入")
    private String id;

    @ApiModelProperty("事件项名称")
    private String itemName;

    @ApiModelProperty("上级ID--新增无需传入")
    private String parentId;

    @ApiModelProperty("级别代码（1：一级，2：二级，3：三级）")
    private Integer levelCode;

    @ApiModelProperty("扣分值")
    private Integer deductPoint;

    @ApiModelProperty("排序号")
    private Integer orderId;

    @ApiModelProperty("所情事件类型ID--新增无需传入")
    private String typeId;

    @ApiModelProperty("子级事件数据--新增无需传入")
    List<PrisonEventTypeItemSaveReqVO> childList;

}
