package com.rs.module.acp.controller.admin.gj;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds.*;
import com.rs.module.acp.job.diagnosisassmtjds.DiagnosisAssmtJdsJob;
import com.rs.module.acp.service.gj.diagnosiassmtjds.bo.AssmtCommonBO;
import io.swagger.annotations.ApiImplicitParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.DiagnosisAssmtJdsDO;
import com.rs.module.acp.service.gj.diagnosiassmtjds.DiagnosisAssmtJdsService;

@Api(tags = "实战平台-管教业务-诊断评估(戒毒所)")
@RestController
@RequestMapping("/acp/gj/diagnosisAssmtJds")
@Validated
public class DiagnosisAssmtJdsController {

    @Resource
    private DiagnosisAssmtJdsService diagnosisAssmtJdsService;

    @Resource
    private DiagnosisAssmtJdsJob diagnosisAssmtJdsJob;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-诊断评估(戒毒所)")
    public CommonResult<String> createDiagnosisAssmtJds(@Valid @RequestBody DiagnosisAssmtJdsSaveReqVO createReqVO) {
        return success(diagnosisAssmtJdsService.createDiagnosisAssmtJds(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-诊断评估(戒毒所)")
    public CommonResult<Boolean> updateDiagnosisAssmtJds(@Valid @RequestBody DiagnosisAssmtJdsSaveReqVO updateReqVO) {
        diagnosisAssmtJdsService.updateDiagnosisAssmtJds(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-诊断评估(戒毒所)")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteDiagnosisAssmtJds(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            diagnosisAssmtJdsService.deleteDiagnosisAssmtJds(id);
        }
        return success(true);
    }

    @GetMapping("/test")
    @ApiOperation(value = "测试")
    public CommonResult<Boolean> test() {
        diagnosisAssmtJdsJob.handleDiagnosisAssmtJds();
        return success(true);
    }


    @GetMapping("/monthTest")
    @ApiOperation(value = "月度考核测试")
    public CommonResult<Boolean> monthAssmtJds() {
        diagnosisAssmtJdsJob.monthAssmtJds();
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-诊断评估(戒毒所)")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<DiagnosisAssmtJdsRespVO> getDiagnosisAssmtJds(@RequestParam("id") String id) {
        return success(diagnosisAssmtJdsService.getDiagnosisAssmtJds(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-诊断评估(戒毒所)分页")
    public CommonResult<PageResult<DiagnosisAssmtJdsRespVO>> getDiagnosisAssmtJdsPage(@Valid @RequestBody DiagnosisAssmtJdsPageReqVO pageReqVO) {
        PageResult<DiagnosisAssmtJdsDO> pageResult = diagnosisAssmtJdsService.getDiagnosisAssmtJdsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DiagnosisAssmtJdsRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-诊断评估(戒毒所)列表")
    public CommonResult<List<DiagnosisAssmtJdsRespVO>> getDiagnosisAssmtJdsList(@Valid @RequestBody DiagnosisAssmtJdsListReqVO listReqVO) {
        return success(diagnosisAssmtJdsService.getDiagnosisAssmtJdsList(listReqVO));
    }


    @GetMapping("/getModelByAssmtType")
    @ApiOperation(value = "获得诊断评估表模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assmtType", value = "模板类型 ZD_PGZD_PGBLX", required = true),
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码")
    })
    public CommonResult<Map<String, List<AssmtCommonBO>>> getDiagnosisAssmtJds(@RequestParam(value = "assmtType") String assmtType,
                                                                               @RequestParam(value = "jgrybm", required = false) String jgrybm) {
        return success(diagnosisAssmtJdsService.getModelByAssmtType(assmtType, jgrybm));
    }

    @GetMapping("/getMaxAssmtPeriod")
    @ApiOperation(value = "获取评估周期")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assmtType", value = "模板类型 ZD_PGZD_PGBLX", required = true),
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true)
    })
    public CommonResult<Map<String, String>> getAssmtPeriod(@RequestParam(value = "assmtType") String assmtType,
                                                      @RequestParam(value = "jgrybm") String jgrybm) {
        return success(diagnosisAssmtJdsService.getMaxAssmtPeriod(assmtType, jgrybm));
    }

    @PostMapping("/assmtCreat")
    @ApiOperation(value = "诊断评估提交")
    public CommonResult<String> assmtCreat(@Valid @RequestBody DiagnosisAssmtJdsCommitReqVO createReqVO) {
        if (StringUtils.isEmpty(createReqVO.getId())) {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(createReqVO));
            String[] params = {"jgrybm", "jgryxm", "roomId", "roomName", "assmtType", "assmtPeriod"};
            for (String param : params) {
                Assert.notNull(jsonObject.get(param), param + "值不能为空");
            }
        }
        Assert.notEmpty(createReqVO.getPgnr(), "评估内容不能为空");
        Assert.notEmpty(createReqVO.getPgjg(), "评估结果不能为空");
        return success(diagnosisAssmtJdsService.assmtCreat(createReqVO));
    }


    @PostMapping("/approval")
    @ApiOperation(value = "诊断评估审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody DiagnosisAssmtJdsApprovalReqVO approvalReqVO) {
        Assert.isTrue("0,1".contains(approvalReqVO.getApprovelResult()), "非法审批结果");
        diagnosisAssmtJdsService.approval(approvalReqVO);
        return success(true);
    }


    @GetMapping("/personRecordList")
    @ApiOperation(value = "个人诊断评估记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assmtType", value = "模板类型 ZD_PGZD_PGBLX"),
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true)
    })
    public CommonResult<List<DiagnosisAssmtJdsRespVO>> personRecordList(@RequestParam("jgrybm") String jgrybm,
                                                                        @RequestParam(value = "assmtType", required = false) String assmtType) {
        return success(diagnosisAssmtJdsService.personRecordList(jgrybm, assmtType));
    }


    @GetMapping("/getFormIdBusinessId")
    @ApiOperation(value = "获取评估手册所需字段")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true)
    })
    public CommonResult<List<DiagnosisAssmtJdsBusinessIdRespVO>> getFormIdBusinessId(@RequestParam("jgrybm") String jgrybm) {
        return success(diagnosisAssmtJdsService.getFormIdBusinessId(jgrybm));
    }


}
