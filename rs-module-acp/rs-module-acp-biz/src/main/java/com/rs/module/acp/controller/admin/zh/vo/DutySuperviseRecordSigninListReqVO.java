package com.rs.module.acp.controller.admin.zh.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 综合管理-值班管理-值班督导记录签至列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutySuperviseRecordSigninListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("值班记录ID")
    private String dutySuperviseRecordId;

    @ApiModelProperty("民警编号")
    private String policeId;

    @ApiModelProperty("民警姓名")
    private String policeName;

}
