package com.rs.module.acp.controller.admin.pi;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingListReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingPageReqVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingRespVO;
import com.rs.module.acp.controller.admin.pi.vo.prisonevent.PrisonEventSettingSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.pi.PrisonEventSettingDO;
import com.rs.module.acp.service.pi.PrisonEventSettingService;

@Api(tags = "实战平台-巡视管控-所警情管理-更新报警联动设置")
@RestController
@RequestMapping("/acp/pi/prisonEventSetting")
@Validated
public class PrisonEventSettingController {

    @Resource
    private PrisonEventSettingService prisonEventSettingService;


    @PostMapping("/update")
    @ApiOperation(value = "更新报警联动设置")
    @LogRecordAnnotation(bizModule = "acp:prisonEventSetting:update", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-更新报警联动设置",
            success = "实战平台-巡视管控-更新报警联动设置成功", fail = "实战平台-巡视管控-更新报警联动设置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updatePrisonEventSetting(@Valid @RequestBody PrisonEventSettingSaveReqVO updateReqVO) {
        prisonEventSettingService.updatePrisonEventSetting(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得报警联动设置详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonEventSetting:get", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获得报警联动设置详情",
            success = "实战平台-巡视管控-获得报警联动设置详情成功",
            fail = "实战平台-巡视管控-获得报警联动设置详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<PrisonEventSettingRespVO> getPrisonEventSetting(@RequestParam("id") String id) {
        PrisonEventSettingDO prisonEventSetting = prisonEventSettingService.getPrisonEventSetting(id);
        return success(BeanUtils.toBean(prisonEventSetting, PrisonEventSettingRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得报警联动设置分页")
    @LogRecordAnnotation(bizModule = "acp:prisonEventSetting:page", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获得报警联动设置分页",
            success = "实战平台-巡视管控-获得报警联动设置分页成功",
            fail = "实战平台-巡视管控-获得报警联动设置分页失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<PrisonEventSettingRespVO>> getPrisonEventSettingPage(@Valid @RequestBody PrisonEventSettingPageReqVO pageReqVO) {
        PageResult<PrisonEventSettingDO> pageResult = prisonEventSettingService.getPrisonEventSettingPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PrisonEventSettingRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得报警联动设置列表")
    @LogRecordAnnotation(bizModule = "acp:prisonEventSetting:list", operateType = LogOperateType.QUERY, title = "实战平台-巡视管控-获得报警联动设置列表",
            success = "实战平台-巡视管控-获得报警联动设置列表成功",
            fail = "实战平台-巡视管控-获得报警联动设置列表失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<PrisonEventSettingRespVO>> getPrisonEventSettingList(@Valid @RequestBody PrisonEventSettingListReqVO listReqVO) {
        List<PrisonEventSettingDO> list = prisonEventSettingService.getPrisonEventSettingList(listReqVO);
        return success(BeanUtils.toBean(list, PrisonEventSettingRespVO.class));
    }

    @GetMapping("/changeStatus")
    @ApiOperation(value = "启用或禁用报警联动设置")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:prisonEventSetting:changeStatus", operateType = LogOperateType.UPDATE, title = "实战平台-巡视管控-启用或禁用报警联动设置",
            success = "实战平台-巡视管控-启用或禁用报警联动设置成功", fail = "实战平台-巡视管控-启用或禁用报警联动设置失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> changeStatus(@RequestParam("id")String id) {
        return success(prisonEventSettingService.changeStatus(id));
    }
}
