package com.rs.module.acp.controller.admin.ryxx;

import cn.hutool.core.util.StrUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.base.controller.admin.pm.vo.PrisonerListVwRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台 - 在所人员")
@RestController
@RequestMapping("/base/pm/prisoner")
@Validated
public class PrisonerController {

    @Resource
    private PrisonerService prisonerService;

    @PostMapping("/getPrisonerSelectCompoment")
    @ApiOperation(value = "人员选择组件查询接口")
    public CommonResult<PageResult<PrisonerListVwRespVO>> getPrisonerSelectCompomentList(@Valid @RequestBody PrisonerVwPageReqVO pageReqVO) {
        if (StringUtils.isEmpty(pageReqVO.getOrgCode())) {
            pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        PageResult<PrisonerListVwRespVO> pageResult = prisonerService.getPrisonerSelectCompomentList(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/civilizedPersonal/getPrisonerSelectCompoment")
    @ApiOperation(value = "文明个人-人员选择组件查询接口，带本月奖励、惩罚、违规数")
    public CommonResult<PageResult<PrisonerListVwRespVO>> getCivilizedPersonalPrisonerSelectCompomentList(@Valid @RequestBody PrisonerVwPageReqVO pageReqVO) {
        PageResult<PrisonerListVwRespVO> pageResult = prisonerService.getCivilizedPersonalPrisonerSelectCompomentList(pageReqVO);
        return success(pageResult);
    }


    @GetMapping("/getPrisonerSelectCompomenOne")
    @ApiOperation(value = "人员选择组件详情查询接口，通过监管人员编号查询")
    public CommonResult<PrisonerVwRespVO> getPrisonerSelectCompomenOne(
            @NotBlank(message = "监管人员编号不能为空") @RequestParam(value = "jgrybm", required = true) String jgrybm,
            @RequestParam("ryzt") PrisonerQueryRyztEnum ryzt) {
        PrisonerVwRespVO prisonerInVwRespVO = prisonerService.getPrisonerSelectCompomenOne(jgrybm, ryzt);
        return success(prisonerInVwRespVO);
    }

    @GetMapping("/getPrisonerIn")
    @ApiOperation(value = "查询在所人员信息")
    public CommonResult<PrisonerVwRespVO> getPrisonerIn(
            @NotBlank(message = "监管人员编号不能为空") @RequestParam(value = "jgrybm") String jgrybm) {
        PrisonerVwRespVO prisonerInVwRespVO = prisonerService.getPrisonerSelectCompomenOne(jgrybm, PrisonerQueryRyztEnum.ZS);
        return success(prisonerInVwRespVO);
    }

    @GetMapping("/getPrisonerListByJgrybm")
    @ApiOperation(value = "简单人员信息查询接口，通过监管人员编号查询,多个用逗号隔开")
    public CommonResult<List<PrisonerVwRespVO>> getPrisonerListByJgrybm(@RequestParam(value = "jgrybm", required = false) String jgrybm,
                                                                        @RequestParam("ryzt") PrisonerQueryRyztEnum ryzt) {
        if (StrUtil.isBlank(jgrybm)) {
            return success(new ArrayList<>(0));
        }
        List<PrisonerVwRespVO> prisonerInVwRespVOList = prisonerService.getPrisonerListByJgrybm(jgrybm, ryzt);
        return success(prisonerInVwRespVOList);
    }




}
