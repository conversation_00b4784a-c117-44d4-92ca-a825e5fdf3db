package com.rs.module.acp.controller.admin.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.CusAppCatRespVO;
import com.rs.module.base.controller.admin.pm.vo.CusAppCatSaveReqVO;
import com.rs.module.base.entity.pm.CusAppCatDO;
import com.rs.module.base.service.pm.CusAppCatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-自定义应用分类")
@RestController
@RequestMapping("/acp/pm/cusAppCat")
@Validated
public class CusAppCatController {

    @Resource
    private CusAppCatService cusAppCatService;

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "创建或者更新自定义应用分类")
    public CommonResult<String> createCusAppCat(@Valid @RequestBody CusAppCatSaveReqVO createReqVO) {
        return success(cusAppCatService.createOrUpdateCusAppCat(createReqVO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除自定义应用分类")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCusAppCat(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           cusAppCatService.deleteCusAppCat(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得自定义应用分类")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CusAppCatRespVO> getCusAppCat(@RequestParam("id") String id) {
        CusAppCatDO cusAppCat = cusAppCatService.getCusAppCat(id);
        return success(BeanUtils.toBean(cusAppCat, CusAppCatRespVO.class));
    }

    @GetMapping("/getAll")
    @ApiOperation(value = "获得所有自定义应用分类")
    public CommonResult<List<CusAppCatRespVO>> getAll() {
        LambdaQueryWrapper<CusAppCatDO> lambdaQuery = Wrappers.lambdaQuery();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        lambdaQuery.eq(CusAppCatDO::getOrgCode, sessionUser.getOrgCode());
        lambdaQuery.orderByAsc(CusAppCatDO::getOrderId);
        List<CusAppCatDO> cusAppCats = cusAppCatService.list(lambdaQuery);
        return success(BeanUtils.toBean(cusAppCats, CusAppCatRespVO.class));
    }

}
