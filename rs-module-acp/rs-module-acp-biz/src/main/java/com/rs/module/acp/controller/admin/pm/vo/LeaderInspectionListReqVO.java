package com.rs.module.acp.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 领导巡视列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LeaderInspectionListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("巡视人")
    private String xsr;

    @ApiModelProperty("巡视类型")
    private String xslx;

    @ApiModelProperty("巡视地点类型：按区域：1；按类型：2")
    private String xsddlx;

    @ApiModelProperty("巡视地点编码，逗号分割")
    private String xsddCode;

    @ApiModelProperty("巡视地点名称，逗号分割")
    private String xsddName;

    @ApiModelProperty("巡视内容")
    private String xsnr;

    @ApiModelProperty("巡视开始时间")
    private Date xskssj;

    @ApiModelProperty("巡视结束时间")
    private String xsjssj;

    @ApiModelProperty("巡视是否发现问题")
    private String xssffxwt;

    @ApiModelProperty("问题通知人ID")
    private String wttzr;

    @ApiModelProperty("问题通知人名称")
    private String wttzrName;

    @ApiModelProperty("巡视结果信息")
    private String xsjgxx;

    @ApiModelProperty("问题处置人ID")
    private String wtczr;

    @ApiModelProperty("问题处置人名称")
    private String wtczrName;

    @ApiModelProperty("处置时间")
    private Date czsj;

    @ApiModelProperty("处置情况")
    private String czqk;

    @ApiModelProperty("状态：1：无问题；2：待处置；3：已处置")
    private String status;

    @ApiModelProperty("领导职务")
    private String ldzw;

}
