package com.rs.module.acp.controller.admin.sys.vo;
import com.rs.framework.mybatis.entity.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-语音播报-定时配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VbConfigTimedSaveReqVO extends BaseVO{
	
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("播报名称")
    private String vbName;

    @ApiModelProperty("查询脚本")
    private String queryScript;

    @ApiModelProperty("播报内容")
    private String content;

    @ApiModelProperty("播报次数")
    private Short vbNum;

    @ApiModelProperty("优先级")
    private Short priority;

    @ApiModelProperty("是否启用(0否1是)")
    private Short isEnabled;

}
