package com.rs.module.acp.controller.admin.sldxxfb;

import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "实战平台-所领导信息发布-监所概况")
@RestController
@RequestMapping("/acp/sldxxfb/prisonprofile")
@Validated
public class PrisonProfileController {
    //全所羁押数,民警/辅警数,警押比(民警人数/羁押人数),外籍人员

    //警力装备 按照岗位进行统计用户数量

    //业务装备 仓内屏 0008,仓外屏 0015,监控设备,移动执法设备 (执法记录仪 0011),物品存储设备,集成设备 ZD_SBLXDM

}
