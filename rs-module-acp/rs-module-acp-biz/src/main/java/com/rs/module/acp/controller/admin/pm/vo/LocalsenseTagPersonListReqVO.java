package com.rs.module.acp.controller.admin.pm.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-定位标签与人员绑定列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LocalsenseTagPersonListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("绑定来源")
    private String bindSource;

    @ApiModelProperty("标签id")
    private String tagId;

    @ApiModelProperty("人员标签类型，01：被监管人员，02：民警")
    private String personType;

    @ApiModelProperty("绑定人员ID")
    private String bindPersonId;

    @ApiModelProperty("bind_person_name")
    private String bindPersonName;

    @ApiModelProperty("绑定时间")
    private Date[] bindTime;

    @ApiModelProperty("解绑时间")
    private Date[] unbindTime;

    @ApiModelProperty("解绑原因")
    private String unbindReason;

    @ApiModelProperty("状态")
    private String status;

}
