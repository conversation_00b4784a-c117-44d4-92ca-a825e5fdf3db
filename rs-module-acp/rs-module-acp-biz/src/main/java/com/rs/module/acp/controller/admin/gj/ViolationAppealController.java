package com.rs.module.acp.controller.admin.gj;

import cn.hutool.core.lang.Assert;
import com.rs.module.acp.controller.admin.gj.vo.violationappeal.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.ViolationAppealDO;
import com.rs.module.acp.service.gj.violationappeal.ViolationAppealService;

@Api(tags = "实战平台-管教业务-违规申诉")
@RestController
@RequestMapping("/acp/gj/violationAppeal")
@Validated
public class ViolationAppealController {

    @Resource
    private ViolationAppealService violationAppealService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-违规申诉")
    public CommonResult<String> createViolationAppeal(@Valid @RequestBody ViolationAppealSaveReqVO createReqVO) {
        return success(violationAppealService.createViolationAppeal(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-违规申诉")
    public CommonResult<Boolean> updateViolationAppeal(@Valid @RequestBody ViolationAppealSaveReqVO updateReqVO) {
        violationAppealService.updateViolationAppeal(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-违规申诉")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteViolationAppeal(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           violationAppealService.deleteViolationAppeal(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<ViolationAppealRespVO> getViolationAppeal(@RequestParam("id") String id) {
        ViolationAppealDO violationAppeal = violationAppealService.getViolationAppeal(id);
        return success(BeanUtils.toBean(violationAppeal, ViolationAppealRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-违规申诉分页")
    public CommonResult<PageResult<ViolationAppealRespVO>> getViolationAppealPage(@Valid @RequestBody ViolationAppealPageReqVO pageReqVO) {
        PageResult<ViolationAppealDO> pageResult = violationAppealService.getViolationAppealPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ViolationAppealRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-违规申诉列表")
    public CommonResult<List<ViolationAppealRespVO>> getViolationAppealList(@Valid @RequestBody ViolationAppealListReqVO listReqVO) {
        List<ViolationAppealDO> list = violationAppealService.getViolationAppealList(listReqVO);
        return success(BeanUtils.toBean(list, ViolationAppealRespVO.class));
    }

    @GetMapping("/getListByIds")
    @ApiOperation(value = "批量审批列表")
    @ApiImplicitParam(name = "ids", value = "多个编号，英文逗号分割")
    public CommonResult<List<ViolationAppealRespVO>> getListByIds(@RequestParam("ids") String ids) {
        List<String> idList = new ArrayList<>(Arrays.asList(ids.split(",")));
        Assert.notEmpty(idList, "编号不能为空");
        List<ViolationAppealDO> list = violationAppealService.getListByIds(idList);
        return success(BeanUtils.toBean(list, ViolationAppealRespVO.class));
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody ViolationAppealApprovalReqVO appealApprovalReqVO) {
        violationAppealService.approval(appealApprovalReqVO);
        return success(true);
    }
}
