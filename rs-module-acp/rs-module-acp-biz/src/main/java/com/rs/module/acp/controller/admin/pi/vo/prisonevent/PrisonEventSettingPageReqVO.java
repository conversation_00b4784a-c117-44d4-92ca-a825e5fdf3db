package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所警情管理-报警联动设置(所情来源)分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrisonEventSettingPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("所请来源 字典：C_JQLY")
    private String eventSrc;

    @ApiModelProperty("所情来源-中文")
    private String name;

    @ApiModelProperty("所情等级 字典：C_JQDJ")
    private Short eventLevel;

    @ApiModelProperty("是否启用 1启用 0禁用")
    private Short enabled;

    @ApiModelProperty("处理时效（分钟）")
    private Short processingDuration;

    @ApiModelProperty("提示音 字典：PRISON_WARNING_TONE")
    private String tone;

    @ApiModelProperty("可选择的设置。逗号分隔	字典：PRISON_EVENT_SETTING_ITEM")
    private String optionalSettings;

    @ApiModelProperty("联动配置。逗号分隔。optional_settings")
    private String settings;

    @ApiModelProperty("扩展字段1")
    private String extendOne;

    @ApiModelProperty("所情事件类型ID")
    private String typeId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
