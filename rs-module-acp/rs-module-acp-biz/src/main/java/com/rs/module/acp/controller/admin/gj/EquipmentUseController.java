package com.rs.module.acp.controller.admin.gj;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApprovalTraceVO;
import com.rs.module.acp.controller.admin.gj.vo.common.GjApproveReqVO;
import com.rs.module.acp.controller.admin.gj.vo.equipment.*;
import com.rs.module.acp.service.gj.equipment.EquipmentUseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-械具使用")
@RestController
@RequestMapping("/acp/gj/equipmentUse")
@Validated
public class EquipmentUseController {

    @Resource
    private EquipmentUseService equipmentUseService;

    @PostMapping("/create")
    @ApiOperation(value = "管教业务-械具使用-使用呈批")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:create", operateType = LogOperateType.CREATE, title = "管教业务-戒具使用-使用呈批",
            success = "管教业务-戒具使用-使用呈批登记成功", fail = "管教业务-戒具使用-使用呈批登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createStartFlow(@Valid @RequestBody EquipmentUseStartFlowReqVO createReqVO) {
        return success(equipmentUseService.createStartFlow(createReqVO));
    }

    @PostMapping("/leaderApproveApply")
    @ApiOperation(value = "领导审批-管教业务-使用呈批审批")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:leaderApproveApply", operateType = LogOperateType.UPDATE, title = "管教业务-戒具使用-领导审批",
            success = "管教业务-戒具使用-领导审批成功", fail = "管教业务-戒具使用-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApproveApply(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        equipmentUseService.leaderApproveApply(approveReqVO);
        return success(true);
    }


    @PostMapping("/regInfo")
    @ApiOperation(value = "管教业务-械具使用-信息登记")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:regInfo", operateType = LogOperateType.UPDATE, title = "管教业务-戒具使用-信息登记",
            success = "管教业务-戒具使用-信息登记", fail = "管教业务-戒具使用-信息登记-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> regInfo(@Valid @RequestBody EquipmentUseSaveRegInfoVO createReqVO) {
        return success(equipmentUseService.regInfo(createReqVO));
    }


    @PostMapping("/extendApprovalApply")
    @ApiOperation(value = "管教业务-械具使用-延长审批申请")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:extendApprovalApply", operateType = LogOperateType.CREATE, title = "管教业务-戒具使用-延长审批申请",
            success = "管教业务-戒具使用-延长审批申请", fail = "管教业务-延长审批使用-信息登记-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> extendApproval(@Valid @RequestBody EquipmentExtendApprovalApplyReqVO createReqVO) {
        return success(equipmentUseService.extendApproval(createReqVO));
    }

    /**
     * 延长所领导审批
     * <AUTHOR>
     * @date 2025/6/5 14:36
     * @param [approveReqVO]
     * @return com.rs.framework.common.pojo.CommonResult<java.lang.Boolean>
     */
    @PostMapping("/leaderApproveExtendApply")
    @ApiOperation(value = "领导审批-管教业务-延长所领导审批")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:leaderApproveExtendApply", operateType = LogOperateType.UPDATE, title = "管教业务-戒具使用-领导审批",
            success = "管教业务-戒具使用-领导审批成功", fail = "管教业务-戒具使用-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApproveExtendApply(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        equipmentUseService.leaderApproveExtendApply(approveReqVO);
        return success(true);
    }


    @PostMapping("/removeApprovalApply")
    @ApiOperation(value = "管教业务-械具使用-解除申请")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:removeApprovalApply", operateType = LogOperateType.CREATE, title = "管教业务-戒具使用-提前解除申请",
            success = "管教业务-戒具使用-提前解除申请，提交成功", fail = "管教业务-戒具使用-提前解除申请-失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> removeApprovalApply(@Valid @RequestBody EquipmentUseRemoveApplyReqVO createReqVO) {
        Boolean flag = equipmentUseService.removeApprovalApply(createReqVO);
        return success(flag);
    }



    @PostMapping("/leaderApproveRemoveApply")
    @ApiOperation(value = "领导审批-管教业务-提前解除所领导审批")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:leaderApproveExtendApply", operateType = LogOperateType.UPDATE, title = "管教业务-戒具使用-提交解除领导审批",
            success = "管教业务-戒具使用-提前解除-领导审批成功", fail = "管教业务-戒具使用-提前解除-领导审审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#approveReqVO}}")
    public CommonResult<Boolean> leaderApproveRemoveApply(@Valid @RequestBody GjApproveReqVO approveReqVO) {
        equipmentUseService.leaderApproveRemoveApply(approveReqVO);
        return success(true);
    }

    @PostMapping("/removeRegInfo")
    @ApiOperation(value = "管教业务-戒具使用-解除登记")
    @LogRecordAnnotation(bizModule = "acp:equipmentuse:removeRegInfo", operateType = LogOperateType.UPDATE, title = "管教业务-戒具使用-解除登记",
            success = "管教业务-戒具使用-解除登记", fail = "管教业务-戒具使用-解除登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> removeRegInfo(@Valid @RequestBody EquipmentUseRemoveApplyRegInfoVO createReqVO) {
        equipmentUseService.removeRegInfo(createReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-械具使用")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EquipmentUseRespVO> getEquipmentUse(@RequestParam("id") String id) {
        EquipmentUseRespVO equipmentUse = equipmentUseService.getEquipmentUse(id);
        return success(equipmentUse);
    }


    @GetMapping("/getEquipmentUseByJgrybmList")
    @ApiOperation(value = "获得实战平台-管教业务-戒具使用记录")
    public CommonResult<List<EquipmentUseRespVO>> getEquipmentUseListByJgrybm(@RequestParam("jgrybm")String jgrybm) {
        List<EquipmentUseRespVO> list = equipmentUseService.getEquipmentUseListByJgrybm(jgrybm);
        return success(list);
    }

    @GetMapping("/getApproveTrack")
    @ApiOperation(value = "管教业务-戒具使用-获取轨迹记录")
    public CommonResult<List<GjApprovalTraceVO>> getApproveTrack(@RequestParam("id")String id) {
        List<GjApprovalTraceVO> list = equipmentUseService.getApproveTrack(id);
        return success(list);
    }

}
