package com.rs.module.acp.controller.admin.pm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemeListReqVO;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemePageReqVO;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemeRespVO;
import com.rs.module.acp.controller.admin.pm.vo.SystemThemeSaveReqVO;
import com.rs.module.acp.entity.pm.SystemThemeDO;
import com.rs.module.acp.service.pm.SystemThemeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-主题-主题配置")
@RestController
@RequestMapping("/acp/pm/systemTheme")
@Validated
public class SystemThemeController {

    @Resource
    private SystemThemeService systemThemeService;


    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-主题-主题配置")
    public CommonResult<Boolean> updateSystemTheme(@Valid @RequestBody SystemThemeSaveReqVO updateReqVO) {
        SystemThemeDO themeDO = systemThemeService.getOne(new LambdaQueryWrapper<SystemThemeDO>()
                .eq(SystemThemeDO::getAddUser, SessionUserUtil.getSessionUser().getIdCard()));
        if (themeDO != null) {
            updateReqVO.setId(themeDO.getId());
            systemThemeService.updateSystemTheme(updateReqVO);
        } else {
            systemThemeService.createSystemTheme(updateReqVO);
        }
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-主题-主题配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSystemTheme(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            systemThemeService.deleteSystemTheme(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-主题-当前用户主题配置")
    public CommonResult<SystemThemeRespVO> getSystemTheme() {
        SystemThemeDO themeDO = systemThemeService.getOne(new LambdaQueryWrapper<SystemThemeDO>()
                .eq(SystemThemeDO::getAddUser, SessionUserUtil.getSessionUser().getIdCard()));
        return success(BeanUtils.toBean(themeDO, SystemThemeRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-主题-主题配置分页")
    public CommonResult<PageResult<SystemThemeRespVO>> getSystemThemePage(@Valid @RequestBody SystemThemePageReqVO pageReqVO) {
        PageResult<SystemThemeDO> pageResult = systemThemeService.getSystemThemePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SystemThemeRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-主题-主题配置列表")
    public CommonResult<List<SystemThemeRespVO>> getSystemThemeList(@Valid @RequestBody SystemThemeListReqVO listReqVO) {
        List<SystemThemeDO> list = systemThemeService.getSystemThemeList(listReqVO);
        return success(BeanUtils.toBean(list, SystemThemeRespVO.class));
    }
}
