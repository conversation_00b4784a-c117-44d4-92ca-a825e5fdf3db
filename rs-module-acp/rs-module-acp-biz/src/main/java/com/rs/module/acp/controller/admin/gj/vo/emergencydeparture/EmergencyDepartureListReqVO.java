package com.rs.module.acp.controller.admin.gj.vo.emergencydeparture;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-紧急出所登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EmergencyDepartureListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("就诊医院")
    private String hospital;

    @ApiModelProperty("病情说明")
    private String symptomDesc;

    @ApiModelProperty("经办民警身份证号")
    private String operatePoliceSfzh;

    @ApiModelProperty("经办民警")
    private String operatePolice;

    @ApiModelProperty("经办时间")
    private Date[] operateTime;

}
