package com.rs.module.acp.controller.admin.gj.vo.punishment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-管教业务-处罚呈批 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PunishmentRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("处罚原因 看守所字典：ZD_GJCFYY   拘留所字典：ZD_GJYGYY")
    private String reason;

    @ApiModelProperty("处罚原因 看守所字典：ZD_GJCFYY   拘留所字典：ZD_GJYGYY")
    private String reasonName;

    @ApiModelProperty("呈批处罚周期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty("呈批处罚周期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty("实际开始日期")
    private Date actualStartDate;

    @ApiModelProperty("实际结束日期")
    private Date actualEndDate;

    @ApiModelProperty("处罚时长，自动生成（天-时-分钟）")
    private Integer duration;

    @ApiModelProperty("实际处罚时长 自动生成（天-时-分钟 ")
    private BigDecimal actualDuration;

    @Trans(type = TransType.DICTIONARY, key = "ZD_GJCFNR")
    @ApiModelProperty("处罚措施逗号分割  字典：ZD_GJCFNR")
    private String measures;

    @ApiModelProperty("具体原因")
    private String specificReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("执行人身份证号")
    private String executorSfzh;

    @ApiModelProperty("执行人身份证号")
    private String executor;

    @ApiModelProperty("执行登记时间")
    private Date executorRegTime;

    @ApiModelProperty("执行情况")
    private String executeSituation;

    @ApiModelProperty("是否提前解除")
    private Short isInAdvanceRemove;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("是否严管 1是0否")
    private Integer sfyg;

    @ApiModelProperty("剩余天数")
    private Long remainderDay;

    //强制措施
    //@ApiModelProperty("措施名称")
    //private String measuresName;

    @ApiModelProperty("申请人")
    private String addUserName;

    @ApiModelProperty("申请时间")
    private Date addTime;

    @ApiModelProperty("延长记录信息")
    private List<PunishmentExtendRespVO> extendRespList;

    @ApiModelProperty("提前解除信息")
    private PunishmentRemoveRespVO removeRespVO;

    @ApiModelProperty("解除登记Info")
    private PunishmentRemoveRespVO removeRegRespVO;

}
