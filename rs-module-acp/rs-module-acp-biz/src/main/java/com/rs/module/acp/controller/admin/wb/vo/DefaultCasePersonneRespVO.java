package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-办案人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DefaultCasePersonneRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("警号")
    private String jh;
    @ApiModelProperty("姓名")
    private String xm;
    @ApiModelProperty("证件类型")
    private String zjlx;
    @ApiModelProperty("证件号码")
    private String zjhm;
    @ApiModelProperty("办案单位代码")
    private String badwdm;
    @ApiModelProperty("办案单位名称")
    private String badwmc;
    @ApiModelProperty("联系方式")
    private String lxfs;
    @ApiModelProperty("性别")
    private String xb;
    @ApiModelProperty("照片存储url")
    private String zpUrl;
    @ApiModelProperty("工作证件url")
    private String gzzjUrl;
    @ApiModelProperty("提解机关类型")
    private String tjjglx;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
}
