package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-领事外事信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConsularPageReqVO extends PageParam  {
private static final long serialVersionUID = 1L;


    @ApiModelProperty("业务主键")
    private String id;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("证件类型")
    private String idType;

    @ApiModelProperty("证件号码")
    private String idNumber;

    @ApiModelProperty("国籍")
    private String nationality;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("工作单位")
    private String workUnit;

    @ApiModelProperty("照片")
    private String imageUrl;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;

    @ApiModelProperty("机构单位")
    private String orgCode;
}
