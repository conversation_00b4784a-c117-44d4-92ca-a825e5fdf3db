package com.rs.module.acp.controller.admin.sldxxfb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 实战平台-巡视管控-违规登记 DO
 *
 * <AUTHOR>
 */
@Data
public class WgdjVO  extends BaseVO implements TransPojo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("巡控室ID")
    private String patrolRoomId;
    @ApiModelProperty("巡控室名称")
    private String patrolRoomName;
    @ApiModelProperty("监室号")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("违规内容")
    private String violationContent;
    @ApiModelProperty("违规内容html")
    private String violationContentHtml;
    @ApiModelProperty("处置情况")
    private String disposalSituation;
    @ApiModelProperty("是否岗位协同：1-是/0-否")
    private Short isPostCoordination;
    @ApiModelProperty("岗位协同人员，多选项")
    private String coordinationPosts;
    @ApiModelProperty("岗位协同人员名称")
    private String coordinationPostsName;
    @ApiModelProperty("上传附件路径，存储附件文件路径")
    private String attachment;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XSGKYWLX")
    private String status;
    @ApiModelProperty("登记经办人")
    private String operatorSfzh;
    @ApiModelProperty("登记经办人姓名")
    private String operatorXm;
    @ApiModelProperty("登记经办时间")
    private Date operatorTime;
    @ApiModelProperty("岗位协同推送对象证件号码")
    private String pushTargetIdCard;
    @ApiModelProperty("岗位协同推送对象")
    private String pushTarget;
    @ApiModelProperty("岗位协同推送内容")
    private String pushContent;
}
