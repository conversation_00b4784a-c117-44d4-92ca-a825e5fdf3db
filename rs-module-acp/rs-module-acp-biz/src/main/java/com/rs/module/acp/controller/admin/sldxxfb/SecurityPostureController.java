package com.rs.module.acp.controller.admin.sldxxfb;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.sldxxfb.vo.WgdjVO;
import com.rs.module.acp.service.sldxxfb.SldxxfbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-所领导信息发布-安全态势")
@RestController
@RequestMapping("/acp/sldxxfb/securityposture")
public class SecurityPostureController {
    @Autowired
    private SldxxfbService sldxxfbService;
    @GetMapping("/fxryfb")
    @ApiOperation(value = "所领导信息发布-安全态势-风险人员分布")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "orgCode", value = "机构编号")
    })
    public CommonResult<JSONObject> fxryfb(@RequestParam(required = false) String orgCode) {

        return CommonResult.success(sldxxfbService.fxryfb(orgCode));
    }
    @GetMapping("/fxryqs")
    @ApiOperation(value = "所领导信息发布-安全态势-风险人员趋势")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "timeRange", value = "时间段类型:3 近一周, 4 近一月"),
    })
    public CommonResult<List<JSONObject>> fxryqs(@RequestParam(required = false) String orgCode,
                                                 @RequestParam(required = false)String timeRange) {

        return CommonResult.success(sldxxfbService.fxryqs(orgCode, timeRange));
    }
    @GetMapping("/wgqs")
    @ApiOperation(value = "所领导信息发布-安全态势-违规趋势")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "timeRange", value = "时间段类型:3 近一周, 4 近一月"),
    })
    public CommonResult<List<JSONObject>> wgqs(@RequestParam(required = false) String orgCode,@RequestParam(required = false)String timeRange) {

        return CommonResult.success(sldxxfbService.wgqs(orgCode, timeRange));
    }
    @GetMapping("/getWggjPage")
    @ApiOperation(value = "违规告警列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "timeRange", value = "周期类型 1本日 2本周 3本月 4本年 ", required = true),
            @ApiImplicitParam(name = "handleStatus", value = "处理类型 1全部 2已处理 3未处理 ", required = true),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public CommonResult<PageResult<WgdjVO>> getWggjPage(@RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                            @RequestParam(name = "timeRange", defaultValue = "2") String timeRange,
                                                            @RequestParam(name = "handleStatus", defaultValue = "1") String handleStatus,
                                                            @RequestParam("code") String code,
                                                            @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        PageResult<WgdjVO> pageResult = sldxxfbService.getWggjPage(pageNo, pageSize, timeRange, handleStatus, code, codeType);
        return success(pageResult);
    }

    @GetMapping("/getWggjAllCount")
    @ApiOperation(value = "违规告警数据统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "codeType", value = "编码类型", required = true),
            @ApiImplicitParam(name = "code", value = "编码", required = true),
            @ApiImplicitParam(name = "timeRange", value = "周期类型 1本日 2本周 3本月 4本年 ", required = true)
    })
    public CommonResult<JSONObject> getWggjAllCount(
            @RequestParam(name = "timeRange", defaultValue = "2") String timeRange,
            @RequestParam("code") String code,
            @RequestParam(value = "codeType", defaultValue = "01") String codeType) {
        Assert.notBlank(code, "code不能为空");
        return success(sldxxfbService.getWggjAllCount(timeRange, code, codeType));
    }
    @GetMapping("/gzry")
    @ApiOperation(value = "所领导信息发布-安全态势-关注人员")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "orgCode", value = "机构编号"),
            @ApiImplicitParam(name = "types", value = "时间段类型:3 近一周, 4 近一月"),
            @ApiImplicitParam(name = "status", value = "类型:总数,已处理,未处理"),
    })
    public CommonResult<JSONObject> gzry(@RequestParam(required = false) String orgCode,
                                         @RequestParam(required = false) String timeRange,
                                         @RequestParam(required = false) String status) {
        //总数，重点关注，戒具使用，重病号，单独关押，临时固定
        return CommonResult.success(sldxxfbService.gzry(orgCode));
    }

}
