package com.rs.module.acp.controller.admin.wb.vo;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师关联被监管人员新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LawyerPrisonerSaveReqVO extends BaseVO implements TransPojo {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("律师表ID")
    @NotEmpty(message = "律师表ID不能为空")
    private String lawyerId;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("委托类型")
    @NotEmpty(message = "委托类型不能为空")
    private String entrustType;

    @ApiModelProperty("委托阶段")
    @NotEmpty(message = "委托阶段不能为空")
    private String entrustStage;

    @ApiModelProperty("委托人")
    private String principal;

    @ApiModelProperty("委托人证件号")
    private String principalId;

    @ApiModelProperty("委托书类型")
    private String powerOfAttorneyType;

    @ApiModelProperty("委托书存储路径")
    private String powerOfAttorneyUrl;

    @ApiModelProperty("介绍信编号")
    private String letterNumber;

    @ApiModelProperty("会见批准机关")
    private String meetingApprovalAuthority;

    @ApiModelProperty("委托状态")
    private String status;

    @ApiModelProperty("委托状态")
    private String lslx;
}
