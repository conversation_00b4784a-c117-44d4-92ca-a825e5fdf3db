package com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-权力义务告知书分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RightsObligationsNoticePageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("签名url")
    private String signUrl;

    @ApiModelProperty("签名时间")
    private Date[] signTime;

    @ApiModelProperty("捺印url")
    private String fingerprintUrl;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
