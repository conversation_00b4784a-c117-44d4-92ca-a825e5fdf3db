package com.rs.module.acp.controller.admin.gj;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutRespVO;
import com.rs.module.acp.controller.admin.gj.vo.groupinout.GroupInOutSaveReqVO;
import com.rs.module.acp.service.gj.groupinout.GroupInOutService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-集体出入")
@RestController
@RequestMapping("/acp/gj/groupInOut")
@Validated
public class GroupInOutController {

    @Resource
    private GroupInOutService groupInOutService;

    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "集体出入-创建和更新")
    public CommonResult<String> createGroupInOut(@Valid @RequestBody GroupInOutSaveReqVO createReqVO) {
        return success(groupInOutService.createGroupInOut(createReqVO));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-集体出入")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteGroupInOut(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           groupInOutService.deleteGroupInOut(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-集体出入")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<GroupInOutRespVO> getGroupInOut(@RequestParam("id") String id) {
        GroupInOutRespVO groupInOut = groupInOutService.getGroupInOutRespVO(id);
        return success(groupInOut);
    }

}
