package com.rs.module.acp.controller.admin.gj.vo.biometriccomp;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-生物特征比对 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricCompRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("提交生物特征类型")
    private String biometricType;
    @ApiModelProperty("提交时间")
    private Date submitTime;
    @ApiModelProperty("提交办案单位类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_KSS_BADWLX")
    private String caseUnitType;
    @ApiModelProperty("提交办案单位名称")
    @Trans(type = TransType.DICTIONARY, key = "ZD_BADW_GAJG")
    private String caseUnitName;
    @ApiModelProperty("提交方式")
    private String submitMethod;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否比中")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SWTZBD_SFBZ")
    private String isComp;
    @ApiModelProperty("比中案件编号")
    private String compCaseNo;
    @ApiModelProperty("比中案件类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GABBZ_XZAJAY")
    private String compCaseType;
    @ApiModelProperty("比中处置情况")
    private String disposalSituation;
    @ApiModelProperty("比中登记人身份证号")
    private String compOperatorSfzh;
    @ApiModelProperty("比中登记人姓名")
    private String compOperatorName;
    @ApiModelProperty("comp_operator_time")
    private Date compOperatorTime;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_SWTZBD_BDZT")
    private String status;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
