package com.rs.module.acp.controller.admin.zh.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("值班记录人员信息VO")
public class StaffDutyRecordPersonVO {

    @ApiModelProperty(value = "民警ID", example = "1941108028032159744")
    private String policeId;

    @ApiModelProperty(value = "民警姓名", example = "张三")
    private String policeName;

    @ApiModelProperty(value = "民警类型", example = "1")
    private String policeType;

    @ApiModelProperty(value = "是否缺席", example = "1")
    private String isAbsent;

    @ApiModelProperty(value = "缺席原因", example = "请假")
    private String absentReason;
}

