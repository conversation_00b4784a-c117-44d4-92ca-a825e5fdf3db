package com.rs.module.acp.controller.admin.gj;

import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.module.acp.controller.admin.gj.vo.assistancesolvecase.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.AssistanceSolveCaseDO;
import com.rs.module.acp.service.gj.assistancesolvecase.AssistanceSolveCaseService;

@Api(tags = "实战平台-管教业务-协助破案")
@RestController
@RequestMapping("/acp/gj/assistanceSolveCase")
@Validated
public class AssistanceSolveCaseController {

    @Resource
    private AssistanceSolveCaseService assistanceSolveCaseService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-协助破案")
    public CommonResult<String> createAssistanceSolveCase(@Valid @RequestBody AssistanceSolveCaseSaveReqVO createReqVO) {
        return success(assistanceSolveCaseService.createAssistanceSolveCase(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-协助破案")
    public CommonResult<Boolean> updateAssistanceSolveCase(@Valid @RequestBody AssistanceSolveCaseSaveReqVO updateReqVO) {
        assistanceSolveCaseService.updateAssistanceSolveCase(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-协助破案")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteAssistanceSolveCase(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            assistanceSolveCaseService.deleteAssistanceSolveCase(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-协助破案")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AssistanceSolveCaseRespVO> getAssistanceSolveCase(@RequestParam("id") String id) {
        AssistanceSolveCaseDO assistanceSolveCase = assistanceSolveCaseService.getAssistanceSolveCase(id);
        return success(BeanUtils.toBean(assistanceSolveCase, AssistanceSolveCaseRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-协助破案分页")
    public CommonResult<PageResult<AssistanceSolveCaseRespVO>> getAssistanceSolveCasePage(@Valid @RequestBody AssistanceSolveCasePageReqVO pageReqVO) {
        PageResult<AssistanceSolveCaseDO> pageResult = assistanceSolveCaseService.getAssistanceSolveCasePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssistanceSolveCaseRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-协助破案列表")
    public CommonResult<List<AssistanceSolveCaseRespVO>> getAssistanceSolveCaseList(@Valid @RequestBody AssistanceSolveCaseListReqVO listReqVO) {
        List<AssistanceSolveCaseDO> list = assistanceSolveCaseService.getAssistanceSolveCaseList(listReqVO);
        return success(BeanUtils.toBean(list, AssistanceSolveCaseRespVO.class));
    }


    @PostMapping("/clueRegist")
    @ApiOperation(value = "线索登记")
    public CommonResult<String> clueRegist(@Valid @RequestBody AssistanceSolveCaseClueRegistReqVO createReqVO) {

        return success(assistanceSolveCaseService.clueRegist(createReqVO));
    }

    @PostMapping("/clueApproval")
    @ApiOperation(value = "线索审批")
    public CommonResult<Boolean> clueApproval(@Valid @RequestBody AssistanceSolveCaseClueApprovalReqVO approvalReqVO) {
        assistanceSolveCaseService.clueApproval(approvalReqVO);
        return success(true);
    }

    @PostMapping("/clueForward")
    @ApiOperation(value = "线索转递")
    public CommonResult<Boolean> clueForward(@Valid @RequestBody AssistanceSolveCaseForwardReqVO caseForwardReqVO) {
        assistanceSolveCaseService.clueForward(caseForwardReqVO);
        return success(true);
    }

    @PostMapping("/clueFeedback")
    @ApiOperation(value = "线索反馈")
    public CommonResult<Boolean> clueFeedback(@Valid @RequestBody AssistanceSolveCaseFeedbackReqVO feedbackReqVO) {
        assistanceSolveCaseService.clueFeedback(feedbackReqVO);
        return success(true);
    }

}
