package com.rs.module.acp.controller.admin.wb.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-单向视频家属会见新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyMeetingVideoSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

    @ApiModelProperty("通知家属时间")
    private Date notificationFamilyTime;

    @ApiModelProperty("通知会见日期")
    private Date notificationMeetingDate;

    @ApiModelProperty("会见开始时间")
    private Date meetingStartTime;

    @ApiModelProperty("会见结束时间")
    private Date meetingEndTime;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审批人签名")
    private String approvalAutograph;

    @ApiModelProperty("审批人签名日期")
    private Date approvalAutographTime;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("通知操作人身份证号")
    private String notificationOperatorSfzh;
    @ApiModelProperty("通知操作人")
    private String notificationOperator;
    @ApiModelProperty("通知操作时间")
    private Date notificationOperatorTime;

    @ApiModelProperty("登记操作人身份证号")
    private String checkOperatorSfzh;
    @ApiModelProperty("登记操作人")
    private String checkOperator;
    @ApiModelProperty("登记操作时间")
    private Date checkOperatorTime;

    @ApiModelProperty("家属列表")
    private List<SocialRelationsChildSaveReqVO> familyList;

    @ApiModelProperty("经办时间--无需传值")
    private String addTime;
    @ApiModelProperty("会见方式--无需传值")
    private String meetingMethod;

    @ApiModelProperty("数据来源（0：PC手动录入，1：智能终端）")
    private String dataSources = "0";

}
