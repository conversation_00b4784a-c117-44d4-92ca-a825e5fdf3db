package com.rs.module.acp.controller.admin.gj.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-戒具检查 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentUseCheckRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("械具使用ID")
    private String equipmentUseId;
    @ApiModelProperty("械具类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_GJ_EQUIPMENT_TYPE")
    private String punishmentToolType;
    @ApiModelProperty("检查时间")
    private Date checkTime;
    @ApiModelProperty("检查人身份证号")
    private String checkUserSfzh;
    @ApiModelProperty("检查人")
    private String checkUser;
    @ApiModelProperty("检查结果")
    @Trans(type = TransType.DICTIONARY, key = "ZD_EQUIPMENT_CHECK_RESULT")
    private String checkResult;
    @ApiModelProperty("检查情况")
    private String checkSituation;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String xm;
    @ApiModelProperty("监管人员性别")
    @Trans(type = TransType.DICTIONARY, key = "ZD_XB")
    private String xb;
    @ApiModelProperty("监管人员年龄")
    private Integer age;
    @ApiModelProperty("监管人员正面照")
    private String frontPhoto;

}
