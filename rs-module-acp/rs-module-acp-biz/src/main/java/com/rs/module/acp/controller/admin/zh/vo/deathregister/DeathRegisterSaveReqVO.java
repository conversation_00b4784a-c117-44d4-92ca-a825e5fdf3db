package com.rs.module.acp.controller.admin.zh.vo.deathregister;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "实战平台-综合管理-死亡登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeathRegisterSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty(value = "保存类型 0 保存,1 提交",hidden = true)
    private String saveType;
    @ApiModelProperty("死亡地点")
    private String deathSite;

    @ApiModelProperty("死亡时间")
    private Date deathTime;

    @ApiModelProperty("现场处理情况")
    private String siteHandlingSituation;

    @ApiModelProperty("死亡人员姓名")
    private String jgryxm;

    @ApiModelProperty("死亡人员编号")
    private String jgrybm;

    @ApiModelProperty("是否需要死亡鉴定,0否、1是")
    private Short deathAppraise;

}
