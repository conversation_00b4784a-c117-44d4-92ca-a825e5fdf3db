package com.rs.module.acp.controller.admin.zh;

import com.bsp.common.util.StringUtil;
import com.rs.module.acp.controller.admin.zh.vo.deathregister.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.service.zh.deathregister.DeathRegisterService;

@Api(tags = "实战平台-综合管理-死亡登记")
@RestController
@RequestMapping("/acp/zh/deathRegister")
@Validated
public class DeathRegisterController {
    @Resource
    private DeathRegisterService deathRegisterService;
    @PostMapping("/save")
    @ApiOperation("死亡登记保存")
    public CommonResult<String> deathRegisterSave(@RequestBody @Valid DeathRegisterSaveReqVO reqVO) {
        reqVO.setSaveType("0");
        String id = deathRegisterService.deathRegisterSave(reqVO);
        if(StringUtil.isEmpty(id)){
            return CommonResult.error("死亡登记保存出错！");
        }
        return CommonResult.success(id);
    }
    @PostMapping("/submit")
    @ApiOperation("死亡登记提交")
    public CommonResult<String> deathRegisterSubmit(@RequestBody @Valid DeathRegisterSaveReqVO reqVO) {
        reqVO.setSaveType("1");
        String id = deathRegisterService.deathRegisterSave(reqVO);
        if(StringUtil.isEmpty(id)){
            return CommonResult.error("死亡登记提交出错！");
        }
        return CommonResult.success(id);
    }
    @PostMapping("/deathAppraiseSave")
    @ApiOperation("死亡鉴定保存")
    public CommonResult<String> deathAppraiseSave(@RequestBody @Valid DeathAppraiseSaveVO reqVO) {
        reqVO.setSaveType("0");
        String id = deathRegisterService.deathAppraiseSave(reqVO);
        if(StringUtil.isEmpty(id)){
            return CommonResult.error("死亡鉴定保存出错！");
        }
        return CommonResult.success(id);
    }
    @PostMapping("/deathAppraiseSubmit")
    @ApiOperation("死亡鉴定提交")
    public CommonResult<String> deathAppraiseSubmit(@RequestBody @Valid DeathAppraiseSaveVO reqVO) {
        reqVO.setSaveType("1");
        String id = deathRegisterService.deathAppraiseSave(reqVO);
        if(StringUtil.isEmpty(id)){
            return CommonResult.error("死亡鉴定保存出错！");
        }
        return CommonResult.success(id);
    }
    @PostMapping("/corpseHandleSave")
    @ApiOperation("尸体处理保存")
    public CommonResult<String> corpseHandleSave(@RequestBody @Valid DeathCorpseHandleSaveVO reqVO) {
        String id = null;
        try {
            id = deathRegisterService.corpseHandleSave(reqVO);
        } catch (Exception e) {
            return CommonResult.error("尸体处理保存异常！"+e.getMessage());
        }
        if(StringUtil.isEmpty(id)){
            return CommonResult.error("尸体处理保存出错！");
        }
        return CommonResult.success(id);
    }
    @PostMapping("/deathRegisterApprove")
    @ApiOperation("死亡登记审批")
    public CommonResult<String> deathRegisterApprove(@RequestBody @Valid DeathApproveSaveVO approveReqVO) {
        String id = null;
        try {
            id = deathRegisterService.deathRegisterApprove(approveReqVO);
        } catch (Exception e) {
            return CommonResult.error("死亡登记审批异常！"+e.getMessage());
        }
        if(StringUtil.isEmpty(id)){
            return CommonResult.error("死亡登记审批出错！");
        }
        return CommonResult.success(id);
    }
    @GetMapping("getDeathRegisterDetailById")
    @ApiOperation("根据id查询详情")
    public CommonResult<DeathRegisterRespVO> getById(@RequestParam("id") String id) {
        return CommonResult.success(deathRegisterService.getDeathRegisterDetailById(id));
    }
}
