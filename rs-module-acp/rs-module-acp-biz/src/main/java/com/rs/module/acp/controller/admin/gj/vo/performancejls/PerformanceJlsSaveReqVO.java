package com.rs.module.acp.controller.admin.gj.vo.performancejls;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-人员表现鉴定表-拘留所新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceJlsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("所规所纪制度，遵守：1，不遵守：2")
    private String performanceSgsjzd;

    @ApiModelProperty("一日生活管理，服从：1，不服从：2")
    private String performanceYrshgl;

    @ApiModelProperty("自杀行为或倾向，无：1  有：2")
    private String performanceZsxwhqx;

    @ApiModelProperty("暴力行为或倾向，无：1  有：2")
    private String performanceBlxwhqx;

    @ApiModelProperty("列为严管人员情况，不曾列为：1 曾列为：2")
    private String performanceLwygryqk;

    @ApiModelProperty("参加所内教育情况，积极主动：1  消极抵触：2")
    private String performanceCjsnjyqk;

    @ApiModelProperty("认错悔过情况，无：1  有：2")
    private String performanceRchgqk;

    @ApiModelProperty("其他情况")
    private String performanceQtqk;

    @ApiModelProperty("需要说明的情况")
    private String needSituations;

}
