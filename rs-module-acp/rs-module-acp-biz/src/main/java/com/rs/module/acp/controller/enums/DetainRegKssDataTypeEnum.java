package com.rs.module.acp.controller.enums;

import lombok.Getter;

@Getter
public enum DetainRegKssDataTypeEnum {

    BASIC("01", "基础信息", "com.rs.module.acp.controller.admin.db.vo.DetainRegKssBasicVO"),
    //
    CASE("02", "案件信息", "com.rs.module.acp.controller.admin.db.vo.DetainRegKssCaseVO"),
    //
    POLICE("03", "办案人信息", "com.rs.module.acp.controller.admin.db.vo.DetainRegKssPoliceVO"),
    //
    DETAIN("04", "收押信息", "com.rs.module.acp.controller.admin.db.vo.DetainRegKssDetainVO"),
    //
    REST("05", "其他信息", "com.rs.module.acp.controller.admin.db.vo.DetainRegKssRestVO"),
    //
    HEALTH("06", "入所健康检查登记", "com.rs.module.acp.controller.admin.db.vo.HealthCheckRespVO"),
    //
    BIOMETRIC("07", "生物信息采集", "com.rs.module.acp.controller.admin.db.vo.DetainRegKssBiometricVO"),
    //
    KSS_RECORD("08", "收押额外信息", "com.rs.module.acp.controller.admin.db.vo.DetainRegKssExtraVO");

    private final String code;
    private final String name;
    private final String classPath;

    DetainRegKssDataTypeEnum(String code, String name, String classPath) {
        this.code = code;
        this.name = name;
        this.classPath = classPath;
    }

    public Class<?> getEntityClass() throws ClassNotFoundException {
        return Class.forName(this.classPath);
    }

    public static DetainRegKssDataTypeEnum getTypeByCode(String code) {
        for (DetainRegKssDataTypeEnum typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        throw new IllegalArgumentException("未知类型: " + code);
    }
}
