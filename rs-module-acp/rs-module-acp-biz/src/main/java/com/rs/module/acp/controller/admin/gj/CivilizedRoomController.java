package com.rs.module.acp.controller.admin.gj;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.*;
import com.rs.module.acp.entity.gj.CivilizedRoomDO;
import com.rs.module.acp.job.civilized.CivilizedJob;
import com.rs.module.acp.service.gj.civilizedroom.CivilizedRoomService;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageWithViolationReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomRespVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-管教业务-文明监室登记")
@RestController
@RequestMapping("/acp/gj/civilizedRoom")
@Validated
public class CivilizedRoomController {

    @Resource
    private CivilizedRoomService civilizedRoomService;

    @Resource
    private CivilizedJob civilizedJob;

    @GetMapping("/test-job")
    @ApiOperation(value = "定时任务测试")
    public CommonResult<Boolean> testJob() {
        civilizedJob.pushCivilizedRoomJob();
        return success(true);
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-文明监室登记")
    public CommonResult<String> createCivilizedRoom(@Valid @RequestBody CivilizedRoomSaveReqVO createReqVO) {
        return success(civilizedRoomService.createCivilizedRoom(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-文明监室登记")
    public CommonResult<Boolean> updateCivilizedRoom(@Valid @RequestBody CivilizedRoomSaveReqVO updateReqVO) {
        civilizedRoomService.updateCivilizedRoom(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-文明监室登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCivilizedRoom(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            civilizedRoomService.deleteCivilizedRoom(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CivilizedRoomRespVO> getCivilizedRoom(@RequestParam("id") String id) {
        return success(civilizedRoomService.getCivilizedRoom(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-文明监室登记分页")
    public CommonResult<PageResult<CivilizedRoomRespVO>> getCivilizedRoomPage(@Valid @RequestBody CivilizedRoomPageReqVO pageReqVO) {
        PageResult<CivilizedRoomDO> pageResult = civilizedRoomService.getCivilizedRoomPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CivilizedRoomRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "文明监室登记列表")
    public CommonResult<List<CivilizedRoomRespVO>> getCivilizedRoomList(@Valid @RequestBody CivilizedRoomListReqVO listReqVO) {
        if (StringUtils.isEmpty(listReqVO.getOrgCode())) {
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        return success(civilizedRoomService.getCivilizedRoomList(listReqVO));
    }

    @PostMapping("/registEvaluation")
    @ApiOperation(value = "登记评比")
    public CommonResult<Boolean> registEvaluation(@Valid @RequestBody CivilizedRoomRegistEvaluationReqVO reqVO) {
        civilizedRoomService.registEvaluation(reqVO);
        return success(true);
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody CivilizedRoomApprovalReqVO reqVO) {
        civilizedRoomService.approval(reqVO);
        return success(true);
    }

    @PostMapping("/againEvaluation")
    @ApiOperation(value = "重新评比")
    public CommonResult<Boolean> againEvaluation(@Valid @RequestBody CivilizedRoomRegistEvaluationReqVO reqVO) {
        civilizedRoomService.againEvaluation(reqVO);
        return success(true);
    }


}
