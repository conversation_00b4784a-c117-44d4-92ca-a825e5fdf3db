package com.rs.module.acp.controller.admin.gj.vo.riskassmt;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-风险评估待办新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskAssmtTodoSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("来源业务ID")
    @NotEmpty(message = "来源业务ID不能为空")
    private String sourceBusinessId;

    @ApiModelProperty("评估类型")
    @NotEmpty(message = "评估类型不能为空")
    private String riskType;

}
