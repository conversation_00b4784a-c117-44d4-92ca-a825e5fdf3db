package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityPageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivityRespVO;
import com.rs.module.acp.controller.admin.gj.vo.edurehabcourses.EdurehabActivitySaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.EdurehabActivityDO;
import com.rs.module.acp.service.gj.edurehabcourses.EdurehabActivityService;

@Api(tags = "实战平台-管教业务-教育康复活动")
@RestController
@RequestMapping("/acp/gj/edurehabActivity")
@Validated
public class EdurehabActivityController {

    @Resource
    private EdurehabActivityService edurehabActivityService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-教育康复活动")
    public CommonResult<String> createEdurehabActivity(@Valid @RequestBody EdurehabActivitySaveReqVO createReqVO) {
        return success(edurehabActivityService.createEdurehabActivity(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-教育康复活动")
    public CommonResult<Boolean> updateEdurehabActivity(@Valid @RequestBody EdurehabActivitySaveReqVO updateReqVO) {
        edurehabActivityService.updateEdurehabActivity(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-教育康复活动")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEdurehabActivity(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           edurehabActivityService.deleteEdurehabActivity(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复活动")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EdurehabActivityRespVO> getEdurehabActivity(@RequestParam("id") String id) {
        return success(edurehabActivityService.getEdurehabActivity(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复活动分页")
    public CommonResult<PageResult<EdurehabActivityRespVO>> getEdurehabActivityPage(@Valid @RequestBody EdurehabActivityPageReqVO pageReqVO) {
        PageResult<EdurehabActivityDO> pageResult = edurehabActivityService.getEdurehabActivityPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EdurehabActivityRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-教育康复活动列表")
    public CommonResult<List<EdurehabActivityRespVO>> getEdurehabActivityList(@Valid @RequestBody EdurehabActivityListReqVO listReqVO) {
        List<EdurehabActivityDO> list = edurehabActivityService.getEdurehabActivityList(listReqVO);
        return success(BeanUtils.toBean(list, EdurehabActivityRespVO.class));
    }


    @GetMapping("/jykfcjqk")
    @ApiOperation(value = "教育康复参加情况")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编号")
    public CommonResult<PageResult<EdurehabActivityRespVO>> jykfcjqk(String jgrybm) {
        PageResult<EdurehabActivityDO> pageResult = edurehabActivityService.jykfcjqk(jgrybm);
        return success(BeanUtils.toBean(pageResult, EdurehabActivityRespVO.class));
    }
}
