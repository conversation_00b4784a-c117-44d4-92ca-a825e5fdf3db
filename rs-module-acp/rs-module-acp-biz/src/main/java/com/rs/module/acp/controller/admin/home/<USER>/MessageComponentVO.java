package com.rs.module.acp.controller.admin.home.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.Document;

import java.util.Collection;
import java.util.List;


@Data
@ApiModel(value = "消息组件VO对象", description = "消息组件VO对象")
public class MessageComponentVO {

    private String typeCode;
    @ApiModelProperty(value = "消息总数")
    private String total;
    @ApiModelProperty(value = "未处理消息数")
    private String totalNotDone;
    @ApiModelProperty(value = "已处理消息数")
    private String totalDone;
    @ApiModelProperty(value = "消息列表")
    private Collection<BusTypeItem> items;
    @Data
    public static class BusTypeItem {
        private String busType;
        private String busTypeCode;
        private List<Document> msgList;
        public void addMsg(Document msg) {
            this.msgList.add(msg);
        }
    }
}
