package com.rs.module.acp.controller.enums;

import lombok.Getter;

@Getter
public enum BizCorrectionBusinessTypeEnum {

    DETAIN_KSS("001", "看守所收押信息"),
    DETAIN_JLS("002", "拘留所收押信息"),
    DETAIN_JDS("003", "戒毒所收押信息");

    private final String code;
    private final String name;

    BizCorrectionBusinessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static BizCorrectionBusinessTypeEnum getTypeByCode(String code) {
        for (BizCorrectionBusinessTypeEnum typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        throw new IllegalArgumentException("未知类型: " + code);
    }

    public static String getNameByCode(String code) {
        for (BizCorrectionBusinessTypeEnum typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum.getName();
            }
        }
        throw new IllegalArgumentException("未知类型: " + code);
    }
}
