package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.biometriccomp.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.BiometricCompDO;
import com.rs.module.acp.service.gj.biometriccomp.BiometricCompService;

@Api(tags = "实战平台-管教业务-生物特征比对")
@RestController
@RequestMapping("/acp/gj/biometricComp")
@Validated
public class BiometricCompController {

    @Resource
    private BiometricCompService biometricCompService;

    @PostMapping("/regist")
    @ApiOperation(value = "比对登记")
    public CommonResult<String> compareCreate(@Valid @RequestBody BiometricComPareSaveReqVO createReqVO) {
        return success(biometricCompService.compareCreate(createReqVO));
    }

    @PostMapping("/feedback")
    @ApiOperation(value = "比对反馈")
    public CommonResult<Boolean> compareFeedback(@Valid @RequestBody BiometricCompFkReqVO fkReqVO) {
        biometricCompService.compareFeedback(fkReqVO);
        return success(true);
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-生物特征比对")
    public CommonResult<String> createBiometricComp(@Valid @RequestBody BiometricCompSaveReqVO createReqVO) {
        return success(biometricCompService.createBiometricComp(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-生物特征比对")
    public CommonResult<Boolean> updateBiometricComp(@Valid @RequestBody BiometricCompSaveReqVO updateReqVO) {
        biometricCompService.updateBiometricComp(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-生物特征比对")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBiometricComp(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           biometricCompService.deleteBiometricComp(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-生物特征比对")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BiometricCompRespVO> getBiometricComp(@RequestParam("id") String id) {
        BiometricCompDO biometricComp = biometricCompService.getBiometricComp(id);
        return success(BeanUtils.toBean(biometricComp, BiometricCompRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-生物特征比对分页")
    public CommonResult<PageResult<BiometricCompRespVO>> getBiometricCompPage(@Valid @RequestBody BiometricCompPageReqVO pageReqVO) {
        PageResult<BiometricCompDO> pageResult = biometricCompService.getBiometricCompPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BiometricCompRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-生物特征比对列表")
    public CommonResult<List<BiometricCompRespVO>> getBiometricCompList(@Valid @RequestBody BiometricCompListReqVO listReqVO) {
        List<BiometricCompDO> list = biometricCompService.getBiometricCompList(listReqVO);
        return success(BeanUtils.toBean(list, BiometricCompRespVO.class));
    }
}
