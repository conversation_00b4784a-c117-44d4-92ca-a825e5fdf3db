package com.rs.module.acp.controller.admin.gj.vo.bookingapprove;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-预约审核配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class BookingApprovalConfigRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("预约类别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_YYSHGL_YYLX")
    private String bookingCategory;

    @ApiModelProperty("服务类别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_YYSHGL_FWLX")
    private String serviceCategory;

    @ApiModelProperty("是否启用")
    private Integer isEnabled;

    @ApiModelProperty("入所时间")
    private Date rssj;
}
