package com.rs.module.acp.controller.admin.pi;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.service.pi.PatrolRecordService;
import com.rs.third.api.component.haikang.HaiKangTVWallControlComponent;
import com.rs.third.api.dto.haikang.tvwall.HaiKangApiResponse;
import com.rs.third.api.model.tvwall.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-巡视管控-高风险人员定屏监控测试")
@RestController
@RequestMapping("/acp/pi/tvwall")
@Validated
public class TVWallController {

    @Resource
    private PatrolRecordService patrolRecordService;

    @Resource
    private HaiKangTVWallControlComponent tvWallControlComponent;

    // =============== 电视墙接口方法 ===============

    @GetMapping("/getAllResources")
    @ApiOperation("获取电视墙大屏信息")
    public CommonResult<HaiKangApiResponse> getAllResources() {
        try {
            HaiKangApiResponse response = tvWallControlComponent.getAllResources();
            return success(response);
        } catch (Exception e) {
            return error("获取电视墙大屏信息失败: " + e.getMessage());
        }
    }

    @GetMapping("/getScenes")
    @ApiOperation("获取电视墙场景列表信息")
    public CommonResult<HaiKangApiResponse> getScenes() {
        try {
            HaiKangApiResponse response = tvWallControlComponent.getScenes();
            return success(response);
        } catch (Exception e) {
            return error("获取电视墙场景列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/getWindowList")
    @ApiOperation("获取电视墙窗口信息列表")
    public CommonResult<HaiKangApiResponse> getWindowList(@RequestBody TvWallWndsReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.getWindowList(req);
            return success(response);
        } catch (Exception e) {
            return error("获取电视墙窗口信息列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/batchAddToWall")
    @ApiOperation("批量上墙")
    public CommonResult<HaiKangApiResponse> batchAddToWall(@RequestBody TvWallRealPlayReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.batchAddToWall(req);
            return success(response);
        } catch (Exception e) {
            return error("批量上墙失败: " + e.getMessage());
        }
    }

    @PostMapping("/batchRemoveFromWall")
    @ApiOperation("批量下墙")
    public CommonResult<HaiKangApiResponse> batchRemoveFromWall(@RequestBody TvWallRealPlayReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.batchRemoveFromWall(req);
            return success(response);
        } catch (Exception e) {
            return error("批量下墙失败: " + e.getMessage());
        }
    }

    @PostMapping("/createScene")
    @ApiOperation("场景创建")
    public CommonResult<HaiKangApiResponse> createScene(@RequestBody TvWallSceneCreateReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.createScene(req);
            return success(response);
        } catch (Exception e) {
            return error("创建场景失败: " + e.getMessage());
        }
    }

    @PostMapping("/updateScene")
    @ApiOperation("场景修改")
    public CommonResult<HaiKangApiResponse> updateScene(@RequestBody TvWallSceneUpdateReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.updateScene(req);
            return success(response);
        } catch (Exception e) {
            return error("修改场景失败: " + e.getMessage());
        }
    }

    @PostMapping("/deleteScene")
    @ApiOperation("场景删除")
    public CommonResult<HaiKangApiResponse> deleteScene(@RequestBody TvWallSceneDeleteReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.deleteScene(req);
            return success(response);
        } catch (Exception e) {
            return error("删除场景失败: " + e.getMessage());
        }
    }

    @PostMapping("/saveAsScene")
    @ApiOperation("场景另存为")
    public CommonResult<HaiKangApiResponse> saveAsScene(@RequestBody TvWallSceneSaveAsReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.saveAsScene(req);
            return success(response);
        } catch (Exception e) {
            return error("场景另存为失败: " + e.getMessage());
        }
    }

    @PostMapping("/switchScene")
    @ApiOperation("电视墙场景切换")
    public CommonResult<HaiKangApiResponse> switchScene(@RequestBody TvWallSceneSwitchReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.switchScene(req);
            return success(response);
        } catch (Exception e) {
            return error("切换场景失败: " + e.getMessage());
        }
    }

    @PostMapping("/addAlarm")
    @ApiOperation("新增报警")
    public CommonResult<HaiKangApiResponse> addAlarm(@RequestBody TvWallAlarmAddReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.addAlarm(req);
            return success(response);
        } catch (Exception e) {
            return error("新增报警失败: " + e.getMessage());
        }
    }

    @PostMapping("/deleteAlarm")
    @ApiOperation("删除报警")
    public CommonResult<HaiKangApiResponse> deleteAlarm(@RequestBody TvWallAlarmDeleteReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.deleteAlarm(req);
            return success(response);
        } catch (Exception e) {
            return error("删除报警失败: " + e.getMessage());
        }
    }

    @PostMapping("/divideWindow")
    @ApiOperation("窗口分割")
    public CommonResult<HaiKangApiResponse> divideWindow(@RequestBody TvWallFloatWndDivisionReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.divideWindow(req);
            return success(response);
        } catch (Exception e) {
            return error("窗口分割失败: " + e.getMessage());
        }
    }

    @PostMapping("/batchCreateWindows")
    @ApiOperation("窗口批量创建")
    public CommonResult<HaiKangApiResponse> batchCreateWindows(@RequestBody TvWallFloatWndsCreateReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.batchCreateWindows(req);
            return success(response);
        } catch (Exception e) {
            return error("批量创建窗口失败: " + e.getMessage());
        }
    }

    @PostMapping("/batchDeleteWindows")
    @ApiOperation("窗口批量删除")
    public CommonResult<HaiKangApiResponse> batchDeleteWindows(@RequestBody TvWallFloatWndsDeleteReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.batchDeleteWindows(req);
            return success(response);
        } catch (Exception e) {
            return error("批量删除窗口失败: " + e.getMessage());
        }
    }

    @PostMapping("/zoomInWindow")
    @ApiOperation("窗口放大")
    public CommonResult<HaiKangApiResponse> zoomInWindow(@RequestBody TvWallFloatWndZoomReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.zoomInWindow(req);
            return success(response);
        } catch (Exception e) {
            return error("窗口放大失败: " + e.getMessage());
        }
    }

    @PostMapping("/moveWindow")
    @ApiOperation("窗口漫游")
    public CommonResult<HaiKangApiResponse> moveWindow(@RequestBody TvWallFloatWndMoveReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.moveWindow(req);
            return success(response);
        } catch (Exception e) {
            return error("窗口漫游失败: " + e.getMessage());
        }
    }

    @PostMapping("/setWindowLayer")
    @ApiOperation("窗口置顶或置底")
    public CommonResult<HaiKangApiResponse> setWindowLayer(@RequestBody TvWallFloatWndLayerCtrlReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.setWindowLayer(req);
            return success(response);
        } catch (Exception e) {
            return error("窗口置顶/置底失败: " + e.getMessage());
        }
    }

    @PostMapping("/zoomOutWindow")
    @ApiOperation("窗口还原")
    public CommonResult<HaiKangApiResponse> zoomOutWindow(@RequestBody TvWallFloatWndZoomOutReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.zoomOutWindow(req);
            return success(response);
        } catch (Exception e) {
            return error("窗口还原失败: " + e.getMessage());
        }
    }

    @PostMapping("/divideMonitor")
    @ApiOperation("非开窗设备窗口分割")
    public CommonResult<HaiKangApiResponse> divideMonitor(@RequestBody TvWallMonitorDivisionReq req) {
        try {
            HaiKangApiResponse response = tvWallControlComponent.divideMonitor(req);
            return success(response);
        } catch (Exception e) {
            return error("非开窗设备窗口分割失败: " + e.getMessage());
        }
    }
}
