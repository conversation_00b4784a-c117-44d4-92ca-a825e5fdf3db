package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情事件在押人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventPrisonerRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所情事件ID")
    private String eventId;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("扣分值")
    private Integer deductPoint;
    @ApiModelProperty("应扣分值")
    private Integer shouldDeductPoint;
    @ApiModelProperty("监室号")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("照片")
    private String zpUrl;
}
