package com.rs.module.acp.controller.admin.pi.vo.prisonevent;
import com.alibaba.fastjson.annotation.JSONField;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情处置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventHandleRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("所属警情")
    private String eventId;
    @ApiModelProperty("处置反馈")
    private String feedbackInfo;
    @ApiModelProperty("处置人身份证号")
    private String handleUserSfzh;
    @ApiModelProperty("处置人名称")
    private String handleUserName;
    @ApiModelProperty("处置时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;
    @ApiModelProperty("处置状态 0未办结 1已办结 2不通过 3通过")
    private String status;
    @ApiModelProperty("处置时所处岗位")
    private String handlePost;
    @ApiModelProperty("处置岗位编码")
    private String handlePostCode;
    @ApiModelProperty("历史数据：1是，0最新数据")
    private Integer history;
    @ApiModelProperty("处置类型 1.处置，2审批")
    private Integer handleType;
    @ApiModelProperty("审批内容")
    private String handleInfo;
    @ApiModelProperty("变动内容")
    private String changeContent;
    @ApiModelProperty("附件地址")
    private String attUrl;

    @ApiModelProperty("处置预案")
    private String disposePlans;
    @ApiModelProperty("处置业务（多个用逗号分隔）")
    private String disposeBusiness;

}
