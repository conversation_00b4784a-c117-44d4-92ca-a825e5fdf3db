package com.rs.module.acp.controller.admin.sys;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigRespVO;
import com.rs.module.acp.controller.admin.sys.vo.BasicConfigSaveReqVO;
import com.rs.module.acp.entity.sys.BasicConfigDO;
import com.rs.module.acp.service.sys.BasicConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-系统基础配置")
@RestController
@RequestMapping("/acp/sys/basicConfig")
@Validated
public class BasicConfigController {

    @Resource
    private BasicConfigService basicConfigService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-系统基础配置")
    public CommonResult<String> createBasicConfig(@Valid @RequestBody BasicConfigSaveReqVO createReqVO) {
        return success(basicConfigService.createBasicConfig(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-系统基础配置")
    public CommonResult<Boolean> updateBasicConfig(@Valid @RequestBody BasicConfigSaveReqVO updateReqVO) {
        basicConfigService.updateBasicConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-系统基础配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBasicConfig(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           basicConfigService.deleteBasicConfig(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-系统基础配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BasicConfigRespVO> getBasicConfig() {
        BasicConfigRespVO basicConfig = basicConfigService.getBasicConfig();
        return success(basicConfig);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-系统基础配置分页")
    public CommonResult<PageResult<BasicConfigRespVO>> getBasicConfigPage(@Valid @RequestBody BasicConfigPageReqVO pageReqVO) {
        PageResult<BasicConfigDO> pageResult = basicConfigService.getBasicConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BasicConfigRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-系统基础配置列表")
    public CommonResult<List<BasicConfigRespVO>> getBasicConfigList(@Valid @RequestBody BasicConfigListReqVO listReqVO) {
        List<BasicConfigDO> list = basicConfigService.getBasicConfigList(listReqVO);
        return success(BeanUtils.toBean(list, BasicConfigRespVO.class));
    }
}
