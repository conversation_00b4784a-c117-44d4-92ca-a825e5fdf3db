package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import io.swagger.annotations.ApiModel;

import java.time.LocalTime;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复课程记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EdurehabCoursesRecordPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("教育康复课程计划ID")
    private String edurehabCoursesPlanId;

    @ApiModelProperty("课程日期")
    private Date[] coursesDate;

    @ApiModelProperty("监区ID")
    private String areaId;

    @ApiModelProperty("监区名称")
    private String areaName;

    @ApiModelProperty("课程时段编码，如“9:00-10:00”，唯一标识时段")
    private String timeSlotCode;

    @ApiModelProperty("课程时段-开始")
    private String timeSlotStartTime;

    @ApiModelProperty("课程时段-结束")
    private String timeSlotEndTime;

    @ApiModelProperty("courses_code")
    private String coursesCode;

    @ApiModelProperty("courses_name")
    private String coursesName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
