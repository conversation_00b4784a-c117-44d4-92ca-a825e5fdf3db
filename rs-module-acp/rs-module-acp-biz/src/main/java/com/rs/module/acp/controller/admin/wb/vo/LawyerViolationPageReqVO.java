package com.rs.module.acp.controller.admin.wb.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-律师违规分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LawyerViolationPageReqVO extends PageParam {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("律师表ID")
    private String lawyerId;

    @ApiModelProperty("违规时间")
    private Date[] violationTime;

    @ApiModelProperty("违规情况")
    private String violationType;

    @ApiModelProperty("详细说明")
    private String description;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
