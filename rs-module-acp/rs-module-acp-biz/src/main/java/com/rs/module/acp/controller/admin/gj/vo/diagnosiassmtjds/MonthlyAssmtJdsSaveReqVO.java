package com.rs.module.acp.controller.admin.gj.vo.diagnosiassmtjds;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-月度考核(戒毒所)新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MonthlyAssmtJdsSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("监室id")
    @NotEmpty(message = "监室id不能为空")
    private String roomId;

    @ApiModelProperty("监室名称")
    @NotEmpty(message = "监室名称不能为空")
    private String roomName;

    @ApiModelProperty("推送时间")
    @NotNull(message = "推送时间不能为空")
    private Date pushTime;

    @ApiModelProperty("考核内容值1")
    private String assmtContentValue1;

    @ApiModelProperty("考核内容值2")
    private String assmtContentValue2;

    @ApiModelProperty("考核内容值3")
    private String assmtContentValue3;

    @ApiModelProperty("考核内容值4")
    private String assmtContentValue4;

    @ApiModelProperty("考核内容值5")
    private String assmtContentValue5;

    @ApiModelProperty("考核内容值6")
    private String assmtContentValue6;

    @ApiModelProperty("考核内容值7")
    private String assmtContentValue7;

    @ApiModelProperty("考核内容值8")
    private String assmtContentValue8;

    @ApiModelProperty("考核内容值9")
    private String assmtContentValue9;

    @ApiModelProperty("考核内容值10")
    private String assmtContentValue10;

    @ApiModelProperty("考核内容值10")
    private String assmtContentTotalScore;

    @ApiModelProperty("加分或扣分情况")
    private String bonusOrPenaltyPointsSituati;

    @ApiModelProperty("评估内容JSON")
    private String asstmContentJson;

    @ApiModelProperty("评估人身份证号")
    private String assmtUserSfzh;

    @ApiModelProperty("评估人姓名")
    private String assmtUser;

    @ApiModelProperty("评估时间")
    private String assmtTime;

    @ApiModelProperty("办理状态")
    @NotEmpty(message = "办理状态不能为空")
    private String status;

    @ApiModelProperty("确认时间")
    private Date confirmTime;

    @ApiModelProperty("在押人员签名地址")
    private String prisonerSignature;

    @ApiModelProperty("月度考核周期")
    private String monthPeriod;

    @ApiModelProperty("依据")
    private String according;

}
