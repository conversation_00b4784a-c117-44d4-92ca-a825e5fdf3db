package com.rs.module.acp.controller.admin.pm.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "管理后台 - 实战平台-监管管理-门禁点管理 Response VO")
@Data
public class DeviceDoorRespVO implements TransPojo {

    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("资源唯一编码")
    private String indexCode;
    @ApiModelProperty("资源类型")
    private String resourceType;
    @ApiModelProperty("资源名称")
    private String name;
    @ApiModelProperty("门禁点编号")
    private String doorNo;
    @ApiModelProperty("通道口")
    private String channelNo;
    @ApiModelProperty("门序号")
    private String doorSerial;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("通道类型")
    private String channelType;
    @ApiModelProperty("原区域名称")
    private String originRegionName;
    @ApiModelProperty("原区域路径")
    private String originRegionPathName;
    @ApiModelProperty("监区ID")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("门禁点状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_MJDZT")
    private String doorStatus;
    @ApiModelProperty("绑定监控室状态(0-未绑定，1-已绑定)")
    private String bindRoomStatus;

}
