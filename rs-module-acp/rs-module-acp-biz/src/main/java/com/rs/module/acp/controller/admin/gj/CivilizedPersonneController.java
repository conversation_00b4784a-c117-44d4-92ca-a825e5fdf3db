package com.rs.module.acp.controller.admin.gj;

import com.alibaba.druid.util.StringUtils;
import com.bsp.security.util.SessionUserUtil;
import com.gosun.zhjg.common.util.StringUtil;
import com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne.*;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomApprovalReqVO;
import com.rs.module.acp.controller.admin.gj.vo.civilizedroom.CivilizedRoomRegistEvaluationReqVO;
import com.rs.module.acp.job.civilized.CivilizedJob;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.CivilizedPersonneDO;
import com.rs.module.acp.service.gj.civilizedpersonne.CivilizedPersonneService;

@Api(tags = "实战平台-管教业务-文明个人登记")
@RestController
@RequestMapping("/acp/gj/civilizedPersonne")
@Validated
public class CivilizedPersonneController {

    @Resource
    private CivilizedPersonneService civilizedPersonneService;

    @Resource
    private CivilizedJob civilizedJob;

    @GetMapping("/test-job")
    @ApiOperation(value = "定时任务测试")
    public CommonResult<Boolean> testJob() {
        civilizedJob.pushCivilizedPersonneJob();
        return success(true);
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-文明个人登记")
    public CommonResult<String> createCivilizedPersonne(@Valid @RequestBody CivilizedPersonneSaveReqVO createReqVO) {
        return success(civilizedPersonneService.createCivilizedPersonne(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-文明个人登记")
    public CommonResult<Boolean> updateCivilizedPersonne(@Valid @RequestBody CivilizedPersonneSaveReqVO updateReqVO) {
        civilizedPersonneService.updateCivilizedPersonne(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-文明个人登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteCivilizedPersonne(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
            civilizedPersonneService.deleteCivilizedPersonne(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<CivilizedPersonneRespVO> getCivilizedPersonne(@RequestParam("id") String id) {
        return success(civilizedPersonneService.getCivilizedPersonne(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-文明个人登记分页")
    public CommonResult<PageResult<CivilizedPersonneRespVO>> getCivilizedPersonnePage(@Valid @RequestBody CivilizedPersonnePageReqVO pageReqVO) {
        PageResult<CivilizedPersonneDO> pageResult = civilizedPersonneService.getCivilizedPersonnePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CivilizedPersonneRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "文明个人登记列表")
    public CommonResult<List<CivilizedPersonneRespVO>> getCivilizedPersonneList(@Valid @RequestBody CivilizedPersonneListReqVO listReqVO) {
        if(StringUtils.isEmpty(listReqVO.getOrgCode())){
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        return success(civilizedPersonneService.getCivilizedPersonneList(listReqVO));
    }

    @PostMapping("/registEvaluation")
    @ApiOperation(value = "登记评比")
    public CommonResult<Boolean> registEvaluation(@Valid @RequestBody CivilizedPersonneRegistReqVO registReqVO) {
        civilizedPersonneService.registEvaluation(registReqVO);
        return success(true);
    }

    @PostMapping("/approval")
    @ApiOperation(value = "审批")
    public CommonResult<Boolean> approval(@Valid @RequestBody CivilizedRoomApprovalReqVO reqVO) {
        civilizedPersonneService.approval(reqVO);
        return success(true);
    }

    @PostMapping("/againEvaluation")
    @ApiOperation(value = "重新评比")
    public CommonResult<Boolean> againEvaluation(@Valid @RequestBody CivilizedPersonneRegistReqVO registReqVO) {
        civilizedPersonneService.againEvaluation(registReqVO);
        return success(true);
    }
}
