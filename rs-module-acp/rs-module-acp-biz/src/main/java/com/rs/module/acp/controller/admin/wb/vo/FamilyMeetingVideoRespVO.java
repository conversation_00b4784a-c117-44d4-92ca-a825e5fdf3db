package com.rs.module.acp.controller.admin.wb.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-单向视频家属会见 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyMeetingVideoRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_WB_JSDXSPHJZT")
    private String status;
    @ApiModelProperty("通知家属时间")
    private Date notificationFamilyTime;
    @ApiModelProperty("通知会见日期")
    private Date notificationMeetingDate;
    @ApiModelProperty("会见开始时间")
    private Date meetingStartTime;
    @ApiModelProperty("会见结束时间")
    private Date meetingEndTime;
    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("通知操作人身份证号")
    private String notificationOperatorSfzh;
    @ApiModelProperty("通知操作人")
    private String notificationOperator;
    @ApiModelProperty("通知操作时间")
    private Date notificationOperatorTime;

    @ApiModelProperty("登记操作人身份证号")
    private String checkOperatorSfzh;
    @ApiModelProperty("登记操作人")
    private String checkOperator;
    @ApiModelProperty("登记操作时间")
    private Date checkOperatorTime;

    @ApiModelProperty("家属姓名（单向视频会见记录使用）")
    private String familyName;
    @ApiModelProperty("证件号码（单向视频会见记录使用）")
    private String idNumber;
    @ApiModelProperty("社会关系（单向视频会见记录使用）")
    private String relationship;
    @ApiModelProperty("会见时间（单向视频会见记录使用）")
    private String meetingTime;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    @ApiModelProperty("家属列表")
    private List<SocialRelationsRespVO> familyList;
}
