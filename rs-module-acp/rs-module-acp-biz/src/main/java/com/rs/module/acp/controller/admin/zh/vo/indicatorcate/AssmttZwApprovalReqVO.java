package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-加减分考核审核新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmttZwApprovalReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;


    @ApiModelProperty("绩效打分记录")
    @NotEmpty(message = "绩效打分记录 不能为空")
    private List<AssmttZwApprovalRecordReqVO> approvalRecordList;

}
