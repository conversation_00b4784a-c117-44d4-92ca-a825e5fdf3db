package com.rs.module.acp.controller.admin.gj.vo.civilizedroom;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明监室登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedRoomListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;

    @ApiModelProperty("评比年份 yyyy")
    @NotEmpty(message = "评比年份 不能为空")
    private String year;

    @ApiModelProperty("评比月份")
    private String evalMonth;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("申请时间")
    private Date[] applyTime;

    @ApiModelProperty("申请人身份证号")
    private String applyUserSfzh;

    @ApiModelProperty("申请人")
    private String applyUser;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("机构")
    private String orgCode;

}
