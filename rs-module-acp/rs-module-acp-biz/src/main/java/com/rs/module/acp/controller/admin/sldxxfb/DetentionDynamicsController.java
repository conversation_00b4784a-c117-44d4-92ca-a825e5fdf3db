package com.rs.module.acp.controller.admin.sldxxfb;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.service.sldxxfb.SldxxfbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "实战平台-所领导信息发布-羁押动态")
@RestController
@RequestMapping("/acp/sldxxfb/detentiondynamics")
public class DetentionDynamicsController {
    @Autowired
    private SldxxfbService sldxxfbService;
    //当前羁押总数 ，昨日羁押总数
    @GetMapping("/zdsj")
    @ApiOperation(value = "所领导信息发布-当前羁押总数 ，昨日羁押总数")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<JSONObject> getDetentionDynamics() {
        return CommonResult.success(sldxxfbService.zdsj());
    }
    @GetMapping("/xsry")
    @ApiOperation(value = "所领导信息发布-新收人员")
    public CommonResult<JSONObject> xsry() {
        return CommonResult.success(sldxxfbService.xxry(null));
    }
    @GetMapping("/csry")
    @ApiOperation(value = "所领导信息发布-出所人员")
    public CommonResult<JSONObject> csry(@RequestParam(required = false) String orgCode) {
        return CommonResult.success(sldxxfbService.csry(orgCode));
    }
    @GetMapping("/ssjd")
    @ApiOperation(value = "所领导信息发布-诉讼阶段")
    public CommonResult<JSONObject> ssjd(@RequestParam(required = false) String orgCode) {
        return CommonResult.success(sldxxfbService.ssjd(orgCode));
    }

}
