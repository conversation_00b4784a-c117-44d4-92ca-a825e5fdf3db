package com.rs.module.acp.controller.admin.pi.vo.shifthandover;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-接班登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SuccessionRegReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("接班总人数")
    private Integer takeoverTotalPersons;

    @ApiModelProperty("接班人身份证号")
    private String takeoverPersonSfzh;

    @ApiModelProperty("接班人")
    private String takeoverPerson;

    @ApiModelProperty("接班时间")
    private Date takeoverTime;

}
