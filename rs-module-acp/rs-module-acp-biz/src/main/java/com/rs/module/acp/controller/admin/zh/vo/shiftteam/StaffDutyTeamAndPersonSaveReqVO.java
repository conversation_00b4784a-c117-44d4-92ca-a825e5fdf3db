package com.rs.module.acp.controller.admin.zh.vo.shiftteam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@ApiModel(description = "管理后台 - 值班模板班组及人员信息新增/修改 Request VO")
@Data
public class StaffDutyTeamAndPersonSaveReqVO {

    @ApiModelProperty("值班模板班组信息")
    private StaffDutyTeamSaveReqVO teamInfo;

    @ApiModelProperty("值班模板班组人员信息列表")
    private List<StaffDutyTeamPersonSaveReqVO> personInfos;
}