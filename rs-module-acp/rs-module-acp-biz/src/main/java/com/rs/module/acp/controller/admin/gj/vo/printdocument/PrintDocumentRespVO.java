package com.rs.module.acp.controller.admin.gj.vo.printdocument;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文书打印预览 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrintDocumentRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("关联id")
    private String glId;
    @ApiModelProperty("关联类型")
    private String glType;
    @ApiModelProperty("文书打印其他信息")
    private String wsdata;
}
