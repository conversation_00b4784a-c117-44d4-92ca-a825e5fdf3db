package com.rs.module.acp.controller.admin.zh.vo.deathregister;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 死亡登记- 死亡鉴定
 */
@ApiModel(description = "实战平台-综合管理-死亡登记-死亡鉴定新增/修改 Request VO")
@Data
public class DeathAppraiseSaveVO {

    @NotBlank(message = "ID不能为空")
    @ApiModelProperty(value = "id", required = true)
    private String id;
    @ApiModelProperty(value = "保存类型 0 保存,1 提交",hidden = true)
    private String saveType;
    /** 死亡鉴定经办时间 */
    //private Date appraiseCreateTime ;
    /** 死亡鉴定经办人，对应permission_user.userid */
    //private Integer appraiseCreateUserId ;
    /** 死亡鉴定经办人名称 */
   // private String appraiseCreateUserName ;
    @NotEmpty(message = "死亡鉴定-处理情况不能为空")
    @ApiModelProperty("死亡鉴定-处理情况")
    private String handleSituation ;

    @ApiModelProperty(value = "死亡鉴定单位代码", required = true)
    private String deathAppraiseUnitCode;

    @ApiModelProperty(value = "死亡鉴定单位名称", required = true)
    private String deathAppraiseUnitName ;
    /**
     * 文件、图片信息
     */
    @ApiModelProperty("文件、图片信息")
    private List<DeathRegisterFilesSaveReqVO> fileList;
}
