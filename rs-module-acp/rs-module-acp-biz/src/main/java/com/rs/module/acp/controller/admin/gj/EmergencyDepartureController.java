package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDeparturePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureRespVO;
import com.rs.module.acp.controller.admin.gj.vo.emergencydeparture.EmergencyDepartureSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.EmergencyDepartureDO;
import com.rs.module.acp.service.gj.emergencydeparture.EmergencyDepartureService;

@Api(tags = "实战平台-管教业务-紧急出所登记")
@RestController
@RequestMapping("/acp/gj/emergencyDeparture")
@Validated
public class EmergencyDepartureController {

    @Resource
    private EmergencyDepartureService emergencyDepartureService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-紧急出所登记")
    public CommonResult<String> createEmergencyDeparture(@Valid @RequestBody EmergencyDepartureSaveReqVO createReqVO) {
        return success(emergencyDepartureService.createEmergencyDeparture(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-紧急出所登记")
    public CommonResult<Boolean> updateEmergencyDeparture(@Valid @RequestBody EmergencyDepartureSaveReqVO updateReqVO) {
        emergencyDepartureService.updateEmergencyDeparture(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-紧急出所登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteEmergencyDeparture(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           emergencyDepartureService.deleteEmergencyDeparture(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-紧急出所登记")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<EmergencyDepartureRespVO> getEmergencyDeparture(@RequestParam("id") String id) {
        EmergencyDepartureDO emergencyDeparture = emergencyDepartureService.getEmergencyDeparture(id);
        return success(BeanUtils.toBean(emergencyDeparture, EmergencyDepartureRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-紧急出所登记分页")
    public CommonResult<PageResult<EmergencyDepartureRespVO>> getEmergencyDeparturePage(@Valid @RequestBody EmergencyDeparturePageReqVO pageReqVO) {
        PageResult<EmergencyDepartureDO> pageResult = emergencyDepartureService.getEmergencyDeparturePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EmergencyDepartureRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-紧急出所登记列表")
    public CommonResult<List<EmergencyDepartureRespVO>> getEmergencyDepartureList(@Valid @RequestBody EmergencyDepartureListReqVO listReqVO) {
        List<EmergencyDepartureDO> list = emergencyDepartureService.getEmergencyDepartureList(listReqVO);
        return success(BeanUtils.toBean(list, EmergencyDepartureRespVO.class));
    }
}
