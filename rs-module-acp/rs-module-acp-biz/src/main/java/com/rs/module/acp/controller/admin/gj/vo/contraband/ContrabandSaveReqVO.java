package com.rs.module.acp.controller.admin.gj.vo.contraband;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-违禁品登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ContrabandSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    @NotEmpty(message = "监管人员编码不能为空")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    @NotEmpty(message = "监管人员姓名不能为空")
    private String jgryxm;

    @ApiModelProperty("检查时间")
    private Date checkTime;

    @ApiModelProperty("违禁品类别, 字典:(ZD_CONTRABAND_TYPE)")
    private String contrabandCategory;

    @ApiModelProperty("违禁品照片")
    private String contrabandImgPath;

    @ApiModelProperty("处理情况")
    private String handlingSituatio;

}
