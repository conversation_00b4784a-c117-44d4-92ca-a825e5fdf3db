package com.rs.module.acp.controller.admin.gj.vo.confinement;

import com.rs.module.base.vo.ApproveReqVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


@ApiModel(description = "管理后台 - 实战平台-管教业务-解除禁闭登记审批 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class ConfinementRemoveApproveVO extends ApproveReqVO {
    private static final long serialVersionUID = 1L;

}
