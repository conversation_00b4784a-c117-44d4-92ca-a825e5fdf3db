package com.rs.module.acp.controller.admin.gj.vo.edurehabcourses;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-教育康复活动列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EdurehabActivityListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("活动时间")
    private Date[] activityTime;

    @ApiModelProperty("康复民警身份证号")
    private String rehabPoliceSfzh;

    @ApiModelProperty("康复民警姓名")
    private String rehabPolice;

    @ApiModelProperty("活动主题")
    private String activityTopic;

    @ApiModelProperty("参加人员身份证号")
    private String jgrybm;

    @ApiModelProperty("参加人员姓名")
    private String jgryxm;

    @ApiModelProperty("参加人数")
    private Integer participantCount;

    @ApiModelProperty("活动详情")
    private String activityDetails;

    @ApiModelProperty("附件地址")
    private String attUrl;

}
