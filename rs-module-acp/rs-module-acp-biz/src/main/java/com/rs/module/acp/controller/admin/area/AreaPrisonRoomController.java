package com.rs.module.acp.controller.admin.area;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageWithViolationReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomListReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomPageReqVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomRespVO;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomSaveReqVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-区域监室")
@RestController
@RequestMapping("/base/pm/areaPrisonRoom")
@Validated
public class AreaPrisonRoomController {

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-区域监室")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:create", operateType = LogOperateType.CREATE, title = "创建实战平台-监管管理-区域监室",
    success = "创建实战平台-监管管理-区域监室成功", fail = "创建实战平台-监管管理-区域监室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createAreaPrisonRoom(@Valid @RequestBody AreaPrisonRoomSaveReqVO createReqVO) {
        return success(areaPrisonRoomService.createAreaPrisonRoom(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-区域监室")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:update", operateType = LogOperateType.UPDATE, title = "更新实战平台-监管管理-区域监室",
    success = "更新实战平台-监管管理-区域监室成功", fail = "更新实战平台-监管管理-区域监室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateAreaPrisonRoom(@Valid @RequestBody AreaPrisonRoomSaveReqVO updateReqVO) {
        areaPrisonRoomService.updateAreaPrisonRoom(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-区域监室")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:delete", operateType = LogOperateType.DELETE, title = "删除实战平台-监管管理-区域监室",
    success = "删除实战平台-监管管理-区域监室成功", fail = "删除实战平台-监管管理-区域监室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteAreaPrisonRoom(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           areaPrisonRoomService.deleteAreaPrisonRoom(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-区域监室")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:get", operateType = LogOperateType.QUERY, title = "获取实战平台-监管管理-区域监室", bizNo = "{{#id}}", success = "获取实战平台-监管管理-区域监室成功", fail = "获取实战平台-监管管理-区域监室失败", extraInfo = "{{#id}}")
    public CommonResult<AreaPrisonRoomRespVO> getAreaPrisonRoom(@RequestParam("id") String id) {
        AreaPrisonRoomDO areaPrisonRoom = areaPrisonRoomService.getAreaPrisonRoom(id);
        return success(BeanUtils.toBean(areaPrisonRoom, AreaPrisonRoomRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-区域监室分页")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:page", operateType = LogOperateType.QUERY, title = "获得实战平台-监管管理-区域监室分页",
    success = "获得实战平台-监管管理-区域监室分页成功", fail = "获得实战平台-监管管理-区域监室分页失败", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<AreaPrisonRoomRespVO>> getAreaPrisonRoomPage(@Valid @RequestBody AreaPrisonRoomPageReqVO pageReqVO) {
        PageResult<AreaPrisonRoomDO> pageResult = areaPrisonRoomService.getAreaPrisonRoomPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AreaPrisonRoomRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-区域监室列表")
    @LogRecordAnnotation(bizModule = "acp:areaPrisonRoom:list", operateType = LogOperateType.QUERY, title = "获得实战平台-监管管理-区域监室列表",
    success = "获得实战平台-监管管理-区域监室列表成功", fail = "获得实战平台-监管管理-区域监室列表失败", extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<List<AreaPrisonRoomRespVO>> getAreaPrisonRoomList(@Valid @RequestBody AreaPrisonRoomListReqVO listReqVO) {
    List<AreaPrisonRoomDO> list = areaPrisonRoomService.getAreaPrisonRoomList(listReqVO);
        return success(BeanUtils.toBean(list, AreaPrisonRoomRespVO.class));
    }

    @PostMapping("/withViolation/page")
    @ApiOperation(value = "区域监室分页--带本月违规数量")
    public CommonResult<PageResult<AreaPrisonRoomRespVO>> getRoomWithViolationPage(@Valid @RequestBody AreaPrisonRoomPageWithViolationReqVO pageReqVO) {
        PageResult<AreaPrisonRoomDO> pageResult = areaPrisonRoomService.getRoomWithViolationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AreaPrisonRoomRespVO.class));
    }

}
