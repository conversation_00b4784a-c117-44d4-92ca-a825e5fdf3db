package com.rs.module.acp.controller.admin.gj.vo.performancekss;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-人员表现鉴定表-看守所分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PerformanceKssPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("接收单位")
    private String jsdw;

    @ApiModelProperty("移送因由")
    private String ysyy;

    @ApiModelProperty("其他情况")
    private String qtqk;

    @ApiModelProperty("身体状况")
    private String healthStzk;

    @ApiModelProperty("外伤情况")
    private String healthWsqk;

    @ApiModelProperty("重大疾病")
    private String healthZdjb;

    @ApiModelProperty("精神状况")
    private String healthJszk;

    @ApiModelProperty("重大疾病及出所住院病因")
    private String healthZdjbjcszyyy;

    @ApiModelProperty("所规所纪制度")
    private String performanceSgsjzd;

    @ApiModelProperty("一日生活管理")
    private String performanceYrshgl;

    @ApiModelProperty("自伤自残行为或倾向")
    private String performanceZszcxwhqx;

    @ApiModelProperty("殴打他人行为")
    private String performanceOdtrxw;

    @ApiModelProperty("曾列为重大安全风险情况-一般")
    private String performanceZdaqfxqk1;

    @ApiModelProperty("曾列为重大安全风险情况-三级")
    private String performanceZdaqfxqk2;

    @ApiModelProperty("曾列为重大安全风险情况-二级")
    private String performanceZdaqfxqk3;

    @ApiModelProperty("曾列为重大安全风险情况-一级")
    private String performanceZdaqfxqk4;

    @ApiModelProperty("家属姓名及电话")
    private String familyXmjdh;

    @ApiModelProperty("羁押期间联系家属情况")
    private String familyJyqjlxjsqk;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
