package com.rs.module.acp.controller.admin.gj.vo.civilizedpersonne;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "管理后台 - 实战平台-管教业务-文明个人登记明细列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CivilizedPersonneDetailListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("文明个人登记ID")
    private String civilizedPersonneId;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("room_id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("奖励次数")
    private Integer numberOfReward;

    @ApiModelProperty("违规次数")
    private Integer numberOfViolations;

    @ApiModelProperty("惩罚次数")
    private Integer numberOfPunishment;

    @ApiModelProperty("评选理由")
    private String selectionReason;

    @ApiModelProperty("附件地址")
    private String attUrl;

}
