package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;

@ApiModel(description = "管理后台 - 实战平台-监管管理-我的应用配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CusAppUserSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("系统ID")
    @NotEmpty(message = "系统ID不能为空")
    private String systemId;

    @ApiModelProperty("用户证件号码")
    private String userIdCard;

}
