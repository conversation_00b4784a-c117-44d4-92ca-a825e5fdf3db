package com.rs.module.acp.controller.admin.gj.vo.biometriccomp;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-生物特征比对列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BiometricCompListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("提交生物特征类型")
    private String biometricType;

    @ApiModelProperty("提交时间")
    private Date[] submitTime;

    @ApiModelProperty("提交办案单位类型")
    private String caseUnitType;

    @ApiModelProperty("提交办案单位名称")
    private String caseUnitName;

    @ApiModelProperty("提交方式")
    private String submitMethod;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否比中")
    private String isComp;

    @ApiModelProperty("比中案件编号")
    private String compCaseNo;

    @ApiModelProperty("比中案件类型")
    private String compCaseType;

    @ApiModelProperty("比中处置情况")
    private String disposalSituation;

    @ApiModelProperty("比中登记人身份证号")
    private String compOperatorSfzh;

    @ApiModelProperty("比中登记人姓名")
    private String compOperatorName;

    @ApiModelProperty("comp_operator_time")
    private Date[] compOperatorTime;

    @ApiModelProperty("状态")
    private String status;

}
