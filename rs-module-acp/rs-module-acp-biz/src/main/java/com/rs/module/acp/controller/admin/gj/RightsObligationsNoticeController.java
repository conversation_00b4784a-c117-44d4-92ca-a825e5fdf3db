package com.rs.module.acp.controller.admin.gj;

import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeListReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticePageReqVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeRespVO;
import com.rs.module.acp.controller.admin.gj.vo.rightsobligationsnotice.RightsObligationsNoticeSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.*;
import java.util.*;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.entity.gj.RightsObligationsNoticeDO;
import com.rs.module.acp.service.gj.rightsobligationsnotice.RightsObligationsNoticeService;

@Api(tags = "实战平台-管教业务-权力义务告知书")
@RestController
@RequestMapping("/acp/gj/rightsObligationsNotice")
@Validated
public class RightsObligationsNoticeController {

    @Resource
    private RightsObligationsNoticeService rightsObligationsNoticeService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-管教业务-权力义务告知书")
    public CommonResult<String> createRightsObligationsNotice(@Valid @RequestBody RightsObligationsNoticeSaveReqVO createReqVO) {
        return success(rightsObligationsNoticeService.createRightsObligationsNotice(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-管教业务-权力义务告知书")
    public CommonResult<Boolean> updateRightsObligationsNotice(@Valid @RequestBody RightsObligationsNoticeSaveReqVO updateReqVO) {
        rightsObligationsNoticeService.updateRightsObligationsNotice(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-管教业务-权力义务告知书")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteRightsObligationsNotice(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           rightsObligationsNoticeService.deleteRightsObligationsNotice(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-管教业务-权力义务告知书")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<RightsObligationsNoticeRespVO> getRightsObligationsNotice(@RequestParam("id") String id) {
        RightsObligationsNoticeDO rightsObligationsNotice = rightsObligationsNoticeService.getRightsObligationsNotice(id);
        return success(BeanUtils.toBean(rightsObligationsNotice, RightsObligationsNoticeRespVO.class));
    }

    @GetMapping("/getByJgrybm")
    @ApiOperation(value = "详情")
    @ApiImplicitParam(name = "jgrybm", value = "编号")
    public CommonResult<RightsObligationsNoticeRespVO> getByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        RightsObligationsNoticeDO rightsObligationsNotice = rightsObligationsNoticeService.getByJgrybm(jgrybm);
        return success(BeanUtils.toBean(rightsObligationsNotice, RightsObligationsNoticeRespVO.class));
    }

    @GetMapping("/getBusinessId")
    @ApiOperation(value = "获取businessId")
    @ApiImplicitParam(name = "jgrybm", value = "编号")
    public CommonResult<String> getBusinessId(@RequestParam("jgrybm") String jgrybm) {
        return success(rightsObligationsNoticeService.getParams(jgrybm).getBusinessId());
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-管教业务-权力义务告知书分页")
    public CommonResult<PageResult<RightsObligationsNoticeRespVO>> getRightsObligationsNoticePage(@Valid @RequestBody RightsObligationsNoticePageReqVO pageReqVO) {
        PageResult<RightsObligationsNoticeDO> pageResult = rightsObligationsNoticeService.getRightsObligationsNoticePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RightsObligationsNoticeRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-管教业务-权力义务告知书列表")
    public CommonResult<List<RightsObligationsNoticeRespVO>> getRightsObligationsNoticeList(@Valid @RequestBody RightsObligationsNoticeListReqVO listReqVO) {
        List<RightsObligationsNoticeDO> list = rightsObligationsNoticeService.getRightsObligationsNoticeList(listReqVO);
        return success(BeanUtils.toBean(list, RightsObligationsNoticeRespVO.class));
    }
}
