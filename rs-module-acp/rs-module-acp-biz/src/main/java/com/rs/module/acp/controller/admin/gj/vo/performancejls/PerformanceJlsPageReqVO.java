package com.rs.module.acp.controller.admin.gj.vo.performancejls;

import io.swagger.annotations.ApiModel;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.*;
import com.rs.framework.common.pojo.PageParam;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-人员表现鉴定表-拘留所分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PerformanceJlsPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("所规所纪制度")
    private String performanceSgsjzd;

    @ApiModelProperty("一日生活管理")
    private String performanceYrshgl;

    @ApiModelProperty("自杀行为或倾向")
    private String performanceZsxwhqx;

    @ApiModelProperty("暴力行为或倾向")
    private String performanceBlxwhqx;

    @ApiModelProperty("列为严管人员情况")
    private String performanceLwygryqk;

    @ApiModelProperty("参加所内教育情况")
    private String performanceCjsnjyqk;

    @ApiModelProperty("认错悔过情况")
    private String performanceRchgqk;

    @ApiModelProperty("其他情况")
    private String performanceQtqk;

    @ApiModelProperty("需要说明的情况")
    private String needSituations;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date[] approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
