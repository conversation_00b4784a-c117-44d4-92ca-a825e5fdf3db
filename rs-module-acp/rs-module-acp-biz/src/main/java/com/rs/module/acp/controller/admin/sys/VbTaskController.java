package com.rs.module.acp.controller.admin.sys;

import static com.rs.framework.common.pojo.CommonResult.success;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.sys.vo.VbTaskListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbTaskPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbTaskRespVO;
import com.rs.module.acp.controller.admin.sys.vo.VbTaskSaveReqVO;
import com.rs.module.acp.entity.sys.VbTaskDO;
import com.rs.module.acp.service.sys.VbTaskService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@Api(tags = "实战平台-语音播报-待播报任务")
@RestController
@RequestMapping("/acp/sys/vbTask")
@Validated
public class VbTaskController {

    @Resource
    private VbTaskService vbTaskService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-语音播报-待播报任务")
    public CommonResult<String> createVbTask(@Valid @RequestBody VbTaskSaveReqVO createReqVO) {
        return success(vbTaskService.createVbTask(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-语音播报-待播报任务")
    public CommonResult<Boolean> updateVbTask(@Valid @RequestBody VbTaskSaveReqVO updateReqVO) {
        vbTaskService.updateVbTask(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-语音播报-待播报任务")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteVbTask(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           vbTaskService.deleteVbTask(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-语音播报-待播报任务")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<VbTaskRespVO> getVbTask(@RequestParam("id") String id) {
        VbTaskDO vbTask = vbTaskService.getVbTask(id);
        return success(BeanUtils.toBean(vbTask, VbTaskRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-语音播报-待播报任务分页")
    public CommonResult<PageResult<VbTaskRespVO>> getVbTaskPage(@Valid @RequestBody VbTaskPageReqVO pageReqVO) {
        PageResult<VbTaskDO> pageResult = vbTaskService.getVbTaskPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VbTaskRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-语音播报-待播报任务列表")
    public CommonResult<List<VbTaskRespVO>> getVbTaskList(@Valid @RequestBody VbTaskListReqVO listReqVO) {
        List<VbTaskDO> list = vbTaskService.getVbTaskList(listReqVO);
        return success(BeanUtils.toBean(list, VbTaskRespVO.class));
    }
}
