package com.rs.module.acp.controller.admin.gj.vo.contraband;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-违禁品登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ContrabandListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("数据来源（字典：ZD_DATA_SOURCES）")
    private String dataSources;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("检查时间")
    private Date[] checkTime;

    @ApiModelProperty("违禁品类别")
    private String contrabandCategory;

    @ApiModelProperty("违禁品照片")
    private String contrabandImgPath;

    @ApiModelProperty("处理情况")
    private String handlingSituatio;

}
