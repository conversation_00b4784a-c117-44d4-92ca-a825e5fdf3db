package com.rs.module.acp.controller.admin.pi.vo.fixedscreenmonitor;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-定屏监控 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class FixedScreenMonitorRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("巡控室ID")
    private String areaId;
    @ApiModelProperty("巡控室名称")
    private String areaName;
    @ApiModelProperty("上墙人员信息")
    private String onScreenPersonInfo;
    @ApiModelProperty("上墙时间")
    private Date onScreenTime;
    @ApiModelProperty("下墙时间")
    private Date offScreenTime;
    @ApiModelProperty("经办人身份证号")
    private String operatorSfzh;
    @ApiModelProperty("经办人姓名")
    private String operatorXm;
    @ApiModelProperty("经办时间")
    private Date operateTime;
    @ApiModelProperty("状态")
    private String status;
}
