package com.rs.module.acp.controller.admin.wb.vo;

import lombok.*;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-对外开放登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VisitorRegListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("来访时间")
    private Date[] visitTime;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("来访事由")
    private String visitReason;

    @ApiModelProperty("来访人员类型")
    private String visitorType;

    @ApiModelProperty("参观区域")
    private String visitArea;

    @ApiModelProperty("备注")
    private String remark;

}
