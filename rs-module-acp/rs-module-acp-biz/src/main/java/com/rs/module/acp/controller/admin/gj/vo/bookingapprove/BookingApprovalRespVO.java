package com.rs.module.acp.controller.admin.gj.vo.bookingapprove;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;
    import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-管教业务-预约审核管理 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BookingApprovalRespVO extends BaseVO implements TransPojo{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("监室id")
    private String roomId;

    @ApiModelProperty("监室名称")
    private String roomName;

    @ApiModelProperty("监管人员编码")
    private String jgrybm;

    @ApiModelProperty("监管人员姓名")
    private String jgryxm;

    @ApiModelProperty("预约类别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_YYSHGL_YYLX")
    private String bookingCategory;

    @ApiModelProperty("服务类别")
    @Trans(type = TransType.DICTIONARY,key = "ZD_GJ_YYSHGL_FWLX")
    private String serviceCategory;

    @ApiModelProperty("申请时间")
    private Date applicationTime;

    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY,key = "ZD_SP_STATUS")
    private String status;

    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;

    @ApiModelProperty("审批人姓名")
    private String approverXm;

    @ApiModelProperty("审批时间")
    private Date approverTime;

    @ApiModelProperty("审批结果")
    private String approvalResult;

    @ApiModelProperty("审核意见")
    private String approvalComments;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("入所时间")
    private Date rssj;

    @ApiModelProperty("审批结果")
    private String approvalResultName;

}
