package com.rs.module.acp.controller.admin.pi.vo.prisonevent;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@ApiModel(description = "管理后台 - 实战平台-巡视管控-人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PrisonEventPersonSaveVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("人员名称")
    private String name;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("照片")
    private String zpUrl;

    @ApiModelProperty("人员类型")
    private String personType;
}
