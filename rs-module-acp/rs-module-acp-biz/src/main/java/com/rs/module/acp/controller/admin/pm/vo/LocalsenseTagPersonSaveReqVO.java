package com.rs.module.acp.controller.admin.pm.vo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-监管管理-定位标签与人员绑定新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class LocalsenseTagPersonSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("绑定来源 ，01：入所，02：智能腕带")
    @NotEmpty(message = "绑定来源不能为空")
    private String bindSource;

    @ApiModelProperty("标签id")
    @NotEmpty(message = "标签id不能为空")
    private String tagId;

    @ApiModelProperty("人员标签类型，01：被监管人员，02：民警")
    @NotEmpty(message = "人员标签类型，01：被监管人员，02：民警不能为空")
    private String personType;

    @ApiModelProperty("绑定人员ID")
    @NotEmpty(message = "绑定人员ID不能为空")
    private String bindPersonId;

    @ApiModelProperty("bind_person_name")
    @NotEmpty(message = "bind_person_name不能为空")
    private String bindPersonName;

    @ApiModelProperty("绑定时间")
    @NotNull(message = "绑定时间不能为空")
    private Date bindTime;

    @ApiModelProperty("解绑时间")
    private Date unbindTime;

    @ApiModelProperty("解绑原因")
    private String unbindReason;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

}
